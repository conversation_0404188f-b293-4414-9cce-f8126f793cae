#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化决策树预测器使用示例
目标：实现80%以上的命中率
"""

import sys
import os

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

import pandas as pd
from typing import List
from datetime import datetime

# 使用绝对导入
from src.framework.backtest_framework import BacktestFramework
from src.framework.data_models import BacktestConfig
from src.framework.result_display import ResultDisplayer
from src.decision_tree.optimized_decision_tree_predictor import OptimizedDecisionTreePredictor
from src.utils.logger import get_logger


def main():
    """主函数"""
    logger = get_logger("OptimizedDecisionTreeExample")
    logger.info("开始优化决策树预测器测试")
    
    try:
        # 1. 加载数据
        data_path = "dlt_data.csv"
        if not os.path.exists(data_path):
            logger.error(f"数据文件不存在: {data_path}")
            return
        
        data = pd.read_csv(data_path, encoding='utf-8')
        logger.info(f"加载数据成功，共{len(data)}期")
        
        # 2. 创建优化预测器
        predictor = OptimizedDecisionTreePredictor(
            max_depth=25,           # 更深的树
            min_samples_split=2,    # 更细的分割
            min_samples_leaf=1,     # 更小的叶节点
            n_estimators=15,        # 更多的集成模型
            random_state=42
        )
        
        # 3. 配置回测参数
        config = BacktestConfig(
            num_periods=min(200, len(data) - 50),  # 使用最近200期进行回测
            min_train_periods=50,                  # 最少需要50期训练数据
            display_periods=10,                    # 显示最后10期结果
            enable_detailed_output=True,
            enable_statistics=True,
            reverse_display=True
        )
        
        logger.info(f"回测配置: 回测{config.num_periods}期，最少训练{config.min_train_periods}期")
        
        # 4. 创建回测框架
        framework = BacktestFramework(data)
        
        # 5. 运行回测
        logger.info("开始运行优化回测...")
        results = framework.run_backtest(predictor, config)
        
        # 6. 显示结果
        if results:
            displayer = ResultDisplayer()
            displayer.display_backtest_result(results)
            
            # 检查是否达到80%目标
            if results.statistics:
                stats = results.statistics
                logger.info("\n=== 命中率检查 ===")
                logger.info(f"奇偶比命中率: {stats.red_odd_even_rate:.2%}")
                logger.info(f"红球大小比命中率: {stats.red_size_rate:.2%}")
                logger.info(f"蓝球大小命中率: {stats.blue_size_rate:.2%}")
                
                # 检查是否达到目标
                target_rate = 0.80
                success_count = 0
                
                if stats.red_odd_even_rate >= target_rate:
                    logger.info(f"✓ 奇偶比命中率达标: {stats.red_odd_even_rate:.2%} >= {target_rate:.0%}")
                    success_count += 1
                else:
                    logger.warning(f"✗ 奇偶比命中率未达标: {stats.red_odd_even_rate:.2%} < {target_rate:.0%}")
                
                if stats.red_size_rate >= target_rate:
                    logger.info(f"✓ 红球大小比命中率达标: {stats.red_size_rate:.2%} >= {target_rate:.0%}")
                    success_count += 1
                else:
                    logger.warning(f"✗ 红球大小比命中率未达标: {stats.red_size_rate:.2%} < {target_rate:.0%}")
                
                if stats.blue_size_rate >= target_rate:
                    logger.info(f"✓ 蓝球大小命中率达标: {stats.blue_size_rate:.2%} >= {target_rate:.0%}")
                    success_count += 1
                else:
                    logger.warning(f"✗ 蓝球大小命中率未达标: {stats.blue_size_rate:.2%} < {target_rate:.0%}")
                
                logger.info(f"\n总体达标情况: {success_count}/3 项达到80%目标")
                
                # 7. 保存模型（如果效果好）
                if success_count >= 2:  # 至少2项达标就保存
                    model_path = "models/optimized_decision_tree_model.pkl"
                    os.makedirs(os.path.dirname(model_path), exist_ok=True)
                    predictor.save_models(model_path)
                    logger.info(f"模型已保存到: {model_path}")
                    
                    # 保存详细报告
                    report_path = "results/optimization_report.txt"
                    os.makedirs(os.path.dirname(report_path), exist_ok=True)
                    save_optimization_report(report_path, results, predictor, success_count)
                    logger.info(f"优化报告已保存到: {report_path}")
                else:
                    logger.warning("模型效果未达到预期，建议进一步优化")
                    suggest_improvements(stats)
            
        else:
            logger.error("回测失败，未获得结果")
    
    except Exception as e:
        logger.error(f"优化决策树测试过程中出错: {e}")
        import traceback
        logger.error(traceback.format_exc())
    
    logger.info("优化决策树预测器测试完成")


def save_optimization_report(filepath: str, results, predictor, success_count: int):
    """保存优化报告"""
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write("优化决策树预测器性能报告\n")
        f.write("=" * 50 + "\n\n")
        
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"预测器: {predictor.get_predictor_name()}\n")
        f.write(f"版本: {predictor.get_predictor_version()}\n\n")
        
        f.write("模型参数:\n")
        f.write(f"- 最大深度: {predictor.max_depth}\n")
        f.write(f"- 最小分割样本: {predictor.min_samples_split}\n")
        f.write(f"- 最小叶节点样本: {predictor.min_samples_leaf}\n")
        f.write(f"- 集成模型数量: {predictor.n_estimators}\n")
        f.write(f"- 随机种子: {predictor.random_state}\n\n")
        
        if results.statistics:
            stats = results.statistics
            f.write("性能指标:\n")
            f.write(f"- 总预测期数: {stats.total_periods}\n")
            f.write(f"- 成功期数: {stats.successful_periods}\n")
            f.write(f"- 整体成功率: {stats.overall_success_rate:.2%}\n\n")
            
            f.write("各项命中率:\n")
            f.write(f"- 奇偶比命中率: {stats.red_odd_even_rate:.2%}\n")
            f.write(f"- 红球大小比命中率: {stats.red_size_rate:.2%}\n")
            f.write(f"- 蓝球大小命中率: {stats.blue_size_rate:.2%}\n\n")
            
            f.write(f"80%目标达成情况: {success_count}/3 项达标\n\n")
            
            if hasattr(stats, 'hit_rates'):
                f.write("详细命中统计:\n")
                for key, value in stats.hit_rates.items():
                    f.write(f"- {key}: {value:.2%}\n")
        
        f.write("\n优化建议:\n")
        if success_count == 3:
            f.write("- 所有指标均达到80%目标，模型表现优秀\n")
            f.write("- 建议在实际应用中继续监控性能\n")
        elif success_count >= 2:
            f.write("- 大部分指标达到目标，模型表现良好\n")
            f.write("- 可以考虑针对未达标指标进行进一步优化\n")
        else:
            f.write("- 需要进一步优化模型参数和特征工程\n")
            f.write("- 建议增加训练数据或调整算法策略\n")


def suggest_improvements(stats):
    """建议改进措施"""
    logger = get_logger("OptimizationSuggestions")
    
    logger.info("\n=== 优化建议 ===")
    
    if stats.red_odd_even_rate < 0.80:
        logger.info("奇偶比预测改进建议:")
        logger.info("- 增加奇偶比历史模式分析")
        logger.info("- 考虑连续奇偶比出现的规律")
        logger.info("- 增加更多时间窗口的统计特征")
    
    if stats.red_size_rate < 0.80:
        logger.info("红球大小比预测改进建议:")
        logger.info("- 分析大小号码的分布规律")
        logger.info("- 考虑号码热度和冷度")
        logger.info("- 增加区间分析特征")
    
    if stats.blue_size_rate < 0.80:
        logger.info("蓝球大小预测改进建议:")
        logger.info("- 分析蓝球的周期性规律")
        logger.info("- 考虑蓝球与红球的关联性")
        logger.info("- 增加蓝球历史频率分析")
    
    logger.info("\n通用优化建议:")
    logger.info("- 增加更多历史期数的训练数据")
    logger.info("- 尝试不同的集成学习策略")
    logger.info("- 考虑使用深度学习方法")
    logger.info("- 增加特征选择和降维技术")


if __name__ == "__main__":
    main()