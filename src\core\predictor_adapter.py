"""预测器适配器
提供旧预测器到新接口的适配功能
"""

from typing import Dict, Any, List
import pandas as pd
from abc import ABC

from .interfaces import (
    IStandardPredictor,
    PredictionResult as StandardPredictionResult,
    ValidationResult,
    PredictionType,
)
from .base import BallType, PredictionResult as LegacyPredictionResult
from .exceptions import ValidationException, PredictionException


class LegacyPredictorAdapter(IStandardPredictor):
    """旧预测器适配器

    将实现IPredictor接口的旧预测器适配到IStandardPredictor接口
    """

    def __init__(self, legacy_predictor, name: str = None, version: str = "1.0"):
        """初始化适配器

        Args:
            legacy_predictor: 旧的预测器实例
            name: 适配器名称
            version: 版本号
        """
        self.legacy_predictor = legacy_predictor
        self.name = name or f"Adapted_{legacy_predictor.__class__.__name__}"
        self.version = version
        self.is_trained = getattr(
            legacy_predictor, "is_trained", True
        )  # 假设旧预测器已训练

    def get_prediction_type(self) -> PredictionType:
        """获取预测类型"""
        # 尝试从旧预测器获取，否则返回默认值
        if hasattr(self.legacy_predictor, "prediction_type"):
            return self.legacy_predictor.prediction_type
        return PredictionType.NUMBERS

    def predict(
        self, data: pd.DataFrame, target_index: int, **kwargs
    ) -> StandardPredictionResult:
        """执行预测"""
        try:
            # 调用旧预测器的predict方法
            legacy_result = self.legacy_predictor.predict(data, target_index, **kwargs)

            # 转换结果格式
            return self._convert_legacy_result(legacy_result, **kwargs)

        except Exception as e:
            raise PredictionException(f"适配器预测失败: {e}")

    def predict_batch(
        self, data: pd.DataFrame, target_indices: List[int], **kwargs
    ) -> List[StandardPredictionResult]:
        """批量预测"""
        results = []
        for target_index in target_indices:
            try:
                result = self.predict(data, target_index, **kwargs)
                results.append(result)
            except Exception as e:
                # 创建错误结果
                error_result = StandardPredictionResult(
                    prediction_type=self.get_prediction_type(),
                    ball_type=kwargs.get("ball_type", BallType.RED),
                    value=None,
                    confidence=0.0,
                    metadata={"error": str(e)},
                )
                results.append(error_result)
        return results

    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        info = {
            "name": self.name,
            "version": self.version,
            "legacy_class": self.legacy_predictor.__class__.__name__,
            "is_adapted": True,
            "is_trained": self.is_trained,
        }

        # 尝试获取旧预测器的配置信息
        if hasattr(self.legacy_predictor, "config"):
            info["legacy_config"] = self.legacy_predictor.config

        return info

    def validate_input(self, data: pd.DataFrame, target_index: int) -> ValidationResult:
        """验证输入数据"""
        errors = []
        warnings = []

        # 基础验证
        if data is None or data.empty:
            errors.append("输入数据为空")

        if target_index < 0:
            errors.append("目标索引不能为负数")

        if target_index >= len(data):
            errors.append(f"目标索引 {target_index} 超出数据范围 {len(data)}")

        # 检查旧预测器是否有自己的验证方法
        if hasattr(self.legacy_predictor, "validate_input"):
            try:
                legacy_validation = self.legacy_predictor.validate_input(
                    data, target_index
                )
                if hasattr(legacy_validation, "errors"):
                    errors.extend(legacy_validation.errors)
                if hasattr(legacy_validation, "warnings"):
                    warnings.extend(legacy_validation.warnings)
            except Exception as e:
                warnings.append(f"旧预测器验证失败: {e}")

        return ValidationResult(
            is_valid=len(errors) == 0, errors=errors, warnings=warnings
        )

    def train(self, data: pd.DataFrame, **kwargs) -> bool:
        """训练模型"""
        if hasattr(self.legacy_predictor, "train"):
            try:
                result = self.legacy_predictor.train(data, **kwargs)
                self.is_trained = True
                return result
            except Exception as e:
                raise PredictionException(f"适配器训练失败: {e}")
        else:
            # 旧预测器没有训练方法，假设不需要训练
            self.is_trained = True
            return True

    def get_confidence_threshold(self) -> float:
        """获取置信度阈值"""
        if hasattr(self.legacy_predictor, "confidence_threshold"):
            return self.legacy_predictor.confidence_threshold
        return 0.5

    def set_confidence_threshold(self, threshold: float) -> None:
        """设置置信度阈值"""
        if hasattr(self.legacy_predictor, "confidence_threshold"):
            self.legacy_predictor.confidence_threshold = threshold

    def supports_incremental_training(self) -> bool:
        """是否支持增量训练"""
        if hasattr(self.legacy_predictor, "supports_incremental_training"):
            return self.legacy_predictor.supports_incremental_training()
        return False

    def _convert_legacy_result(
        self, legacy_result, **kwargs
    ) -> StandardPredictionResult:
        """转换旧结果格式到新格式"""
        # 处理不同类型的旧结果
        if isinstance(legacy_result, dict):
            return self._convert_dict_result(legacy_result, **kwargs)
        elif hasattr(legacy_result, "predicted_value"):
            return self._convert_prediction_result(legacy_result, **kwargs)
        else:
            # 简单值结果
            return StandardPredictionResult(
                prediction_type=self.get_prediction_type(),
                ball_type=kwargs.get("ball_type", BallType.RED),
                value=legacy_result,
                confidence=kwargs.get("confidence", 0.5),
                metadata={"source": "legacy_adapter"},
            )

    def _convert_dict_result(
        self, result_dict: Dict, **kwargs
    ) -> StandardPredictionResult:
        """转换字典格式的结果"""
        return StandardPredictionResult(
            prediction_type=self.get_prediction_type(),
            ball_type=kwargs.get("ball_type", BallType.RED),
            value=result_dict.get("value", result_dict.get("predicted_value")),
            confidence=result_dict.get("confidence", 0.5),
            metadata={"source": "legacy_adapter", "original_result": result_dict},
        )

    def _convert_prediction_result(
        self, legacy_result, **kwargs
    ) -> StandardPredictionResult:
        """转换PredictionResult格式的结果"""
        return StandardPredictionResult(
            prediction_type=self.get_prediction_type(),
            ball_type=kwargs.get("ball_type", BallType.RED),
            value=legacy_result.predicted_value,
            confidence=getattr(legacy_result, "confidence", 0.5),
            metadata={
                "source": "legacy_adapter",
                "original_metadata": getattr(legacy_result, "metadata", {}),
            },
        )


def create_legacy_adapter(
    legacy_predictor, name: str = None, version: str = "1.0"
) -> LegacyPredictorAdapter:
    """创建旧预测器适配器的工厂函数

    Args:
        legacy_predictor: 旧的预测器实例
        name: 适配器名称
        version: 版本号

    Returns:
        LegacyPredictorAdapter: 适配器实例
    """
    return LegacyPredictorAdapter(legacy_predictor, name, version)


__all__ = ["LegacyPredictorAdapter", "create_legacy_adapter"]
