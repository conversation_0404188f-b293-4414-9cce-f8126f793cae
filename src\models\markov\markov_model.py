"""
马尔科夫链模型模块
实现状态转移概率矩阵建模与预测
"""

import numpy as np
from typing import List, Dict, Tuple
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.utils.utils import get_all_red_states, get_all_blue_states


class MarkovModel:
    """增强型马尔科夫链模型"""

    def __init__(self, feature_type: str, order: int = 2):
        """
        初始化马尔科夫模型

        Args:
            feature_type: 特征类型 ('red' 或 'blue')
            order: 马尔科夫链阶数，默认为2阶
        """
        self.feature_type = feature_type
        self.order = order
        if feature_type == 'red':
            self.states = get_all_red_states()
        else:
            self.states = get_all_blue_states()

        self.state_to_index = {state: i for i, state in enumerate(self.states)}
        self.index_to_state = {i: state for i, state in enumerate(self.states)}

        # 多阶马尔科夫链需要更复杂的状态空间
        if order == 1:
            self.transition_matrix = np.zeros((len(self.states), len(self.states)))
            self.transition_dict = None
        else:
            # 2阶马尔科夫链：状态对 -> 下一状态
            self.transition_matrix = None
            self.transition_dict = {}

        self.is_trained = False
    
    def train(self, state_sequence: List[str]) -> None:
        """
        训练增强型马尔科夫模型

        Args:
            state_sequence: 状态序列（从新到旧）
        """
        if len(state_sequence) < self.order + 1:
            return

        if self.order == 1:
            self._train_first_order(state_sequence)
        else:
            self._train_higher_order(state_sequence)

        self.is_trained = True

    def _train_first_order(self, state_sequence: List[str]) -> None:
        """训练一阶马尔科夫模型"""
        # 重置转移矩阵
        self.transition_matrix = np.zeros((len(self.states), len(self.states)))

        # 添加时间衰减权重（越近的数据权重越大）
        total_transitions = len(state_sequence) - 1

        # 统计状态转移次数（注意：序列是从新到旧，所以要反向处理）
        for i in range(len(state_sequence) - 1, 0, -1):
            current_state = state_sequence[i]
            next_state = state_sequence[i - 1]

            if current_state in self.state_to_index and next_state in self.state_to_index:
                current_idx = self.state_to_index[current_state]
                next_idx = self.state_to_index[next_state]

                # 时间衰减权重：越新的数据权重越大
                time_weight = np.exp(-0.01 * (total_transitions - (len(state_sequence) - 1 - i)))
                self.transition_matrix[current_idx][next_idx] += time_weight

        # 归一化转移概率矩阵
        for i in range(len(self.states)):
            row_sum = np.sum(self.transition_matrix[i])
            if row_sum > 0:
                self.transition_matrix[i] = self.transition_matrix[i] / row_sum
            else:
                # 如果某个状态没有转移记录，使用均匀分布
                self.transition_matrix[i] = np.ones(len(self.states)) / len(self.states)

    def _train_higher_order(self, state_sequence: List[str]) -> None:
        """训练高阶马尔科夫模型"""
        self.transition_dict = {}
        total_transitions = len(state_sequence) - self.order

        # 统计高阶状态转移
        for i in range(len(state_sequence) - self.order, 0, -1):
            # 构建状态序列
            state_tuple = tuple(state_sequence[i:i + self.order])
            next_state = state_sequence[i - 1]

            if state_tuple not in self.transition_dict:
                self.transition_dict[state_tuple] = {}

            if next_state not in self.transition_dict[state_tuple]:
                self.transition_dict[state_tuple][next_state] = 0

            # 时间衰减权重
            time_weight = np.exp(-0.01 * (total_transitions - (len(state_sequence) - self.order - i)))
            self.transition_dict[state_tuple][next_state] += time_weight

        # 归一化概率
        for state_tuple in self.transition_dict:
            total_weight = sum(self.transition_dict[state_tuple].values())
            if total_weight > 0:
                for next_state in self.transition_dict[state_tuple]:
                    self.transition_dict[state_tuple][next_state] /= total_weight
    
    def predict_next_state(self, recent_states: List[str]) -> Tuple[str, float]:
        """
        预测下一个状态（支持多阶）

        Args:
            recent_states: 最近的状态序列（从旧到新）

        Returns:
            Tuple[str, float]: (预测状态, 概率)
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练")

        if self.order == 1:
            return self._predict_first_order(recent_states[-1] if recent_states else "")
        else:
            return self._predict_higher_order(recent_states)

    def _predict_first_order(self, current_state: str) -> Tuple[str, float]:
        """一阶马尔科夫预测"""
        if current_state not in self.state_to_index:
            return self._get_most_frequent_state()

        current_idx = self.state_to_index[current_state]
        probabilities = self.transition_matrix[current_idx]

        # 选择概率最大的状态（确定性输出）
        max_prob_idx = np.argmax(probabilities)
        predicted_state = self.index_to_state[max_prob_idx]
        probability = probabilities[max_prob_idx]

        return predicted_state, probability

    def _predict_higher_order(self, recent_states: List[str]) -> Tuple[str, float]:
        """高阶马尔科夫预测"""
        if len(recent_states) < self.order:
            # 如果历史状态不足，降级为一阶预测
            return self._predict_first_order(recent_states[-1] if recent_states else "")

        # 构建状态元组
        state_tuple = tuple(recent_states[-self.order:])

        if state_tuple in self.transition_dict:
            # 找到概率最大的下一状态
            next_states = self.transition_dict[state_tuple]
            best_state = max(next_states.keys(), key=lambda x: next_states[x])
            probability = next_states[best_state]
            return best_state, probability
        else:
            # 如果没有找到匹配的状态序列，尝试降阶
            if self.order > 1:
                return self._predict_higher_order(recent_states[1:])
            else:
                return self._get_most_frequent_state()
    
    def get_state_probabilities(self, recent_states: List[str]) -> Dict[str, float]:
        """
        获取从当前状态转移到各个状态的概率

        Args:
            recent_states: 最近的状态序列

        Returns:
            Dict[str, float]: 状态概率字典
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练")

        if self.order == 1:
            return self._get_first_order_probabilities(recent_states[-1] if recent_states else "")
        else:
            return self._get_higher_order_probabilities(recent_states)

    def _get_first_order_probabilities(self, current_state: str) -> Dict[str, float]:
        """获取一阶马尔科夫概率 - 增强极端状态预测"""
        if current_state not in self.state_to_index:
            uniform_prob = 1.0 / len(self.states)
            return {state: uniform_prob for state in self.states}

        current_idx = self.state_to_index[current_state]
        probabilities = self.transition_matrix[current_idx].copy()

        # 增强极端状态的预测能力
        # 识别极端状态（如4:1, 1:4等）并给予额外权重
        enhanced_probs = {}
        for i, prob in enumerate(probabilities):
            state = self.index_to_state[i]
            enhanced_prob = prob

            # 检查是否为极端状态
            if self._is_extreme_state(state):
                # 给极端状态增加20%的权重提升
                enhanced_prob = min(prob * 1.2, 1.0)

            enhanced_probs[state] = enhanced_prob

        # 重新归一化概率
        total_prob = sum(enhanced_probs.values())
        if total_prob > 0:
            for state in enhanced_probs:
                enhanced_probs[state] /= total_prob

        return enhanced_probs

    def _is_extreme_state(self, state: str) -> bool:
        """
        判断是否为极端状态

        Args:
            state: 状态字符串（如"4:1", "1:4", "0:5"等）

        Returns:
            bool: 是否为极端状态
        """
        if ':' not in state:
            return False

        try:
            parts = state.split(':')
            if len(parts) != 2:
                return False

            left, right = int(parts[0]), int(parts[1])
            total = left + right

            # 定义极端状态：比例差异大于等于3
            # 例如：4:1, 1:4, 5:0, 0:5等
            if total >= 4:  # 至少4个元素才考虑极端状态
                diff = abs(left - right)
                return diff >= 3

            return False
        except (ValueError, IndexError):
            return False

    def _get_higher_order_probabilities(self, recent_states: List[str]) -> Dict[str, float]:
        """获取高阶马尔科夫概率 - 增强极端状态预测"""
        if len(recent_states) < self.order:
            return self._get_first_order_probabilities(recent_states[-1] if recent_states else "")

        state_tuple = tuple(recent_states[-self.order:])

        if state_tuple in self.transition_dict:
            # 补全所有可能的状态
            result = {state: 0.0 for state in self.states}
            for state, prob in self.transition_dict[state_tuple].items():
                if state in result:
                    # 增强极端状态的预测能力
                    enhanced_prob = prob
                    if self._is_extreme_state(state):
                        enhanced_prob = min(prob * 1.2, 1.0)
                    result[state] = enhanced_prob

            # 重新归一化概率
            total_prob = sum(result.values())
            if total_prob > 0:
                for state in result:
                    result[state] /= total_prob

            return result
        else:
            # 降阶处理
            if self.order > 1:
                return self._get_higher_order_probabilities(recent_states[1:])
            else:
                uniform_prob = 1.0 / len(self.states)
                return {state: uniform_prob for state in self.states}
    
    def _get_most_frequent_state(self) -> Tuple[str, float]:
        """
        获取最常见的状态（当无法预测时的备选方案）
        
        Returns:
            Tuple[str, float]: (状态, 概率)
        """
        # 计算稳态分布
        row_sums = np.sum(self.transition_matrix, axis=1)
        if np.sum(row_sums) > 0:
            steady_state = np.sum(self.transition_matrix, axis=0)
            steady_state = steady_state / np.sum(steady_state)
            max_idx = np.argmax(steady_state)
            return self.index_to_state[max_idx], steady_state[max_idx]
        else:
            # 如果没有训练数据，返回第一个状态
            return self.states[0], 1.0 / len(self.states)
    
    def get_transition_matrix(self) -> np.ndarray:
        """
        获取转移概率矩阵
        
        Returns:
            np.ndarray: 转移概率矩阵
        """
        return self.transition_matrix.copy()
    
    def get_states(self) -> List[str]:
        """
        获取所有状态
        
        Returns:
            List[str]: 状态列表
        """
        return self.states.copy()
    
    def predict_multi_step(self, current_state: str, steps: int) -> List[Tuple[str, float]]:
        """
        多步预测
        
        Args:
            current_state: 当前状态
            steps: 预测步数
            
        Returns:
            List[Tuple[str, float]]: 每一步的预测结果
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        predictions = []
        state = current_state
        
        for _ in range(steps):
            next_state, prob = self.predict_next_state(state)
            predictions.append((next_state, prob))
            state = next_state
        
        return predictions
    
    def calculate_model_confidence(self) -> float:
        """
        计算模型置信度（基于转移概率的确定性）

        Returns:
            float: 置信度分数 (0-1)
        """
        if not self.is_trained:
            return 0.0

        if self.order == 1:
            # 一阶马尔科夫：计算每行的最大概率的平均值
            max_probs = np.max(self.transition_matrix, axis=1)
            return np.mean(max_probs)
        else:
            # 高阶马尔科夫：计算每个状态序列的最大转移概率的平均值
            if not self.transition_dict:
                return 0.5

            max_probs = []
            for state_tuple in self.transition_dict:
                if self.transition_dict[state_tuple]:
                    max_prob = max(self.transition_dict[state_tuple].values())
                    max_probs.append(max_prob)

            return np.mean(max_probs) if max_probs else 0.5
