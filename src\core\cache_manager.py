#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一缓存管理系统

提供标准化的缓存接口，支持多种缓存后端、过期策略、序列化方式和性能监控。

主要功能：
- 多种缓存后端（内存、Redis、文件等）
- 灵活的过期策略（TTL、LRU、LFU等）
- 多种序列化方式（JSON、Pickle、自定义）
- 缓存统计和性能监控
- 批量操作和事务支持
- 缓存预热和清理

作者: Assistant
创建时间: 2024
"""

import json
import pickle
import time
import threading
import hashlib
from abc import ABC, abstractmethod
from collections import OrderedDict
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Callable, Tuple
from functools import wraps
import logging


class CacheBackend(Enum):
    """缓存后端类型"""

    MEMORY = "memory"
    REDIS = "redis"
    FILE = "file"
    MEMCACHED = "memcached"
    HYBRID = "hybrid"


class EvictionPolicy(Enum):
    """缓存淘汰策略"""

    LRU = "lru"  # Least Recently Used
    LFU = "lfu"  # Least Frequently Used
    FIFO = "fifo"  # First In First Out
    TTL = "ttl"  # Time To Live
    RANDOM = "random"


class SerializationType(Enum):
    """序列化类型"""

    JSON = "json"
    PICKLE = "pickle"
    STRING = "string"
    CUSTOM = "custom"


class CacheOperation(Enum):
    """缓存操作类型"""

    GET = "get"
    SET = "set"
    DELETE = "delete"
    CLEAR = "clear"
    EXISTS = "exists"


@dataclass
class CacheConfig:
    """缓存配置"""

    backend: CacheBackend = CacheBackend.MEMORY
    max_size: int = 1000
    default_ttl: int = 3600  # 默认过期时间（秒）
    eviction_policy: EvictionPolicy = EvictionPolicy.LRU
    serialization: SerializationType = SerializationType.JSON
    enable_stats: bool = True
    enable_compression: bool = False
    compression_threshold: int = 1024
    redis_url: Optional[str] = None
    file_cache_dir: Optional[str] = None
    namespace: str = "default"


@dataclass
class CacheStats:
    """缓存统计信息"""

    hits: int = 0
    misses: int = 0
    sets: int = 0
    deletes: int = 0
    evictions: int = 0
    size: int = 0
    memory_usage: int = 0
    hit_rate: float = 0.0
    avg_access_time: float = 0.0
    last_reset: float = field(default_factory=time.time)

    def calculate_hit_rate(self) -> float:
        """计算命中率"""
        total = self.hits + self.misses
        if total == 0:
            return 0.0
        self.hit_rate = self.hits / total
        return self.hit_rate


@dataclass
class CacheEntry:
    """缓存条目"""

    key: str
    value: Any
    created_at: float
    accessed_at: float
    access_count: int = 0
    ttl: Optional[int] = None
    size: int = 0

    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.ttl is None:
            return False
        return time.time() - self.created_at > self.ttl

    def touch(self) -> None:
        """更新访问时间和次数"""
        self.accessed_at = time.time()
        self.access_count += 1


class CacheSerializer(ABC):
    """缓存序列化器抽象基类"""

    @abstractmethod
    def serialize(self, data: Any) -> bytes:
        """序列化数据"""
        pass

    @abstractmethod
    def deserialize(self, data: bytes) -> Any:
        """反序列化数据"""
        pass


class JSONSerializer(CacheSerializer):
    """JSON序列化器"""

    def serialize(self, data: Any) -> bytes:
        return json.dumps(data, ensure_ascii=False).encode("utf-8")

    def deserialize(self, data: bytes) -> Any:
        return json.loads(data.decode("utf-8"))


class PickleSerializer(CacheSerializer):
    """Pickle序列化器"""

    def serialize(self, data: Any) -> bytes:
        return pickle.dumps(data)

    def deserialize(self, data: bytes) -> Any:
        return pickle.loads(data)


class StringSerializer(CacheSerializer):
    """字符串序列化器"""

    def serialize(self, data: Any) -> bytes:
        return str(data).encode("utf-8")

    def deserialize(self, data: bytes) -> Any:
        return data.decode("utf-8")


class CacheBackendInterface(ABC):
    """缓存后端接口"""

    @abstractmethod
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        pass

    @abstractmethod
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存值"""
        pass

    @abstractmethod
    def delete(self, key: str) -> bool:
        """删除缓存值"""
        pass

    @abstractmethod
    def exists(self, key: str) -> bool:
        """检查键是否存在"""
        pass

    @abstractmethod
    def clear(self) -> bool:
        """清空缓存"""
        pass

    @abstractmethod
    def size(self) -> int:
        """获取缓存大小"""
        pass


class MemoryCacheBackend(CacheBackendInterface):
    """内存缓存后端"""

    def __init__(self, config: CacheConfig):
        self.config = config
        self.cache: Dict[str, CacheEntry] = {}
        self.access_order: OrderedDict = OrderedDict()
        self.lock = threading.RLock()
        self.serializer = self._get_serializer()

    def _get_serializer(self) -> CacheSerializer:
        """获取序列化器"""
        if self.config.serialization == SerializationType.JSON:
            return JSONSerializer()
        elif self.config.serialization == SerializationType.PICKLE:
            return PickleSerializer()
        elif self.config.serialization == SerializationType.STRING:
            return StringSerializer()
        else:
            return JSONSerializer()

    def _evict_if_needed(self) -> None:
        """根据策略淘汰缓存"""
        if len(self.cache) < self.config.max_size:
            return

        if self.config.eviction_policy == EvictionPolicy.LRU:
            # 删除最近最少使用的
            oldest_key = next(iter(self.access_order))
            self._remove_entry(oldest_key)
        elif self.config.eviction_policy == EvictionPolicy.LFU:
            # 删除使用频率最低的
            min_count = min(entry.access_count for entry in self.cache.values())
            for key, entry in self.cache.items():
                if entry.access_count == min_count:
                    self._remove_entry(key)
                    break
        elif self.config.eviction_policy == EvictionPolicy.FIFO:
            # 删除最早添加的
            oldest_key = next(iter(self.cache))
            self._remove_entry(oldest_key)

    def _remove_entry(self, key: str) -> None:
        """移除缓存条目"""
        if key in self.cache:
            del self.cache[key]
        if key in self.access_order:
            del self.access_order[key]

    def _cleanup_expired(self) -> None:
        """清理过期条目"""
        expired_keys = [key for key, entry in self.cache.items() if entry.is_expired()]
        for key in expired_keys:
            self._remove_entry(key)

    def get(self, key: str) -> Optional[Any]:
        with self.lock:
            self._cleanup_expired()

            if key not in self.cache:
                return None

            entry = self.cache[key]
            if entry.is_expired():
                self._remove_entry(key)
                return None

            entry.touch()
            # 更新访问顺序
            if key in self.access_order:
                del self.access_order[key]
            self.access_order[key] = True

            return entry.value

    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        with self.lock:
            try:
                # 序列化值以计算大小
                serialized = self.serializer.serialize(value)
                size = len(serialized)

                # 创建缓存条目
                entry = CacheEntry(
                    key=key,
                    value=value,
                    created_at=time.time(),
                    accessed_at=time.time(),
                    ttl=ttl or self.config.default_ttl,
                    size=size,
                )

                # 如果键已存在，先删除
                if key in self.cache:
                    self._remove_entry(key)

                # 检查是否需要淘汰
                self._evict_if_needed()

                # 添加新条目
                self.cache[key] = entry
                self.access_order[key] = True

                return True
            except Exception as e:
                logging.error(f"Failed to set cache key {key}: {e}")
                return False

    def delete(self, key: str) -> bool:
        with self.lock:
            if key in self.cache:
                self._remove_entry(key)
                return True
            return False

    def exists(self, key: str) -> bool:
        with self.lock:
            self._cleanup_expired()
            return key in self.cache and not self.cache[key].is_expired()

    def clear(self) -> bool:
        with self.lock:
            self.cache.clear()
            self.access_order.clear()
            return True

    def size(self) -> int:
        with self.lock:
            self._cleanup_expired()
            return len(self.cache)


class FileCacheBackend(CacheBackendInterface):
    """文件缓存后端"""

    def __init__(self, config: CacheConfig):
        self.config = config
        self.cache_dir = Path(config.file_cache_dir or "./cache")
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.serializer = self._get_serializer()
        self.lock = threading.RLock()

    def _get_serializer(self) -> CacheSerializer:
        """获取序列化器"""
        if self.config.serialization == SerializationType.JSON:
            return JSONSerializer()
        elif self.config.serialization == SerializationType.PICKLE:
            return PickleSerializer()
        else:
            return PickleSerializer()

    def _get_file_path(self, key: str) -> Path:
        """获取缓存文件路径"""
        # 使用MD5哈希避免文件名问题
        hash_key = hashlib.md5(key.encode()).hexdigest()
        return self.cache_dir / f"{hash_key}.cache"

    def get(self, key: str) -> Optional[Any]:
        with self.lock:
            file_path = self._get_file_path(key)
            if not file_path.exists():
                return None

            try:
                with open(file_path, "rb") as f:
                    data = f.read()
                    entry_data = self.serializer.deserialize(data)

                    # 检查过期
                    if "ttl" in entry_data and entry_data["ttl"]:
                        if time.time() - entry_data["created_at"] > entry_data["ttl"]:
                            file_path.unlink(missing_ok=True)
                            return None

                    return entry_data["value"]
            except Exception as e:
                logging.error(f"Failed to read cache file {file_path}: {e}")
                return None

    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        with self.lock:
            try:
                file_path = self._get_file_path(key)
                entry_data = {
                    "key": key,
                    "value": value,
                    "created_at": time.time(),
                    "ttl": ttl or self.config.default_ttl,
                }

                serialized = self.serializer.serialize(entry_data)
                with open(file_path, "wb") as f:
                    f.write(serialized)
                return True
            except Exception as e:
                logging.error(f"Failed to write cache file for key {key}: {e}")
                return False

    def delete(self, key: str) -> bool:
        with self.lock:
            file_path = self._get_file_path(key)
            if file_path.exists():
                file_path.unlink()
                return True
            return False

    def exists(self, key: str) -> bool:
        return self.get(key) is not None

    def clear(self) -> bool:
        with self.lock:
            try:
                for file_path in self.cache_dir.glob("*.cache"):
                    file_path.unlink()
                return True
            except Exception as e:
                logging.error(f"Failed to clear cache directory: {e}")
                return False

    def size(self) -> int:
        return len(list(self.cache_dir.glob("*.cache")))


class CacheManager:
    """统一缓存管理器"""

    def __init__(self, config: CacheConfig):
        self.config = config
        self.backend = self._create_backend()
        self.stats = CacheStats()
        self.lock = threading.RLock()
        self.namespace = config.namespace

    def _create_backend(self) -> CacheBackendInterface:
        """创建缓存后端"""
        if self.config.backend == CacheBackend.MEMORY:
            return MemoryCacheBackend(self.config)
        elif self.config.backend == CacheBackend.FILE:
            return FileCacheBackend(self.config)
        else:
            # 默认使用内存缓存
            return MemoryCacheBackend(self.config)

    def _make_key(self, key: str) -> str:
        """生成带命名空间的键"""
        return f"{self.namespace}:{key}"

    def _update_stats(self, operation: CacheOperation, hit: bool = False) -> None:
        """更新统计信息"""
        if not self.config.enable_stats:
            return

        with self.lock:
            if operation == CacheOperation.GET:
                if hit:
                    self.stats.hits += 1
                else:
                    self.stats.misses += 1
            elif operation == CacheOperation.SET:
                self.stats.sets += 1
            elif operation == CacheOperation.DELETE:
                self.stats.deletes += 1

            self.stats.calculate_hit_rate()
            self.stats.size = self.backend.size()

    def get(self, key: str, default: Any = None) -> Any:
        """获取缓存值"""
        start_time = time.time()
        namespaced_key = self._make_key(key)

        try:
            value = self.backend.get(namespaced_key)
            hit = value is not None
            self._update_stats(CacheOperation.GET, hit)

            if self.config.enable_stats:
                access_time = time.time() - start_time
                self.stats.avg_access_time = (
                    self.stats.avg_access_time
                    * (self.stats.hits + self.stats.misses - 1)
                    + access_time
                ) / (self.stats.hits + self.stats.misses)

            return value if value is not None else default
        except Exception as e:
            logging.error(f"Cache get error for key {key}: {e}")
            return default

    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存值"""
        namespaced_key = self._make_key(key)

        try:
            result = self.backend.set(namespaced_key, value, ttl)
            self._update_stats(CacheOperation.SET)
            return result
        except Exception as e:
            logging.error(f"Cache set error for key {key}: {e}")
            return False

    def delete(self, key: str) -> bool:
        """删除缓存值"""
        namespaced_key = self._make_key(key)

        try:
            result = self.backend.delete(namespaced_key)
            self._update_stats(CacheOperation.DELETE)
            return result
        except Exception as e:
            logging.error(f"Cache delete error for key {key}: {e}")
            return False

    def exists(self, key: str) -> bool:
        """检查键是否存在"""
        namespaced_key = self._make_key(key)
        return self.backend.exists(namespaced_key)

    def clear(self) -> bool:
        """清空缓存"""
        try:
            result = self.backend.clear()
            self.stats = CacheStats()
            return result
        except Exception as e:
            logging.error(f"Cache clear error: {e}")
            return False

    def get_stats(self) -> CacheStats:
        """获取缓存统计信息"""
        self.stats.size = self.backend.size()
        return self.stats

    def reset_stats(self) -> None:
        """重置统计信息"""
        self.stats = CacheStats()

    def get_or_set(
        self, key: str, factory: Callable[[], Any], ttl: Optional[int] = None
    ) -> Any:
        """获取缓存值，如果不存在则通过工厂函数创建"""
        value = self.get(key)
        if value is None:
            value = factory()
            self.set(key, value, ttl)
        return value

    def mget(self, keys: List[str]) -> Dict[str, Any]:
        """批量获取缓存值"""
        result = {}
        for key in keys:
            value = self.get(key)
            if value is not None:
                result[key] = value
        return result

    def mset(self, mapping: Dict[str, Any], ttl: Optional[int] = None) -> bool:
        """批量设置缓存值"""
        try:
            for key, value in mapping.items():
                self.set(key, value, ttl)
            return True
        except Exception as e:
            logging.error(f"Cache mset error: {e}")
            return False

    def increment(self, key: str, delta: int = 1) -> Optional[int]:
        """递增计数器"""
        try:
            current = self.get(key, 0)
            if isinstance(current, (int, float)):
                new_value = int(current) + delta
                self.set(key, new_value)
                return new_value
            return None
        except Exception as e:
            logging.error(f"Cache increment error for key {key}: {e}")
            return None

    def decrement(self, key: str, delta: int = 1) -> Optional[int]:
        """递减计数器"""
        return self.increment(key, -delta)


# 全局缓存管理器实例
_cache_manager: Optional[CacheManager] = None
_cache_lock = threading.Lock()


def get_cache_manager(config: Optional[CacheConfig] = None) -> CacheManager:
    """获取缓存管理器单例"""
    global _cache_manager

    if _cache_manager is None:
        with _cache_lock:
            if _cache_manager is None:
                _cache_manager = CacheManager(config or CacheConfig())

    return _cache_manager


def cache_result(ttl: int = 3600, key_func: Optional[Callable] = None):
    """缓存函数结果的装饰器"""

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                # 默认使用函数名和参数生成键
                args_str = str(args) + str(sorted(kwargs.items()))
                cache_key = (
                    f"{func.__name__}:{hashlib.md5(args_str.encode()).hexdigest()}"
                )

            cache_manager = get_cache_manager()

            # 尝试从缓存获取
            result = cache_manager.get(cache_key)
            if result is not None:
                return result

            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache_manager.set(cache_key, result, ttl)
            return result

        return wrapper

    return decorator


def cache_clear(pattern: Optional[str] = None) -> bool:
    """清空缓存"""
    cache_manager = get_cache_manager()
    return cache_manager.clear()


def cache_stats() -> CacheStats:
    """获取缓存统计信息"""
    cache_manager = get_cache_manager()
    return cache_manager.get_stats()


def cached_property(ttl: int = 3600):
    """缓存属性装饰器"""

    def decorator(func: Callable) -> property:
        cache_key = f"_cached_{func.__name__}"

        def getter(self):
            if not hasattr(self, cache_key):
                setattr(self, cache_key, func(self))
            return getattr(self, cache_key)

        def setter(self, value):
            setattr(self, cache_key, value)

        def deleter(self):
            if hasattr(self, cache_key):
                delattr(self, cache_key)

        return property(getter, setter, deleter)

    return decorator
