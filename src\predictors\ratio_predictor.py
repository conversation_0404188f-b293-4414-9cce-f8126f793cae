"""
比值预测器
实现红球和蓝球的奇偶比、大小比、质合比预测
"""

from typing import Dict, List, Tuple, Any
import pandas as pd
import numpy as np
from collections import Counter
from dataclasses import dataclass

from ..core.interfaces import IPredictor, PredictionResult, PredictionType, BallType


@dataclass
class RatioPrediction:
    """比值预测结果"""
    ratio: str  # 如 "1:4", "2:3"
    confidence: float
    state: int  # 状态编号


class RatioPredictor(IPredictor):
    """比值预测器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        
        # 红球比值状态映射 (5个红球)
        self.red_ratio_states = {
            0: "0:5", 1: "1:4", 2: "2:3", 3: "3:2", 4: "4:1", 5: "5:0"
        }
        
        # 蓝球比值状态映射 (2个蓝球)
        self.blue_ratio_states = {
            0: "0:2", 1: "1:1", 2: "2:0"
        }
        
        # 质数定义
        self.primes = {2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31}
    
    def predict(self, data: pd.DataFrame, target_index: int, **kwargs) -> PredictionResult:
        """执行比值预测"""
        prediction_type = kwargs.get('prediction_type', 'odd_even')
        ball_type = kwargs.get('ball_type', 'red')
        
        if ball_type == 'red':
            return self._predict_red_ratio(data, target_index, prediction_type)
        else:
            return self._predict_blue_ratio(data, target_index, prediction_type)
    
    def train(self, data: pd.DataFrame, **kwargs) -> bool:
        """训练模型（比值预测不需要特殊训练）"""
        return True
    
    def get_prediction_type(self) -> PredictionType:
        """获取预测类型"""
        return PredictionType.ODD_EVEN_RATIO
    
    def _predict_red_ratio(self, data: pd.DataFrame, target_index: int, ratio_type: str) -> PredictionResult:
        """预测红球比值"""
        # 获取历史数据（从target_index+1开始，因为要预测target_index期）
        history_data = data.iloc[target_index+1:].copy()
        
        if len(history_data) < 10:
            # 数据不足，返回默认预测
            return self._get_default_red_prediction(ratio_type)
        
        # 计算历史比值
        ratios = []
        for _, row in history_data.iterrows():
            red_balls = [row['红球1'], row['红球2'], row['红球3'], row['红球4'], row['红球5']]
            ratio = self._calculate_red_ratio(red_balls, ratio_type)
            ratios.append(ratio)
        
        # 使用马尔科夫链预测
        predictions = self._markov_predict_red(ratios)
        
        return PredictionResult(
            prediction_type=PredictionType.ODD_EVEN_RATIO,
            ball_type=BallType.RED,
            value={
                'ratio_type': ratio_type,
                'predictions': predictions,
                'state_mapping': self.red_ratio_states
            },
            confidence=predictions[0].confidence if predictions else 0.0,
            metadata={'target_index': target_index, 'history_length': len(history_data)}
        )
    
    def _predict_blue_ratio(self, data: pd.DataFrame, target_index: int, ratio_type: str) -> PredictionResult:
        """预测蓝球比值"""
        # 获取历史数据
        history_data = data.iloc[target_index+1:].copy()
        
        if len(history_data) < 10:
            # 数据不足，返回默认预测
            return self._get_default_blue_prediction(ratio_type)
        
        # 计算历史比值
        ratios = []
        for _, row in history_data.iterrows():
            blue_balls = [row['蓝球1'], row['蓝球2']]
            ratio = self._calculate_blue_ratio(blue_balls, ratio_type)
            ratios.append(ratio)
        
        # 使用马尔科夫链预测
        predictions = self._markov_predict_blue(ratios)
        
        return PredictionResult(
            prediction_type=PredictionType.ODD_EVEN_RATIO,
            ball_type=BallType.BLUE,
            value={
                'ratio_type': ratio_type,
                'predictions': predictions,
                'state_mapping': self.blue_ratio_states
            },
            confidence=predictions[0].confidence if predictions else 0.0,
            metadata={'target_index': target_index, 'history_length': len(history_data)}
        )
    
    def _calculate_red_ratio(self, balls: List[int], ratio_type: str) -> int:
        """计算红球比值状态"""
        if ratio_type == 'odd_even':
            # 奇偶比
            odd_count = sum(1 for ball in balls if ball % 2 == 1)
            return odd_count  # 0-5
        elif ratio_type == 'size':
            # 大小比 (大号：18-35)
            large_count = sum(1 for ball in balls if ball >= 18)
            return large_count  # 0-5
        elif ratio_type == 'prime_composite':
            # 质合比
            prime_count = sum(1 for ball in balls if ball in self.primes)
            return prime_count  # 0-5
        else:
            return 2  # 默认状态
    
    def _calculate_blue_ratio(self, balls: List[int], ratio_type: str) -> int:
        """计算蓝球比值状态"""
        if ratio_type == 'odd_even':
            # 奇偶比
            odd_count = sum(1 for ball in balls if ball % 2 == 1)
            return odd_count  # 0-2
        elif ratio_type == 'size':
            # 大小比 (大号：7-12)
            large_count = sum(1 for ball in balls if ball >= 7)
            return large_count  # 0-2
        elif ratio_type == 'prime_composite':
            # 质合比
            prime_count = sum(1 for ball in balls if ball in self.primes)
            return prime_count  # 0-2
        else:
            return 1  # 默认状态
    
    def _markov_predict_red(self, ratios: List[int]) -> List[RatioPrediction]:
        """使用马尔科夫链预测红球比值"""
        if len(ratios) < 2:
            return self._get_default_red_predictions()
        
        # 构建转移矩阵
        transition_matrix = np.zeros((6, 6))  # 6个状态 (0-5)
        
        for i in range(len(ratios) - 1):
            current_state = ratios[i]
            next_state = ratios[i + 1]
            transition_matrix[current_state][next_state] += 1
        
        # 标准化转移矩阵
        for i in range(6):
            row_sum = np.sum(transition_matrix[i])
            if row_sum > 0:
                transition_matrix[i] = transition_matrix[i] / row_sum
        
        # 获取当前状态
        current_state = ratios[0]  # 最新状态
        
        # 预测下一状态的概率
        next_probs = transition_matrix[current_state]
        
        # 按概率排序
        state_probs = [(i, prob) for i, prob in enumerate(next_probs) if prob > 0]
        state_probs.sort(key=lambda x: x[1], reverse=True)
        
        # 转换为预测结果
        predictions = []
        for state, prob in state_probs[:2]:  # 取前2个最可能的状态
            predictions.append(RatioPrediction(
                ratio=self.red_ratio_states[state],
                confidence=prob,
                state=state
            ))
        
        return predictions if predictions else self._get_default_red_predictions()
    
    def _markov_predict_blue(self, ratios: List[int]) -> List[RatioPrediction]:
        """使用马尔科夫链预测蓝球比值"""
        if len(ratios) < 2:
            return self._get_default_blue_predictions()
        
        # 构建转移矩阵
        transition_matrix = np.zeros((3, 3))  # 3个状态 (0-2)
        
        for i in range(len(ratios) - 1):
            current_state = ratios[i]
            next_state = ratios[i + 1]
            transition_matrix[current_state][next_state] += 1
        
        # 标准化转移矩阵
        for i in range(3):
            row_sum = np.sum(transition_matrix[i])
            if row_sum > 0:
                transition_matrix[i] = transition_matrix[i] / row_sum
        
        # 获取当前状态
        current_state = ratios[0]  # 最新状态
        
        # 预测下一状态的概率
        next_probs = transition_matrix[current_state]
        
        # 按概率排序
        state_probs = [(i, prob) for i, prob in enumerate(next_probs) if prob > 0]
        state_probs.sort(key=lambda x: x[1], reverse=True)
        
        # 转换为预测结果
        predictions = []
        for state, prob in state_probs[:2]:  # 取前2个最可能的状态
            predictions.append(RatioPrediction(
                ratio=self.blue_ratio_states[state],
                confidence=prob,
                state=state
            ))
        
        return predictions if predictions else self._get_default_blue_predictions()
    
    def _get_default_red_prediction(self, ratio_type: str) -> PredictionResult:
        """获取默认红球预测"""
        predictions = self._get_default_red_predictions()
        
        return PredictionResult(
            prediction_type=PredictionType.ODD_EVEN_RATIO,
            ball_type=BallType.RED,
            value={
                'ratio_type': ratio_type,
                'predictions': predictions,
                'state_mapping': self.red_ratio_states
            },
            confidence=predictions[0].confidence,
            metadata={'default': True}
        )
    
    def _get_default_blue_prediction(self, ratio_type: str) -> PredictionResult:
        """获取默认蓝球预测"""
        predictions = self._get_default_blue_predictions()
        
        return PredictionResult(
            prediction_type=PredictionType.ODD_EVEN_RATIO,
            ball_type=BallType.BLUE,
            value={
                'ratio_type': ratio_type,
                'predictions': predictions,
                'state_mapping': self.blue_ratio_states
            },
            confidence=predictions[0].confidence,
            metadata={'default': True}
        )
    
    def _get_default_red_predictions(self) -> List[RatioPrediction]:
        """获取默认红球预测"""
        return [
            RatioPrediction(ratio="2:3", confidence=0.4, state=2),
            RatioPrediction(ratio="3:2", confidence=0.3, state=3)
        ]
    
    def _get_default_blue_predictions(self) -> List[RatioPrediction]:
        """获取默认蓝球预测"""
        return [
            RatioPrediction(ratio="1:1", confidence=0.5, state=1),
            RatioPrediction(ratio="2:0", confidence=0.3, state=2)
        ]
    
    def predict_all_ratios(self, data: pd.DataFrame, target_index: int) -> Dict[str, PredictionResult]:
        """预测所有比值"""
        results = {}
        
        # 红球比值预测
        for ratio_type in ['odd_even', 'size', 'prime_composite']:
            key = f"red_{ratio_type}"
            results[key] = self._predict_red_ratio(data, target_index, ratio_type)
        
        # 蓝球比值预测
        for ratio_type in ['odd_even', 'size', 'prime_composite']:
            key = f"blue_{ratio_type}"
            results[key] = self._predict_blue_ratio(data, target_index, ratio_type)
        
        return results
    
    def evaluate_prediction(self, prediction: PredictionResult, actual_balls: List[int]) -> Dict[str, Any]:
        """评估预测结果"""
        if not prediction.value or 'predictions' not in prediction.value:
            return {'hit': False, 'confidence': 0.0}
        
        ratio_type = prediction.value['ratio_type']
        predictions = prediction.value['predictions']
        ball_type = prediction.ball_type
        
        # 计算实际比值
        if ball_type == BallType.RED:
            actual_state = self._calculate_red_ratio(actual_balls, ratio_type)
            actual_ratio = self.red_ratio_states[actual_state]
        else:
            actual_state = self._calculate_blue_ratio(actual_balls, ratio_type)
            actual_ratio = self.blue_ratio_states[actual_state]
        
        # 检查是否命中
        hit = False
        hit_confidence = 0.0
        
        for pred in predictions:
            if pred.ratio == actual_ratio:
                hit = True
                hit_confidence = pred.confidence
                break
        
        return {
            'hit': hit,
            'confidence': hit_confidence,
            'actual_ratio': actual_ratio,
            'actual_state': actual_state,
            'predictions': [(p.ratio, p.confidence) for p in predictions]
        }