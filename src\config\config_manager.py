"""
配置管理系统
提供统一的配置管理，支持多环境、热更新和配置验证
"""

import json
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, Union, List
import logging
from dataclasses import dataclass, asdict
from enum import Enum
import os
from threading import Lock

from ..core.interfaces import IConfigManager


class ConfigFormat(Enum):
    """配置文件格式"""
    JSON = "json"
    YAML = "yaml"
    YML = "yml"


@dataclass
class ConfigMetadata:
    """配置元数据"""
    version: str
    last_modified: float
    source_file: str
    format: ConfigFormat
    checksum: str = ""


class ConfigManager(IConfigManager):
    """配置管理器"""
    
    def __init__(self, 
                 config_dir: str = "config",
                 environment: str = "default",
                 auto_reload: bool = False):
        self.config_dir = Path(config_dir)
        self.environment = environment
        self.auto_reload = auto_reload
        self.logger = logging.getLogger(__name__)
        
        # 配置存储
        self._configs: Dict[str, Any] = {}
        self._metadata: Dict[str, ConfigMetadata] = {}
        self._lock = Lock()
        
        # 默认配置
        self._default_configs = self._get_default_configs()
        
        # 初始化
        self._ensure_config_dir()
        self._load_all_configs()
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        with self._lock:
            # 支持嵌套键访问，如 "database.host"
            keys = key.split('.')
            value = self._configs
            
            try:
                for k in keys:
                    value = value[k]
                return value
            except (KeyError, TypeError):
                # 尝试从默认配置获取
                try:
                    value = self._default_configs
                    for k in keys:
                        value = value[k]
                    return value
                except (KeyError, TypeError):
                    return default
    
    def set_config(self, key: str, value: Any) -> None:
        """设置配置值"""
        with self._lock:
            keys = key.split('.')
            config = self._configs
            
            # 创建嵌套结构
            for k in keys[:-1]:
                if k not in config:
                    config[k] = {}
                config = config[k]
            
            config[keys[-1]] = value
            
            self.logger.debug(f"配置已更新: {key} = {value}")
    
    def load_config(self, config_path: str) -> bool:
        """加载配置文件"""
        try:
            file_path = Path(config_path)
            if not file_path.exists():
                self.logger.warning(f"配置文件不存在: {config_path}")
                return False
            
            # 确定文件格式
            format_type = self._detect_format(file_path)
            
            # 读取配置
            config_data = self._read_config_file(file_path, format_type)
            
            # 合并到主配置
            with self._lock:
                self._merge_config(self._configs, config_data)
                
                # 更新元数据
                self._metadata[config_path] = ConfigMetadata(
                    version="1.0",
                    last_modified=file_path.stat().st_mtime,
                    source_file=str(file_path),
                    format=format_type
                )
            
            self.logger.info(f"配置文件加载成功: {config_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"加载配置文件失败 {config_path}: {e}")
            return False
    
    def save_config(self, config_path: str, config_data: Dict[str, Any] = None) -> bool:
        """保存配置到文件"""
        try:
            file_path = Path(config_path)
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 使用当前配置或指定配置
            data_to_save = config_data if config_data is not None else self._configs
            
            # 确定文件格式
            format_type = self._detect_format(file_path)
            
            # 写入文件
            self._write_config_file(file_path, data_to_save, format_type)
            
            self.logger.info(f"配置文件保存成功: {config_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存配置文件失败 {config_path}: {e}")
            return False
    
    def reload_config(self, config_path: str = None) -> bool:
        """重新加载配置"""
        if config_path:
            return self.load_config(config_path)
        else:
            # 重新加载所有配置
            return self._load_all_configs()
    
    def get_all_configs(self) -> Dict[str, Any]:
        """获取所有配置"""
        with self._lock:
            return self._configs.copy()
    
    def get_config_metadata(self, config_path: str) -> Optional[ConfigMetadata]:
        """获取配置元数据"""
        return self._metadata.get(config_path)
    
    def validate_config(self, schema: Dict[str, Any]) -> List[str]:
        """验证配置"""
        errors = []
        
        def _validate_recursive(config: Dict[str, Any], schema: Dict[str, Any], path: str = ""):
            for key, expected_type in schema.items():
                current_path = f"{path}.{key}" if path else key
                
                if key not in config:
                    errors.append(f"缺少必需的配置项: {current_path}")
                    continue
                
                value = config[key]
                
                if isinstance(expected_type, dict):
                    if not isinstance(value, dict):
                        errors.append(f"配置项类型错误 {current_path}: 期望 dict，实际 {type(value).__name__}")
                    else:
                        _validate_recursive(value, expected_type, current_path)
                elif isinstance(expected_type, type):
                    if not isinstance(value, expected_type):
                        errors.append(f"配置项类型错误 {current_path}: 期望 {expected_type.__name__}，实际 {type(value).__name__}")
        
        _validate_recursive(self._configs, schema)
        return errors
    
    def _get_default_configs(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "system": {
                "name": "大乐透预测系统",
                "version": "2.0.0",
                "debug": False,
                "log_level": "INFO"
            },
            "prediction": {
                "default_periods": 10,
                "confidence_threshold": 0.6,
                "max_workers": 4,
                "enable_async": True,
                "cache_enabled": True,
                "cache_ttl": 3600
            },
            "algorithms": {
                "markov": {
                    "enabled": True,
                    "order": 2,
                    "smoothing": 0.1,
                    "weight": 0.3
                },
                "bayesian": {
                    "enabled": True,
                    "frequency_weight": 0.3,
                    "pattern_weight": 0.2,
                    "trend_weight": 0.5,
                    "weight": 0.4
                },
                "neural_network": {
                    "enabled": False,
                    "hidden_layers": [64, 32],
                    "dropout_rate": 0.2,
                    "weight": 0.3
                }
            },
            "data": {
                "source_file": "data/raw/dlt_data.csv",
                "backup_enabled": True,
                "validation_enabled": True,
                "min_records": 100
            },
            "output": {
                "format": "console",
                "save_predictions": True,
                "prediction_file": "data/results/predictions.json",
                "log_file": "logs/system.log"
            },
            "kill_numbers": {
                "red_ball_count": 5,
                "blue_ball_count": 2,
                "algorithm": "markov_bayesian",
                "success_rate_threshold": 0.9
            }
        }
    
    def _ensure_config_dir(self):
        """确保配置目录存在"""
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建默认配置文件
        default_config_file = self.config_dir / "default.json"
        if not default_config_file.exists():
            self._write_config_file(default_config_file, self._default_configs, ConfigFormat.JSON)
    
    def _load_all_configs(self) -> bool:
        """加载所有配置文件"""
        success = True
        
        # 首先加载默认配置
        with self._lock:
            self._configs = self._default_configs.copy()
        
        # 加载配置目录中的所有配置文件
        if self.config_dir.exists():
            for config_file in self.config_dir.glob("*.json"):
                if not self.load_config(str(config_file)):
                    success = False
            
            for config_file in self.config_dir.glob("*.yaml"):
                if not self.load_config(str(config_file)):
                    success = False
            
            for config_file in self.config_dir.glob("*.yml"):
                if not self.load_config(str(config_file)):
                    success = False
        
        # 加载环境特定配置
        env_config_file = self.config_dir / f"{self.environment}.json"
        if env_config_file.exists():
            if not self.load_config(str(env_config_file)):
                success = False
        
        return success
    
    def _detect_format(self, file_path: Path) -> ConfigFormat:
        """检测配置文件格式"""
        suffix = file_path.suffix.lower()
        if suffix == ".json":
            return ConfigFormat.JSON
        elif suffix in [".yaml", ".yml"]:
            return ConfigFormat.YAML
        else:
            # 默认为JSON
            return ConfigFormat.JSON
    
    def _read_config_file(self, file_path: Path, format_type: ConfigFormat) -> Dict[str, Any]:
        """读取配置文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            if format_type == ConfigFormat.JSON:
                return json.load(f)
            elif format_type in [ConfigFormat.YAML, ConfigFormat.YML]:
                return yaml.safe_load(f) or {}
            else:
                raise ValueError(f"不支持的配置文件格式: {format_type}")
    
    def _write_config_file(self, file_path: Path, data: Dict[str, Any], format_type: ConfigFormat):
        """写入配置文件"""
        with open(file_path, 'w', encoding='utf-8') as f:
            if format_type == ConfigFormat.JSON:
                json.dump(data, f, indent=2, ensure_ascii=False)
            elif format_type in [ConfigFormat.YAML, ConfigFormat.YML]:
                yaml.dump(data, f, default_flow_style=False, allow_unicode=True)
            else:
                raise ValueError(f"不支持的配置文件格式: {format_type}")
    
    def _merge_config(self, target: Dict[str, Any], source: Dict[str, Any]):
        """合并配置"""
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                self._merge_config(target[key], value)
            else:
                target[key] = value


class EnvironmentConfigManager(ConfigManager):
    """环境配置管理器
    
    支持多环境配置管理（开发、测试、生产等）
    """
    
    def __init__(self, 
                 config_dir: str = "config",
                 environment: str = None,
                 auto_reload: bool = False):
        # 从环境变量获取环境名称
        if environment is None:
            environment = os.getenv('LOTTERY_ENV', 'development')
        
        super().__init__(config_dir, environment, auto_reload)
        
        self.logger.info(f"环境配置管理器初始化完成，当前环境: {environment}")
    
    def switch_environment(self, new_environment: str) -> bool:
        """切换环境"""
        old_environment = self.environment
        self.environment = new_environment
        
        try:
            # 重新加载配置
            if self._load_all_configs():
                self.logger.info(f"环境切换成功: {old_environment} -> {new_environment}")
                return True
            else:
                # 切换失败，恢复原环境
                self.environment = old_environment
                self._load_all_configs()
                self.logger.error(f"环境切换失败，已恢复到: {old_environment}")
                return False
        except Exception as e:
            # 切换失败，恢复原环境
            self.environment = old_environment
            self._load_all_configs()
            self.logger.error(f"环境切换异常: {e}，已恢复到: {old_environment}")
            return False
    
    def get_current_environment(self) -> str:
        """获取当前环境"""
        return self.environment
    
    def list_available_environments(self) -> List[str]:
        """列出可用的环境"""
        environments = set()
        
        if self.config_dir.exists():
            for config_file in self.config_dir.glob("*.json"):
                if config_file.stem not in ['default', 'schema']:
                    environments.add(config_file.stem)
            
            for config_file in self.config_dir.glob("*.yaml"):
                if config_file.stem not in ['default', 'schema']:
                    environments.add(config_file.stem)
        
        return sorted(list(environments))