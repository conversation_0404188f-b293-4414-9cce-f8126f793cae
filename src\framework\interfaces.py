"""
回测框架接口定义
定义预测器和评估器的标准接口
"""

from abc import ABC, abstractmethod
from typing import Any
import pandas as pd
from .data_models import PredictionResult, EvaluationResult


class PredictorInterface(ABC):
    """预测器接口 - 所有预测器必须实现此接口"""
    
    @abstractmethod
    def predict_for_period(self, data_index: int, data: pd.DataFrame) -> PredictionResult:
        """
        为指定数据索引预测下一期
        
        Args:
            data_index: 数据索引（不是期号！）
            data: 完整的历史数据
            
        Returns:
            PredictionResult: 标准化的预测结果
            
        注意：
        - data_index 是数据在DataFrame中的索引位置
        - 期号通过 data.iloc[data_index]['期号'] 获取，只作为标志使用
        - 预测器应该使用 data.iloc[:data_index+1] 作为训练数据
        """
        pass
    
    @abstractmethod
    def get_predictor_name(self) -> str:
        """获取预测器名称"""
        pass
    
    @abstractmethod
    def get_predictor_version(self) -> str:
        """获取预测器版本"""
        pass


class EvaluatorInterface(ABC):
    """评估器接口 - 用于评估预测结果"""
    
    @abstractmethod
    def evaluate(self, prediction: PredictionResult, actual_data: dict) -> EvaluationResult:
        """
        评估预测结果
        
        Args:
            prediction: 预测结果
            actual_data: 实际开奖数据
            
        Returns:
            EvaluationResult: 评估结果
        """
        pass
    
    @abstractmethod
    def get_supported_metrics(self) -> list:
        """获取支持的评估指标列表"""
        pass


class DataLoaderInterface(ABC):
    """数据加载器接口"""
    
    @abstractmethod
    def load_data(self) -> pd.DataFrame:
        """加载数据"""
        pass
    
    @abstractmethod
    def validate_data(self, data: pd.DataFrame) -> bool:
        """验证数据格式"""
        pass
