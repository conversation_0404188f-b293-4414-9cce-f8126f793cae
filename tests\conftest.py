"""
pytest配置文件
提供测试夹具和配置
"""

import pytest
import pandas as pd
import numpy as np
from pathlib import Path
import sys
import tempfile
import shutil

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config import get_settings, setup_logging
from src.utils.utils import load_data


@pytest.fixture(scope="session")
def test_settings():
    """测试配置夹具"""
    # 设置测试环境
    import os
    os.environ['DEBUG'] = 'true'
    os.environ['LOG_LEVEL'] = 'DEBUG'
    
    settings = get_settings()
    setup_logging(level='DEBUG', console_enabled=True, file_enabled=False)
    
    return settings


@pytest.fixture(scope="session")
def sample_data():
    """示例数据夹具"""
    # 创建示例数据
    data = {
        '期号': [25068, 25067, 25066, 25065, 25064],
        '红球1': [1, 6, 15, 7, 5],
        '红球2': [4, 10, 18, 25, 10],
        '红球3': [17, 12, 27, 32, 18],
        '红球4': [20, 21, 28, 33, 20],
        '红球5': [22, 22, 34, 35, 34],
        '蓝球1': [4, 1, 3, 4, 1],
        '蓝球2': [10, 6, 6, 9, 8],
        '日期': ['2025-06-18', '2025-06-16', '2025-06-14', '2025-06-11', '2025-06-09']
    }
    
    return pd.DataFrame(data)


@pytest.fixture(scope="session")
def real_data():
    """真实数据夹具"""
    try:
        return load_data()
    except FileNotFoundError:
        # 如果真实数据不存在，返回示例数据
        return sample_data()


@pytest.fixture
def temp_data_file(sample_data):
    """临时数据文件夹具"""
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
        sample_data.to_csv(f.name, index=False)
        yield f.name
    
    # 清理
    Path(f.name).unlink(missing_ok=True)


@pytest.fixture
def temp_dir():
    """临时目录夹具"""
    temp_path = Path(tempfile.mkdtemp())
    yield temp_path
    
    # 清理
    shutil.rmtree(temp_path, ignore_errors=True)


@pytest.fixture
def mock_analyzer(sample_data):
    """模拟分析器夹具"""
    from src.core.analyzer import LotteryAnalyzer
    return LotteryAnalyzer(sample_data)


@pytest.fixture
def prediction_result():
    """预测结果夹具"""
    from src.core.base import PredictionResult, FeatureType, BallType
    
    return PredictionResult(
        feature_type=FeatureType.ODD_EVEN,
        ball_type=BallType.RED,
        predicted_state="3:2",
        confidence=0.75,
        probability_distribution={"3:2": 0.75, "2:3": 0.25}
    )


@pytest.fixture
def generation_result():
    """生成结果夹具"""
    from src.core.base import GenerationResult
    
    return GenerationResult(
        red_balls=[1, 5, 12, 18, 25],
        blue_balls=[3, 8],
        generation_method="test_generator",
        confidence=0.8,
        kill_numbers={"red": [[], [], [], [], []], "blue": [[], []]}
    )


# 测试标记
def pytest_configure(config):
    """配置pytest标记"""
    config.addinivalue_line(
        "markers", "unit: 单元测试"
    )
    config.addinivalue_line(
        "markers", "integration: 集成测试"
    )
    config.addinivalue_line(
        "markers", "performance: 性能测试"
    )
    config.addinivalue_line(
        "markers", "slow: 慢速测试"
    )


# 测试收集钩子
def pytest_collection_modifyitems(config, items):
    """修改测试项目"""
    for item in items:
        # 为慢速测试添加标记
        if "backtest" in item.name or "train" in item.name:
            item.add_marker(pytest.mark.slow)
        
        # 为集成测试添加标记
        if "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        
        # 为单元测试添加标记
        if "unit" in str(item.fspath):
            item.add_marker(pytest.mark.unit)
