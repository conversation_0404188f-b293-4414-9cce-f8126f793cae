"""
预测器工厂
使用工厂模式创建和管理预测器实例，支持配置驱动
"""

from typing import Dict, List, Type, Any, Optional
from abc import ABC, abstractmethod
import importlib
import json
from pathlib import Path

from .interfaces import IPredictor, IKillNumberPredictor, PredictionType, BallType
from .dependency_container import get_container


class PredictorFactory(ABC):
    """预测器工厂基类"""

    @abstractmethod
    def create_predictor(
        self, predictor_type: str, config: Dict[str, Any] = None
    ) -> IPredictor:
        """创建预测器实例"""
        pass

    @abstractmethod
    def get_available_predictors(self) -> List[str]:
        """获取可用的预测器类型"""
        pass


class ConfigurablePredictorFactory(PredictorFactory):
    """可配置的预测器工厂"""

    def __init__(self, config_path: str = None):
        """
        初始化工厂

        Args:
            config_path: 配置文件路径
        """
        self._predictors: Dict[str, Dict[str, Any]] = {}
        self._instances: Dict[str, IPredictor] = {}
        self._container = get_container()

        # 加载配置
        if config_path and Path(config_path).exists():
            self._load_config(config_path)
        else:
            self._load_default_config()

    def _load_config(self, config_path: str) -> None:
        """加载预测器配置"""
        try:
            with open(config_path, "r", encoding="utf-8") as f:
                config = json.load(f)

            self._predictors = config.get("predictors", {})
            print(f"[成功] 预测器配置加载成功: {config_path}")

        except Exception as e:
            print(f"[警告] 配置加载失败: {e}，使用默认配置")
            self._load_default_config()

    def _load_default_config(self) -> None:
        """加载默认配置"""
        self._predictors = {
            # 杀号预测器
            "enhanced_kill_system": {
                "module": "src.algorithms.enhanced_kill_system",
                "class": "EnhancedKillSystem",
                "type": "kill_number",
                "singleton": True,
                "config": {},
            },
            # 贝叶斯预测器
            "enhanced_bayesian": {
                "module": "src.algorithms.enhanced_bayesian",
                "class": "EnhancedBayesianPredictor",
                "type": "probability",
                "singleton": True,
                "config": {},
            },
            # 马尔科夫预测器
            "optimized_markov": {
                "module": "src.algorithms.optimized_markov",
                "class": "OptimizedMarkovPredictor",
                "type": "number",
                "singleton": True,
                "config": {},
            },
            # 集成预测器
            "intelligent_ensemble": {
                "module": "src.algorithms.intelligent_ensemble",
                "class": "IntelligentEnsemble",
                "type": "ensemble",
                "singleton": True,
                "config": {},
            },
            # 比值预测器
            "ratio_predictor": {
                "module": "src.predictors.ratio_predictor",
                "class": "RatioPredictor",
                "type": "ratio",
                "singleton": True,
                "config": {},
            },
            # 深度学习模型
            "lstm_predictor": {
                "module": "src.models.deep_learning.lstm_predictor",
                "class": "LSTMPredictor",
                "type": "deep_learning",
                "singleton": True,
                "config": {},
            },
            "transformer_predictor": {
                "module": "src.models.deep_learning.transformer_predictor",
                "class": "TransformerPredictor",
                "type": "deep_learning",
                "singleton": True,
                "config": {},
            },
            "multi_task_neural": {
                "module": "src.models.deep_learning.multi_task_neural",
                "class": "MultiTaskNeuralPredictor",
                "type": "deep_learning",
                "singleton": True,
                "config": {},
            },
            "ensemble_deep_learning": {
                "module": "src.models.deep_learning.ensemble_deep_learning",
                "class": "EnsembleDeepLearning",
                "type": "deep_learning",
                "singleton": True,
                "config": {},
            },
        }

    def create_predictor(
        self, predictor_type: str, config: Dict[str, Any] = None
    ) -> IPredictor:
        """
        创建预测器实例

        Args:
            predictor_type: 预测器类型
            config: 额外配置参数

        Returns:
            预测器实例
        """
        if predictor_type not in self._predictors:
            raise ValueError(f"未知的预测器类型: {predictor_type}")

        predictor_config = self._predictors[predictor_type]

        # 检查是否为单例
        if (
            predictor_config.get("singleton", False)
            and predictor_type in self._instances
        ):
            return self._instances[predictor_type]

        # 动态导入模块
        try:
            module_name = predictor_config["module"]
            class_name = predictor_config["class"]

            module = importlib.import_module(module_name)
            predictor_class = getattr(module, class_name)

            # 合并配置参数
            instance_config = predictor_config.get("config", {})
            if config:
                instance_config.update(config)

            # 创建实例
            if instance_config:
                instance = predictor_class(**instance_config)
            else:
                instance = predictor_class()

            # 缓存单例
            if predictor_config.get("singleton", False):
                self._instances[predictor_type] = instance

            return instance

        except Exception as e:
            raise RuntimeError(f"创建预测器失败 {predictor_type}: {e}")

    def get_available_predictors(self) -> List[str]:
        """获取可用的预测器类型"""
        return list(self._predictors.keys())

    def get_predictors_by_type(self, prediction_type: str) -> List[str]:
        """
        根据预测类型获取预测器

        Args:
            prediction_type: 预测类型 (kill_number, number, probability, ensemble, ratio)

        Returns:
            预测器类型列表
        """
        return [
            name
            for name, config in self._predictors.items()
            if config.get("type") == prediction_type
        ]

    def register_predictor(
        self,
        name: str,
        module: str,
        class_name: str,
        predictor_type: str,
        singleton: bool = True,
        config: Dict[str, Any] = None,
    ) -> None:
        """
        注册新的预测器

        Args:
            name: 预测器名称
            module: 模块路径
            class_name: 类名
            predictor_type: 预测器类型
            singleton: 是否单例
            config: 配置参数
        """
        self._predictors[name] = {
            "module": module,
            "class": class_name,
            "type": predictor_type,
            "singleton": singleton,
            "config": config or {},
        }

    def create_ensemble_predictor(
        self, predictor_names: List[str], weights: Dict[str, float] = None
    ) -> IPredictor:
        """
        创建集成预测器

        Args:
            predictor_names: 子预测器名称列表
            weights: 预测器权重

        Returns:
            集成预测器实例
        """
        # 创建集成预测器
        ensemble = self.create_predictor("intelligent_ensemble")

        # 添加子预测器
        for name in predictor_names:
            predictor = self.create_predictor(name)
            weight = weights.get(name, 1.0) if weights else 1.0
            ensemble.add_predictor(predictor, weight)

        return ensemble

    def save_config(self, config_path: str) -> None:
        """
        保存配置到文件

        Args:
            config_path: 配置文件路径
        """
        try:
            config = {"predictors": self._predictors}

            with open(config_path, "w", encoding="utf-8") as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            print(f"[成功] 预测器配置已保存: {config_path}")

        except Exception as e:
            print(f"[失败] 配置保存失败: {e}")


class PredictorRegistry:
    """预测器注册表"""

    def __init__(self):
        self._factories: Dict[str, PredictorFactory] = {}
        self._default_factory: Optional[PredictorFactory] = None

    def register_factory(
        self, name: str, factory: PredictorFactory, is_default: bool = False
    ) -> None:
        """
        注册预测器工厂

        Args:
            name: 工厂名称
            factory: 工厂实例
            is_default: 是否为默认工厂
        """
        self._factories[name] = factory

        if is_default or self._default_factory is None:
            self._default_factory = factory

    def get_factory(self, name: str = None) -> PredictorFactory:
        """
        获取预测器工厂

        Args:
            name: 工厂名称，为None时返回默认工厂

        Returns:
            预测器工厂实例
        """
        if name is None:
            if self._default_factory is None:
                raise ValueError("未设置默认预测器工厂")
            return self._default_factory

        if name not in self._factories:
            raise ValueError(f"未找到预测器工厂: {name}")

        return self._factories[name]

    def create_predictor(
        self,
        predictor_type: str,
        factory_name: str = None,
        config: Dict[str, Any] = None,
    ) -> IPredictor:
        """
        创建预测器实例

        Args:
            predictor_type: 预测器类型
            factory_name: 工厂名称
            config: 配置参数

        Returns:
            预测器实例
        """
        factory = self.get_factory(factory_name)
        return factory.create_predictor(predictor_type, config)


# 全局预测器注册表
_registry = PredictorRegistry()

# 注册默认工厂
_default_factory = ConfigurablePredictorFactory()
_registry.register_factory("default", _default_factory, is_default=True)


def get_predictor_factory(name: str = None) -> PredictorFactory:
    """获取预测器工厂"""
    return _registry.get_factory(name)


def create_predictor(predictor_type: str, config: Dict[str, Any] = None) -> IPredictor:
    """创建预测器实例"""
    return _registry.create_predictor(predictor_type, config=config)


def get_available_predictors() -> List[str]:
    """获取可用的预测器类型"""
    factory = _registry.get_factory()
    return factory.get_available_predictors()
