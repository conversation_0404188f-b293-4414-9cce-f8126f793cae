#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
红球大小比专项分析器
专门分析红球大小比预测困难的原因，为优化提供数据支持
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from collections import Counter
from scipy import stats
from sklearn.feature_selection import mutual_info_classif
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class RedSizeRatioAnalyzer:
    """红球大小比专项分析器"""
    
    def __init__(self, data_path: str = "data/raw/dlt_data.csv"):
        self.data_path = Path(data_path)
        self.df = None
        self.analysis_results = {}
        
    def load_and_prepare_data(self):
        """加载和准备数据"""
        print("[INFO] 加载彩票数据...")
        
        # 读取数据
        self.df = pd.read_csv(self.data_path, header=None)
        self.df.columns = ['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '蓝球1', '蓝球2', '日期']
        
        # 数据类型转换
        numeric_cols = ['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '蓝球1', '蓝球2']
        for col in numeric_cols:
            self.df[col] = pd.to_numeric(self.df[col], errors='coerce')
        
        # 删除空值
        self.df = self.df.dropna().reset_index(drop=True)
        
        # 计算红球大小比
        red_balls = ['红球1', '红球2', '红球3', '红球4', '红球5']
        self.df['红球大数个数'] = self.df[red_balls].apply(lambda row: sum(x > 17 for x in row), axis=1)
        self.df['红球小数个数'] = 5 - self.df['红球大数个数']
        self.df['红球大小比'] = self.df['红球大数个数'].astype(str) + ':' + self.df['红球小数个数'].astype(str)
        
        # 计算其他特征用于相关性分析
        self._calculate_features()
        
        print(f"[OK] 数据准备完成: {len(self.df)} 期")
        
    def _calculate_features(self):
        """计算分析用特征"""
        red_balls = ['红球1', '红球2', '红球3', '红球4', '红球5']
        blue_balls = ['蓝球1', '蓝球2']
        
        # 基础特征
        self.df['红球奇数个数'] = self.df[red_balls].apply(lambda row: sum(x % 2 == 1 for x in row), axis=1)
        self.df['红球和值'] = self.df[red_balls].sum(axis=1)
        self.df['红球均值'] = self.df[red_balls].mean(axis=1)
        self.df['红球方差'] = self.df[red_balls].var(axis=1)
        self.df['红球跨度'] = self.df[red_balls].max(axis=1) - self.df[red_balls].min(axis=1)
        self.df['蓝球大数个数'] = self.df[blue_balls].apply(lambda row: sum(x > 7 for x in row), axis=1)
        
        # 历史趋势特征
        for window in [3, 5, 10]:
            self.df[f'红球大数个数_趋势{window}'] = self.df['红球大数个数'].rolling(window=window, min_periods=1).mean()
            self.df[f'红球大数个数_波动{window}'] = self.df['红球大数个数'].rolling(window=window, min_periods=1).std().fillna(0)
    
    def analyze_distribution(self):
        """分析红球大小比分布"""
        print("\n[STEP 1] 红球大小比分布分析")
        print("=" * 50)
        
        # 统计各比例的出现次数
        ratio_counts = self.df['红球大小比'].value_counts().sort_index()
        ratio_percentages = (ratio_counts / len(self.df) * 100).round(2)
        
        print("红球大小比分布:")
        for ratio, count in ratio_counts.items():
            percentage = ratio_percentages[ratio]
            print(f"  {ratio}: {count}次 ({percentage}%)")
        
        # 保存结果
        self.analysis_results['distribution'] = {
            'counts': ratio_counts.to_dict(),
            'percentages': ratio_percentages.to_dict()
        }
        
        # 计算分布均匀性
        expected_freq = len(self.df) / len(ratio_counts)
        chi2_stat = sum((count - expected_freq) ** 2 / expected_freq for count in ratio_counts)
        print(f"\n分布均匀性检验 (卡方统计量): {chi2_stat:.2f}")
        print("(值越大表示分布越不均匀)")
        
        return ratio_counts, ratio_percentages
    
    def analyze_time_patterns(self):
        """分析时间模式"""
        print("\n[STEP 2] 时间模式分析")
        print("=" * 50)
        
        # 计算连续相同比例的情况
        consecutive_same = 0
        max_consecutive = 0
        current_consecutive = 1
        
        for i in range(1, len(self.df)):
            if self.df.iloc[i]['红球大小比'] == self.df.iloc[i-1]['红球大小比']:
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                if current_consecutive > 1:
                    consecutive_same += 1
                current_consecutive = 1
        
        print(f"连续相同比例出现次数: {consecutive_same}")
        print(f"最长连续相同长度: {max_consecutive}")
        
        # 分析周期性
        ratios = self.df['红球大数个数'].values
        
        # 计算自相关
        autocorr_lags = [1, 2, 3, 5, 7, 10, 15, 20]
        autocorr_values = []
        
        for lag in autocorr_lags:
            if len(ratios) > lag:
                corr = np.corrcoef(ratios[:-lag], ratios[lag:])[0, 1]
                autocorr_values.append(corr)
                print(f"滞后{lag}期自相关系数: {corr:.4f}")
            else:
                autocorr_values.append(0)
        
        self.analysis_results['time_patterns'] = {
            'consecutive_same': consecutive_same,
            'max_consecutive': max_consecutive,
            'autocorr': dict(zip(autocorr_lags, autocorr_values))
        }
        
        return autocorr_values
    
    def analyze_feature_correlation(self):
        """分析特征相关性"""
        print("\n[STEP 3] 特征相关性分析")
        print("=" * 50)
        
        # 选择分析特征
        feature_cols = [
            '红球奇数个数', '红球和值', '红球均值', '红球方差', '红球跨度', '蓝球大数个数',
            '红球大数个数_趋势3', '红球大数个数_趋势5', '红球大数个数_趋势10',
            '红球大数个数_波动3', '红球大数个数_波动5', '红球大数个数_波动10'
        ]
        
        # 计算互信息
        X = self.df[feature_cols].fillna(0)
        y = self.df['红球大数个数']
        
        # 标准化特征
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # 计算互信息
        mi_scores = mutual_info_classif(X_scaled, y, random_state=42)
        
        # 排序并显示
        feature_importance = list(zip(feature_cols, mi_scores))
        feature_importance.sort(key=lambda x: x[1], reverse=True)
        
        print("特征重要性 (互信息分数):")
        for feature, score in feature_importance:
            print(f"  {feature}: {score:.4f}")
        
        # 计算皮尔逊相关系数
        print("\n与红球大数个数的皮尔逊相关系数:")
        for col in feature_cols:
            if col in self.df.columns:
                corr = self.df[col].corr(self.df['红球大数个数'])
                print(f"  {col}: {corr:.4f}")
        
        self.analysis_results['feature_correlation'] = {
            'mutual_info': dict(feature_importance),
            'pearson_corr': {col: self.df[col].corr(self.df['红球大数个数']) 
                           for col in feature_cols if col in self.df.columns}
        }
        
        return feature_importance
    
    def analyze_prediction_difficulty(self):
        """分析预测难度"""
        print("\n[STEP 4] 预测难度分析")
        print("=" * 50)
        
        # 计算随机预测的期望准确率
        ratio_probs = np.array(list(self.analysis_results['distribution']['percentages'].values())) / 100
        random_accuracy = np.sum(ratio_probs ** 2) * 100
        
        print(f"随机预测期望准确率: {random_accuracy:.2f}%")
        print(f"当前模型准确率: 22.00%")
        print(f"相对于随机的提升: {22.00 - random_accuracy:.2f}%")
        
        # 计算信息熵
        entropy = -np.sum(ratio_probs * np.log2(ratio_probs + 1e-10))
        max_entropy = np.log2(len(ratio_probs))
        normalized_entropy = entropy / max_entropy
        
        print(f"\n信息熵: {entropy:.4f}")
        print(f"标准化信息熵: {normalized_entropy:.4f}")
        print("(值越接近1表示越随机，越难预测)")
        
        self.analysis_results['prediction_difficulty'] = {
            'random_accuracy': random_accuracy,
            'current_accuracy': 22.00,
            'entropy': entropy,
            'normalized_entropy': normalized_entropy
        }
        
        return random_accuracy, entropy
    
    def generate_insights(self):
        """生成分析洞察"""
        print("\n[STEP 5] 分析洞察与建议")
        print("=" * 50)
        
        insights = []
        
        # 分布分析洞察
        dist = self.analysis_results['distribution']['percentages']
        most_common = max(dist, key=dist.get)
        least_common = min(dist, key=dist.get)
        
        insights.append(f"1. 分布不均匀: 最常见比例{most_common}({dist[most_common]}%), 最少见{least_common}({dist[least_common]}%)")
        
        # 时间模式洞察
        time_patterns = self.analysis_results['time_patterns']
        max_autocorr = max(time_patterns['autocorr'].values())
        
        if max_autocorr > 0.1:
            insights.append(f"2. 存在时间依赖性: 最大自相关系数{max_autocorr:.4f}")
        else:
            insights.append("2. 时间序列接近随机游走，历史信息预测价值有限")
        
        # 特征相关性洞察
        feature_corr = self.analysis_results['feature_correlation']
        best_feature = max(feature_corr['mutual_info'], key=feature_corr['mutual_info'].get)
        best_score = feature_corr['mutual_info'][best_feature]
        
        if best_score > 0.1:
            insights.append(f"3. 最有价值特征: {best_feature} (互信息: {best_score:.4f})")
        else:
            insights.append("3. 所有特征与目标的相关性都很弱，特征工程空间有限")
        
        # 预测难度洞察
        difficulty = self.analysis_results['prediction_difficulty']
        if difficulty['normalized_entropy'] > 0.9:
            insights.append("4. 高随机性: 接近完全随机分布，预测极其困难")
        elif difficulty['normalized_entropy'] > 0.7:
            insights.append("4. 中等随机性: 存在一定模式但噪声较大")
        else:
            insights.append("4. 低随机性: 存在明显模式，有优化空间")
        
        for insight in insights:
            print(insight)
        
        self.analysis_results['insights'] = insights
        
        return insights
    
    def save_analysis_report(self, output_path: str = None):
        """保存分析报告"""
        if output_path is None:
            output_path = "models/analysis_reports/red_size_ratio_analysis.md"
        
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("# 红球大小比专项分析报告\n\n")
            
            # 分布分析
            f.write("## 1. 分布分析\n\n")
            dist = self.analysis_results['distribution']
            for ratio, percentage in dist['percentages'].items():
                f.write(f"- {ratio}: {dist['counts'][ratio]}次 ({percentage}%)\n")
            
            # 时间模式
            f.write("\n## 2. 时间模式\n\n")
            time_patterns = self.analysis_results['time_patterns']
            f.write(f"- 连续相同比例出现: {time_patterns['consecutive_same']}次\n")
            f.write(f"- 最长连续相同: {time_patterns['max_consecutive']}期\n")
            f.write("\n自相关系数:\n")
            for lag, corr in time_patterns['autocorr'].items():
                f.write(f"- 滞后{lag}期: {corr:.4f}\n")
            
            # 特征相关性
            f.write("\n## 3. 特征重要性\n\n")
            feature_corr = self.analysis_results['feature_correlation']
            for feature, score in sorted(feature_corr['mutual_info'].items(), key=lambda x: x[1], reverse=True):
                f.write(f"- {feature}: {score:.4f}\n")
            
            # 预测难度
            f.write("\n## 4. 预测难度评估\n\n")
            difficulty = self.analysis_results['prediction_difficulty']
            f.write(f"- 随机预测期望准确率: {difficulty['random_accuracy']:.2f}%\n")
            f.write(f"- 当前模型准确率: {difficulty['current_accuracy']:.2f}%\n")
            f.write(f"- 标准化信息熵: {difficulty['normalized_entropy']:.4f}\n")
            
            # 洞察建议
            f.write("\n## 5. 分析洞察\n\n")
            for insight in self.analysis_results['insights']:
                f.write(f"{insight}\n\n")
        
        print(f"\n[OK] 分析报告已保存: {output_path}")
        return output_path
    
    def run_complete_analysis(self):
        """运行完整分析"""
        print("红球大小比专项分析器")
        print("=" * 60)
        
        # 加载数据
        self.load_and_prepare_data()
        
        # 执行各项分析
        self.analyze_distribution()
        self.analyze_time_patterns()
        self.analyze_feature_correlation()
        self.analyze_prediction_difficulty()
        self.generate_insights()
        
        # 保存报告
        report_path = self.save_analysis_report()
        
        print(f"\n[SUCCESS] 完整分析完成！")
        print(f"分析报告: {report_path}")
        
        return self.analysis_results

if __name__ == "__main__":
    analyzer = RedSizeRatioAnalyzer()
    results = analyzer.run_complete_analysis()
