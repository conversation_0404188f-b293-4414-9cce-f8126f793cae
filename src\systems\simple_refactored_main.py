"""简化的重构后主系统 - 无emoji版本
演示架构重构的核心概念：依赖注入、工厂模式、门面模式、配置驱动
"""

import sys
from pathlib import Path
import pandas as pd

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.core import (
    DependencyContainer,
    ConfigurationManager,
    ConfigurablePredictorFactory,
)


class SimpleRefactoredLotterySystem:
    """简化的重构后彩票预测系统"""

    def __init__(self, config_path: str = None):
        """
        初始化系统

        Args:
            config_path: 配置文件路径
        """
        print("* 初始化重构后的彩票预测系统...")

        # 加载配置
        self.config_manager = ConfigurationManager()
        self.config = self._load_config(config_path)

        # 初始化组件
        self.container = DependencyContainer()
        self.predictor_factory = ConfigurablePredictorFactory()
        self.data = None

        print("* 重构后的彩票预测系统初始化完成")

    def _load_config(self, config_path: str = None):
        """加载配置文件"""
        try:
            if config_path and Path(config_path).exists():
                from src.core.configuration_manager import JsonConfigurationSource

                self.config_manager.add_source(JsonConfigurationSource(config_path))
                print(f"* 配置加载成功: {config_path}")
            else:
                print(f"* 配置文件不存在: {config_path}，使用默认配置")

            return self.config_manager.load_config()
        except Exception as e:
            print(f"* 配置加载失败: {e}，使用默认配置")
            return self._get_default_config()

    def _get_default_config(self):
        """获取默认配置"""
        return {
            "lottery": {
                "red_ball_range": [1, 35],
                "blue_ball_range": [1, 12],
                "red_ball_count": 5,
                "blue_ball_count": 2,
            },
            "data": {"source": "data/dlt_history.csv"},
            "predictors": {"default": "EnhancedBayesianPredictor"},
        }

    def load_data(self, data_path: str = None):
        """
        加载数据

        Args:
            data_path: 数据文件路径

        Returns:
            加载是否成功
        """
        try:
            # 确保 data_path 不为 None
            if data_path is None:
                data_path = self.config.get("data", {}).get(
                    "source", "data/dlt_history.csv"
                )

            data_file = Path(data_path)
            if not data_file.exists():
                print(f"* 数据文件不存在: {data_path}")
                return False

            self.data = pd.read_csv(data_path)
            print(f"* 数据加载成功: {len(self.data)} 条记录")
            return True

        except Exception as e:
            print(f"* 数据加载失败: {e}")
            return False

    def run_interactive(self):
        """运行交互模式"""
        print("\n* 重构后的彩票预测系统 - 交互模式")

        while True:
            print("\n* 可用选项:")
            print("1. 加载数据")
            print("2. 显示配置")
            print("3. 演示架构特性")
            print("4. 演示预测功能")
            print("5. 退出")

            try:
                choice = input("\n请选择操作 (1-5): ").strip()

                if choice == "1":
                    self._handle_load_data()
                elif choice == "2":
                    self._show_configuration()
                elif choice == "3":
                    self._demonstrate_architecture()
                elif choice == "4":
                    self._demonstrate_prediction()
                elif choice == "5":
                    print("* 感谢使用重构后的彩票预测系统！")
                    break
                else:
                    print("* 无效选择，请输入 1-5")

            except KeyboardInterrupt:
                print("\n* 感谢使用重构后的彩票预测系统！")
                break

    def _handle_load_data(self):
        print("\n* 数据加载")

        data_path = input("请输入数据文件路径 (回车使用默认): ").strip()
        if not data_path:
            data_path = self.config.get("data", {}).get(
                "source", "data/dlt_history.csv"
            )

        success = self.load_data(data_path)
        if success:
            print(f"* 数据加载成功，共 {len(self.data)} 条记录")
        else:
            print("* 数据加载失败")

    def _show_configuration(self):
        """显示系统配置"""
        print("\n* 系统配置")

        lottery_config = self.config.get("lottery", {})
        print("* 彩票配置:")
        print(f"   - 红球范围: {lottery_config.get('red_ball_range', [1, 35])}")
        print(f"   - 蓝球范围: {lottery_config.get('blue_ball_range', [1, 12])}")
        print(f"   - 红球数量: {lottery_config.get('red_ball_count', 5)}")
        print(f"   - 蓝球数量: {lottery_config.get('blue_ball_count', 2)}")

        print("\n* 数据源:")
        data_config = self.config.get("data", {})
        print(f"   - 数据文件: {data_config.get('source', 'data/dlt_history.csv')}")

        predictor_config = self.config.get("predictors", {})
        print("\n* 预测设置:")
        print(
            f"   - 默认预测器: {predictor_config.get('default', 'EnhancedBayesianPredictor')}"
        )

    def _demonstrate_architecture(self):
        """演示架构特性"""
        print("\n* 架构重构特性演示")

        print("* 已实现的架构模式:")
        print("   1. * 依赖注入容器 - 统一管理组件依赖")
        print("   2. * 工厂模式 - 动态创建预测器实例")
        print("   3. * 门面模式 - 简化系统接口")
        print("   4. * 配置驱动 - 外部配置控制行为")
        print("   5. * 接口分离 - 清晰的抽象层")

        print("\n* 架构优势:")
        print("   - 低耦合: 模块间通过接口交互")
        print("   - 高内聚: 每个模块职责单一")
        print("   - 可测试: 依赖注入便于单元测试")
        print("   - 可扩展: 工厂模式支持动态扩展")
        print("   - 可配置: 配置文件控制系统行为")

        print("\n* 核心组件:")
        print("   - src/core/dependency_container.py - 依赖注入容器")
        print("   - src/core/predictor_factory.py - 预测器工厂")
        print("   - src/core/system_facade.py - 系统门面")
        print("   - src/core/configuration_manager.py - 配置管理器")
        print("   - config/system.json - 系统配置文件")

    def _demonstrate_prediction(self):
        """演示预测功能"""
        print("\n* 预测功能演示")

        # 模拟预测结果
        demo_result = {
            "predicted_numbers": {
                "red_balls": [7, 14, 21, 28, 35],
                "blue_balls": [3, 9],
            },
            "kill_numbers": {"red_kills": [1, 8, 15, 22, 29], "blue_kills": [6, 12]},
            "confidence": 0.75,
            "architecture_info": {
                "used_patterns": ["依赖注入", "工厂模式", "门面模式"],
                "config_driven": True,
                "modular_design": True,
            },
        }

        arch_info = demo_result["architecture_info"]

        print("* 预测号码:")
        print(f"   红球: {demo_result['predicted_numbers']['red_balls']}")
        print(f"   蓝球: {demo_result['predicted_numbers']['blue_balls']}")

        print("\n* 杀号:")
        print(f"   红球杀号: {demo_result['kill_numbers']['red_kills']}")
        print(f"   蓝球杀号: {demo_result['kill_numbers']['blue_kills']}")

        print(f"\n* 置信度: {demo_result['confidence']:.2%}")

        print("\n* 架构特性:")
        print(f"   使用模式: {', '.join(arch_info['used_patterns'])}")
        print(f"   配置驱动: {'*' if arch_info['config_driven'] else '*'}")
        print(f"   模块化设计: {'*' if arch_info['modular_design'] else '*'}")


# 简化的主函数用于演示
def main():
    """主函数 - 演示重构后的架构"""
    try:
        # 创建系统实例
        system = SimpleRefactoredLotterySystem()

        # 运行交互模式
        system.run_interactive()

    except Exception as e:
        print(f"* 系统启动失败: {e}")


if __name__ == "__main__":
    main()
