#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版集成学习管理器

主要改进:
1. 动态算法权重调整
2. 多层次集成策略
3. 自适应模型选择
4. 性能监控和反馈
5. 不确定性量化
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional, Any, Callable
from dataclasses import dataclass, field
from collections import defaultdict, deque
import logging
from datetime import datetime
import json
from scipy import stats
from ..utils.utils import (
    parse_numbers, calculate_odd_even_ratio, calculate_size_ratio_red,
    calculate_size_ratio_blue, ratio_to_state, state_to_ratio
)


@dataclass
class AlgorithmMetrics:
    """算法性能指标"""
    algorithm_name: str
    accuracy_scores: deque = field(default_factory=lambda: deque(maxlen=100))
    prediction_times: deque = field(default_factory=lambda: deque(maxlen=50))
    confidence_scores: deque = field(default_factory=lambda: deque(maxlen=100))
    error_rates: deque = field(default_factory=lambda: deque(maxlen=100))
    
    # 性能统计
    total_predictions: int = 0
    correct_predictions: int = 0
    last_update: datetime = field(default_factory=datetime.now)
    
    # 稳定性指标
    performance_variance: float = 0.0
    trend_direction: float = 0.0  # 1: 上升, -1: 下降, 0: 稳定
    
    def get_current_accuracy(self) -> float:
        """获取当前准确率"""
        if not self.accuracy_scores:
            return 0.5
        return np.mean(list(self.accuracy_scores))
    
    def get_recent_performance(self, window: int = 20) -> float:
        """获取最近性能"""
        if not self.accuracy_scores:
            return 0.5
        recent_scores = list(self.accuracy_scores)[-window:]
        return np.mean(recent_scores)
    
    def get_performance_trend(self) -> float:
        """获取性能趋势"""
        if len(self.accuracy_scores) < 5:
            return 0.0
        
        scores = list(self.accuracy_scores)[-20:]  # 最近20次
        x = np.arange(len(scores))
        slope, _, _, _, _ = stats.linregress(x, scores)
        return slope
    
    def get_stability_score(self) -> float:
        """获取稳定性评分"""
        if len(self.accuracy_scores) < 10:
            return 0.5
        
        scores = list(self.accuracy_scores)
        variance = np.var(scores)
        # 稳定性 = 1 / (1 + variance)
        return 1.0 / (1.0 + variance * 10)


@dataclass
class EnsembleConfig:
    """集成配置"""
    # 权重调整策略
    weight_update_method: str = 'adaptive'  # 'fixed', 'adaptive', 'performance_based'
    weight_decay_rate: float = 0.95
    min_weight: float = 0.01
    max_weight: float = 0.8
    
    # 集成策略
    ensemble_method: str = 'weighted_average'  # 'voting', 'weighted_average', 'stacking'
    confidence_threshold: float = 0.6
    diversity_bonus: float = 0.1
    
    # 自适应参数
    adaptation_rate: float = 0.1
    performance_window: int = 50
    stability_weight: float = 0.3
    
    # 模型选择
    auto_model_selection: bool = True
    min_algorithms: int = 2
    max_algorithms: int = 8


class EnhancedEnsembleLearning:
    """增强版集成学习管理器"""
    
    def __init__(self, config: EnsembleConfig = None):
        """
        初始化增强版集成学习管理器
        
        Args:
            config: 集成配置
        """
        self.config = config or EnsembleConfig()
        
        # 算法注册表
        self.algorithms = {}
        self.algorithm_metrics = {}
        self.algorithm_weights = {}
        
        # 集成历史
        self.prediction_history = []
        self.ensemble_performance = deque(maxlen=200)
        
        # 多层次集成
        self.level1_algorithms = []  # 基础算法
        self.level2_algorithms = []  # 元算法
        self.meta_learner = None
        
        # 自适应机制
        self.adaptation_counter = 0
        self.last_adaptation = datetime.now()
        
        # 性能监控
        self.performance_monitor = {
            'accuracy_trend': deque(maxlen=100),
            'diversity_scores': deque(maxlen=100),
            'confidence_levels': deque(maxlen=100)
        }
        
        self.logger = logging.getLogger(__name__)
    
    def register_algorithm(self, name: str, algorithm: Any, 
                         algorithm_type: str = 'base', 
                         initial_weight: float = None) -> None:
        """注册算法"""
        self.algorithms[name] = algorithm
        
        # 初始化性能指标
        self.algorithm_metrics[name] = AlgorithmMetrics(algorithm_name=name)
        
        # 设置初始权重
        if initial_weight is None:
            initial_weight = 1.0 / max(1, len(self.algorithms))
        
        self.algorithm_weights[name] = initial_weight
        
        # 分类算法层次
        if algorithm_type == 'base':
            self.level1_algorithms.append(name)
        elif algorithm_type == 'meta':
            self.level2_algorithms.append(name)
        
        self.logger.info(f"算法 {name} 注册成功，类型: {algorithm_type}，初始权重: {initial_weight:.3f}")
    
    def predict_ensemble(self, data: pd.DataFrame, 
                        prediction_type: str = 'ratio') -> Dict[str, Any]:
        """集成预测"""
        if not self.algorithms:
            raise ValueError("没有注册的算法")
        
        start_time = datetime.now()
        
        # 收集各算法预测结果
        algorithm_predictions = {}
        algorithm_confidences = {}
        
        for name, algorithm in self.algorithms.items():
            try:
                if prediction_type == 'ratio':
                    pred_result = self._get_ratio_prediction(algorithm, data)
                elif prediction_type == 'numbers':
                    pred_result = self._get_number_prediction(algorithm, data)
                else:
                    pred_result = self._get_general_prediction(algorithm, data)
                
                algorithm_predictions[name] = pred_result['prediction']
                algorithm_confidences[name] = pred_result.get('confidence', 0.5)
                
                # 记录预测时间
                pred_time = (datetime.now() - start_time).total_seconds()
                self.algorithm_metrics[name].prediction_times.append(pred_time)
                
            except Exception as e:
                self.logger.warning(f"算法 {name} 预测失败: {e}")
                continue
        
        if not algorithm_predictions:
            return self._get_fallback_prediction(prediction_type)
        
        # 执行集成策略
        ensemble_result = self._apply_ensemble_strategy(
            algorithm_predictions, algorithm_confidences, prediction_type
        )
        
        # 计算集成不确定性
        uncertainty = self._calculate_ensemble_uncertainty(
            algorithm_predictions, algorithm_confidences
        )
        
        # 记录预测历史
        prediction_record = {
            'timestamp': datetime.now(),
            'prediction_type': prediction_type,
            'algorithm_predictions': algorithm_predictions,
            'algorithm_confidences': algorithm_confidences,
            'ensemble_result': ensemble_result,
            'uncertainty': uncertainty
        }
        
        self.prediction_history.append(prediction_record)
        
        return {
            'prediction': ensemble_result,
            'confidence': self._calculate_ensemble_confidence(algorithm_confidences),
            'uncertainty': uncertainty,
            'algorithm_contributions': self._get_algorithm_contributions(),
            'meta_info': {
                'active_algorithms': len(algorithm_predictions),
                'prediction_time': (datetime.now() - start_time).total_seconds(),
                'ensemble_method': self.config.ensemble_method
            }
        }
    
    def _get_ratio_prediction(self, algorithm: Any, data: pd.DataFrame) -> Dict[str, Any]:
        """获取比值预测"""
        if hasattr(algorithm, 'predict_ratios'):
            result = algorithm.predict_ratios(data)
            confidence = getattr(algorithm, 'get_model_confidence', lambda: 0.5)()
            return {'prediction': result, 'confidence': confidence}
        elif hasattr(algorithm, 'predict_ratio'):
            result = algorithm.predict_ratio(data)
            return {'prediction': result, 'confidence': 0.5}
        else:
            raise ValueError(f"算法不支持比值预测")
    
    def _get_number_prediction(self, algorithm: Any, data: pd.DataFrame) -> Dict[str, Any]:
        """获取号码预测"""
        if hasattr(algorithm, 'predict_numbers'):
            result = algorithm.predict_numbers(data)
            return {'prediction': result, 'confidence': 0.5}
        else:
            raise ValueError(f"算法不支持号码预测")
    
    def _get_general_prediction(self, algorithm: Any, data: pd.DataFrame) -> Dict[str, Any]:
        """获取通用预测"""
        if hasattr(algorithm, 'predict'):
            result = algorithm.predict(data)
            return {'prediction': result, 'confidence': 0.5}
        else:
            raise ValueError(f"算法不支持通用预测")
    
    def _apply_ensemble_strategy(self, predictions: Dict[str, Any], 
                               confidences: Dict[str, float],
                               prediction_type: str) -> Any:
        """应用集成策略"""
        if self.config.ensemble_method == 'weighted_average':
            return self._weighted_average_ensemble(predictions, confidences)
        elif self.config.ensemble_method == 'voting':
            return self._voting_ensemble(predictions, confidences)
        elif self.config.ensemble_method == 'stacking':
            return self._stacking_ensemble(predictions, confidences)
        else:
            return self._weighted_average_ensemble(predictions, confidences)
    
    def _weighted_average_ensemble(self, predictions: Dict[str, Any], 
                                 confidences: Dict[str, float]) -> Any:
        """加权平均集成"""
        if not predictions:
            return None
        
        # 获取第一个预测的结构
        first_pred = next(iter(predictions.values()))
        
        if isinstance(first_pred, dict):
            # 处理字典类型预测 (如比值预测)
            result = {}
            
            for key in first_pred.keys():
                if isinstance(first_pred[key], dict):
                    # 嵌套字典
                    result[key] = {}
                    for sub_key in first_pred[key].keys():
                        weighted_sum = 0.0
                        total_weight = 0.0
                        
                        for alg_name, pred in predictions.items():
                            if key in pred and sub_key in pred[key]:
                                weight = self._get_effective_weight(alg_name, confidences.get(alg_name, 0.5))
                                weighted_sum += pred[key][sub_key] * weight
                                total_weight += weight
                        
                        result[key][sub_key] = weighted_sum / max(total_weight, 1e-10)
                else:
                    # 简单值
                    weighted_sum = 0.0
                    total_weight = 0.0
                    
                    for alg_name, pred in predictions.items():
                        if key in pred:
                            weight = self._get_effective_weight(alg_name, confidences.get(alg_name, 0.5))
                            weighted_sum += pred[key] * weight
                            total_weight += weight
                    
                    result[key] = weighted_sum / max(total_weight, 1e-10)
            
            return result
        
        elif isinstance(first_pred, (list, tuple)):
            # 处理列表类型预测
            result = []
            pred_length = len(first_pred)
            
            for i in range(pred_length):
                weighted_sum = 0.0
                total_weight = 0.0
                
                for alg_name, pred in predictions.items():
                    if i < len(pred):
                        weight = self._get_effective_weight(alg_name, confidences.get(alg_name, 0.5))
                        weighted_sum += pred[i] * weight
                        total_weight += weight
                
                result.append(weighted_sum / max(total_weight, 1e-10))
            
            return result
        
        else:
            # 处理标量预测
            weighted_sum = 0.0
            total_weight = 0.0
            
            for alg_name, pred in predictions.items():
                weight = self._get_effective_weight(alg_name, confidences.get(alg_name, 0.5))
                weighted_sum += pred * weight
                total_weight += weight
            
            return weighted_sum / max(total_weight, 1e-10)
    
    def _voting_ensemble(self, predictions: Dict[str, Any], 
                        confidences: Dict[str, float]) -> Any:
        """投票集成"""
        # 简化投票：选择最高置信度的预测
        if not predictions:
            return None
        
        best_algorithm = max(confidences.keys(), key=lambda x: confidences[x])
        return predictions[best_algorithm]
    
    def _stacking_ensemble(self, predictions: Dict[str, Any], 
                          confidences: Dict[str, float]) -> Any:
        """堆叠集成"""
        # 简化堆叠：使用元学习器
        if self.meta_learner is None:
            return self._weighted_average_ensemble(predictions, confidences)
        
        # TODO: 实现真正的堆叠集成
        return self._weighted_average_ensemble(predictions, confidences)
    
    def _get_effective_weight(self, algorithm_name: str, confidence: float) -> float:
        """获取有效权重"""
        base_weight = self.algorithm_weights.get(algorithm_name, 0.0)
        
        # 结合置信度调整权重
        confidence_factor = confidence ** 2  # 平方增强置信度影响
        
        # 结合性能指标
        metrics = self.algorithm_metrics.get(algorithm_name)
        if metrics:
            performance_factor = metrics.get_recent_performance()
            stability_factor = metrics.get_stability_score()
            
            # 综合权重
            effective_weight = base_weight * confidence_factor * performance_factor * stability_factor
        else:
            effective_weight = base_weight * confidence_factor
        
        return max(self.config.min_weight, min(self.config.max_weight, effective_weight))
    
    def _calculate_ensemble_uncertainty(self, predictions: Dict[str, Any], 
                                      confidences: Dict[str, float]) -> float:
        """计算集成不确定性"""
        if len(predictions) < 2:
            return 1.0  # 高不确定性
        
        # 计算预测分歧度
        disagreement = self._calculate_prediction_disagreement(predictions)
        
        # 计算置信度方差
        confidence_values = list(confidences.values())
        confidence_variance = np.var(confidence_values) if len(confidence_values) > 1 else 0.0
        
        # 综合不确定性
        uncertainty = 0.6 * disagreement + 0.4 * confidence_variance
        
        return min(1.0, max(0.0, uncertainty))
    
    def _calculate_prediction_disagreement(self, predictions: Dict[str, Any]) -> float:
        """计算预测分歧度"""
        if len(predictions) < 2:
            return 0.0
        
        pred_values = list(predictions.values())
        first_pred = pred_values[0]
        
        if isinstance(first_pred, dict):
            # 字典类型：计算各键值的分歧
            total_disagreement = 0.0
            key_count = 0
            
            for key in first_pred.keys():
                if isinstance(first_pred[key], dict):
                    # 嵌套字典
                    for sub_key in first_pred[key].keys():
                        values = [pred[key][sub_key] for pred in pred_values 
                                if key in pred and sub_key in pred[key]]
                        if len(values) > 1:
                            total_disagreement += np.var(values)
                            key_count += 1
                else:
                    # 简单值
                    values = [pred[key] for pred in pred_values if key in pred]
                    if len(values) > 1:
                        total_disagreement += np.var(values)
                        key_count += 1
            
            return total_disagreement / max(key_count, 1)
        
        elif isinstance(first_pred, (list, tuple)):
            # 列表类型
            total_disagreement = 0.0
            
            for i in range(len(first_pred)):
                values = [pred[i] for pred in pred_values if i < len(pred)]
                if len(values) > 1:
                    total_disagreement += np.var(values)
            
            return total_disagreement / len(first_pred)
        
        else:
            # 标量类型
            return np.var(pred_values)
    
    def _calculate_ensemble_confidence(self, confidences: Dict[str, float]) -> float:
        """计算集成置信度"""
        if not confidences:
            return 0.5
        
        # 加权平均置信度
        weighted_confidence = 0.0
        total_weight = 0.0
        
        for alg_name, confidence in confidences.items():
            weight = self.algorithm_weights.get(alg_name, 0.0)
            weighted_confidence += confidence * weight
            total_weight += weight
        
        if total_weight == 0:
            return np.mean(list(confidences.values()))
        
        return weighted_confidence / total_weight
    
    def _get_algorithm_contributions(self) -> Dict[str, float]:
        """获取算法贡献度"""
        contributions = {}
        total_weight = sum(self.algorithm_weights.values())
        
        for name, weight in self.algorithm_weights.items():
            contributions[name] = weight / max(total_weight, 1e-10)
        
        return contributions
    
    def _get_fallback_prediction(self, prediction_type: str) -> Dict[str, Any]:
        """获取备用预测"""
        if prediction_type == 'ratio':
            fallback = {
                'odd_even': {'low': 0.33, 'medium': 0.34, 'high': 0.33},
                'size_red': {'low': 0.33, 'medium': 0.34, 'high': 0.33},
                'size_blue': {'low': 0.33, 'medium': 0.34, 'high': 0.33}
            }
        else:
            fallback = None
        
        return {
            'prediction': fallback,
            'confidence': 0.1,
            'uncertainty': 1.0,
            'algorithm_contributions': {},
            'meta_info': {
                'active_algorithms': 0,
                'prediction_time': 0.0,
                'ensemble_method': 'fallback'
            }
        }
    
    def update_performance(self, actual_result: Dict[str, Any], 
                         prediction_record: Dict[str, Any]) -> None:
        """更新性能"""
        algorithm_predictions = prediction_record['algorithm_predictions']
        
        # 更新各算法性能
        for alg_name, prediction in algorithm_predictions.items():
            accuracy = self._calculate_accuracy(prediction, actual_result)
            
            metrics = self.algorithm_metrics[alg_name]
            metrics.accuracy_scores.append(accuracy)
            metrics.total_predictions += 1
            
            if accuracy > 0.5:  # 简化的正确性判断
                metrics.correct_predictions += 1
            
            metrics.last_update = datetime.now()
        
        # 更新集成性能
        ensemble_accuracy = self._calculate_accuracy(
            prediction_record['ensemble_result'], actual_result
        )
        self.ensemble_performance.append(ensemble_accuracy)
        
        # 触发自适应调整
        self._trigger_adaptation()
        
        self.logger.info(f"性能更新完成，集成准确率: {ensemble_accuracy:.3f}")
    
    def _calculate_accuracy(self, prediction: Any, actual: Any) -> float:
        """计算准确率"""
        if prediction is None or actual is None:
            return 0.0
        
        if isinstance(prediction, dict) and isinstance(actual, dict):
            # 字典类型比较
            total_score = 0.0
            key_count = 0
            
            for key in prediction.keys():
                if key in actual:
                    if isinstance(prediction[key], dict) and isinstance(actual[key], dict):
                        # 嵌套字典
                        for sub_key in prediction[key].keys():
                            if sub_key in actual[key]:
                                pred_val = prediction[key][sub_key]
                                actual_val = actual[key][sub_key]
                                
                                # 计算相似度
                                similarity = 1.0 - abs(pred_val - actual_val)
                                total_score += max(0.0, similarity)
                                key_count += 1
                    else:
                        # 简单值比较
                        pred_val = prediction[key]
                        actual_val = actual[key]
                        
                        similarity = 1.0 - abs(pred_val - actual_val)
                        total_score += max(0.0, similarity)
                        key_count += 1
            
            return total_score / max(key_count, 1)
        
        elif isinstance(prediction, (list, tuple)) and isinstance(actual, (list, tuple)):
            # 列表类型比较
            min_length = min(len(prediction), len(actual))
            if min_length == 0:
                return 0.0
            
            total_score = 0.0
            for i in range(min_length):
                similarity = 1.0 - abs(prediction[i] - actual[i])
                total_score += max(0.0, similarity)
            
            return total_score / min_length
        
        else:
            # 标量比较
            try:
                similarity = 1.0 - abs(float(prediction) - float(actual))
                return max(0.0, similarity)
            except (ValueError, TypeError):
                return 0.0
    
    def _trigger_adaptation(self) -> None:
        """触发自适应调整"""
        self.adaptation_counter += 1
        
        # 每隔一定次数进行权重调整
        if self.adaptation_counter % 10 == 0:
            self._adapt_weights()
        
        # 每隔更长时间进行模型选择
        if self.adaptation_counter % 50 == 0:
            self._adapt_model_selection()
    
    def _adapt_weights(self) -> None:
        """自适应权重调整"""
        if self.config.weight_update_method == 'fixed':
            return
        
        new_weights = {}
        
        for alg_name in self.algorithms.keys():
            metrics = self.algorithm_metrics[alg_name]
            
            if self.config.weight_update_method == 'performance_based':
                # 基于性能的权重
                performance = metrics.get_recent_performance()
                stability = metrics.get_stability_score()
                trend = metrics.get_performance_trend()
                
                # 综合评分
                score = (performance * 0.5 + 
                        stability * self.config.stability_weight + 
                        max(0, trend) * 0.2)
                
                new_weights[alg_name] = score
            
            elif self.config.weight_update_method == 'adaptive':
                # 自适应权重
                current_weight = self.algorithm_weights[alg_name]
                performance = metrics.get_recent_performance()
                
                # 基于性能调整权重
                if performance > 0.6:
                    adjustment = self.config.adaptation_rate * (performance - 0.5)
                else:
                    adjustment = -self.config.adaptation_rate * (0.5 - performance)
                
                new_weight = current_weight + adjustment
                new_weights[alg_name] = new_weight
        
        # 归一化权重
        total_weight = sum(new_weights.values())
        if total_weight > 0:
            for alg_name in new_weights:
                normalized_weight = new_weights[alg_name] / total_weight
                self.algorithm_weights[alg_name] = max(
                    self.config.min_weight, 
                    min(self.config.max_weight, normalized_weight)
                )
        
        self.logger.info("权重自适应调整完成")
    
    def _adapt_model_selection(self) -> None:
        """自适应模型选择"""
        if not self.config.auto_model_selection:
            return
        
        # 评估各算法性能
        algorithm_scores = {}
        
        for alg_name, metrics in self.algorithm_metrics.items():
            performance = metrics.get_recent_performance()
            stability = metrics.get_stability_score()
            trend = metrics.get_performance_trend()
            
            # 综合评分
            score = performance * 0.4 + stability * 0.3 + max(0, trend) * 0.3
            algorithm_scores[alg_name] = score
        
        # 选择最佳算法
        sorted_algorithms = sorted(algorithm_scores.items(), 
                                 key=lambda x: x[1], reverse=True)
        
        # 动态调整活跃算法数量
        active_count = min(self.config.max_algorithms, 
                          max(self.config.min_algorithms, 
                              len([s for s in algorithm_scores.values() if s > 0.4])))
        
        # 更新权重（低性能算法权重降低）
        for i, (alg_name, score) in enumerate(sorted_algorithms):
            if i < active_count:
                # 保持或提升权重
                self.algorithm_weights[alg_name] = max(
                    self.algorithm_weights[alg_name], 
                    self.config.min_weight * 2
                )
            else:
                # 降低权重
                self.algorithm_weights[alg_name] = self.config.min_weight
        
        self.logger.info(f"模型选择调整完成，活跃算法数: {active_count}")
    
    def get_ensemble_status(self) -> Dict[str, Any]:
        """获取集成状态"""
        status = {
            'algorithm_count': len(self.algorithms),
            'active_algorithms': len([w for w in self.algorithm_weights.values() 
                                    if w > self.config.min_weight]),
            'ensemble_performance': {
                'recent_accuracy': np.mean(list(self.ensemble_performance)[-20:]) 
                                 if self.ensemble_performance else 0.0,
                'total_predictions': len(self.prediction_history),
                'performance_trend': self._get_ensemble_trend()
            },
            'algorithm_weights': self.algorithm_weights.copy(),
            'algorithm_metrics': {},
            'config': {
                'ensemble_method': self.config.ensemble_method,
                'weight_update_method': self.config.weight_update_method,
                'auto_model_selection': self.config.auto_model_selection
            }
        }
        
        # 算法详细指标
        for name, metrics in self.algorithm_metrics.items():
            status['algorithm_metrics'][name] = {
                'current_accuracy': metrics.get_current_accuracy(),
                'recent_performance': metrics.get_recent_performance(),
                'stability_score': metrics.get_stability_score(),
                'performance_trend': metrics.get_performance_trend(),
                'total_predictions': metrics.total_predictions,
                'last_update': metrics.last_update.isoformat()
            }
        
        return status
    
    def _get_ensemble_trend(self) -> float:
        """获取集成趋势"""
        if len(self.ensemble_performance) < 5:
            return 0.0
        
        recent_scores = list(self.ensemble_performance)[-20:]
        x = np.arange(len(recent_scores))
        slope, _, _, _, _ = stats.linregress(x, recent_scores)
        
        return slope
    
    def save_state(self, filepath: str) -> None:
        """保存状态"""
        state = {
            'algorithm_weights': self.algorithm_weights,
            'algorithm_metrics': {},
            'config': self.config.__dict__,
            'ensemble_performance': list(self.ensemble_performance),
            'adaptation_counter': self.adaptation_counter
        }
        
        # 序列化算法指标
        for name, metrics in self.algorithm_metrics.items():
            state['algorithm_metrics'][name] = {
                'accuracy_scores': list(metrics.accuracy_scores),
                'prediction_times': list(metrics.prediction_times),
                'confidence_scores': list(metrics.confidence_scores),
                'total_predictions': metrics.total_predictions,
                'correct_predictions': metrics.correct_predictions
            }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(state, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"集成状态已保存到: {filepath}")
    
    def load_state(self, filepath: str) -> None:
        """加载状态"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                state = json.load(f)
            
            # 恢复权重
            self.algorithm_weights = state.get('algorithm_weights', {})
            
            # 恢复性能历史
            self.ensemble_performance = deque(
                state.get('ensemble_performance', []), maxlen=200
            )
            
            # 恢复计数器
            self.adaptation_counter = state.get('adaptation_counter', 0)
            
            # 恢复算法指标
            metrics_data = state.get('algorithm_metrics', {})
            for name, data in metrics_data.items():
                if name in self.algorithm_metrics:
                    metrics = self.algorithm_metrics[name]
                    metrics.accuracy_scores = deque(data.get('accuracy_scores', []), maxlen=100)
                    metrics.prediction_times = deque(data.get('prediction_times', []), maxlen=50)
                    metrics.confidence_scores = deque(data.get('confidence_scores', []), maxlen=100)
                    metrics.total_predictions = data.get('total_predictions', 0)
                    metrics.correct_predictions = data.get('correct_predictions', 0)
            
            self.logger.info(f"集成状态已从 {filepath} 加载")
        
        except Exception as e:
            self.logger.error(f"加载集成状态失败: {e}")