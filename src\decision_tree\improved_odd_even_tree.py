"""改进版奇偶比决策树预测器

基于改进版基类的奇偶比决策树预测器实现。
"""

from typing import List, Tuple, Dict, Any, Optional
import random
from collections import defaultdict

from .improved_decision_tree_predictor import ImprovedDecisionTreePredictor
from .config import DecisionTreeConfig
from ..utils.logger import get_logger


class ImprovedOddEvenTreePredictor(ImprovedDecisionTreePredictor):
    """改进版奇偶比决策树预测器"""
    
    def __init__(self, config: Optional[DecisionTreeConfig] = None, **kwargs: Any) -> None:
        """初始化改进版奇偶比决策树预测器
        
        Args:
            config: 配置对象
            **kwargs: 额外的配置参数
        """
        super().__init__(config, **kwargs)
        self.logger = get_logger(self.__class__.__name__)
        
        # 号码池
        self.red_ball_pool = list(range(1, 34))  # 红球1-33
        self.blue_ball_pool = list(range(1, 17))  # 蓝球1-16
        
        if self.config.enable_detailed_logging:
            self.logger.info("改进版奇偶比决策树预测器初始化完成")
    
    def _generate_numbers(self, odd_even_pred: List[Tuple[str, float]], 
                         red_size_pred: List[Tuple[str, float]], 
                         blue_size_pred: List[Tuple[str, float]]) -> Tuple[List[int], List[int]]:
        """根据预测结果生成号码
        
        Args:
            odd_even_pred: 奇偶比预测结果
            red_size_pred: 红球大小比预测结果
            blue_size_pred: 蓝球大小预测结果
            
        Returns:
            (红球列表, 蓝球列表)
        """
        try:
            # 获取最佳预测结果
            best_odd_even = odd_even_pred[0][0] if odd_even_pred else "3:3"
            best_red_size = red_size_pred[0][0] if red_size_pred else "3:3"
            best_blue_size = blue_size_pred[0][0] if blue_size_pred else "小"
            
            if self.config.enable_detailed_logging:
                self.logger.debug(f"预测结果 - 奇偶比: {best_odd_even}, 红球大小比: {best_red_size}, 蓝球大小: {best_blue_size}")
            
            # 生成红球
            red_balls = self._generate_red_balls(best_odd_even, best_red_size)
            
            # 生成蓝球
            blue_balls = self._generate_blue_balls(best_blue_size)
            
            return red_balls, blue_balls
            
        except Exception as e:
            self.logger.error(f"生成号码失败: {e}")
            # 返回随机号码作为备选
            return self._generate_fallback_numbers()
    
    def _generate_red_balls(self, odd_even_ratio: str, size_ratio: str) -> List[int]:
        """生成红球号码
        
        Args:
            odd_even_ratio: 奇偶比（如"3:3"）
            size_ratio: 大小比（如"3:3"）
            
        Returns:
            红球号码列表
        """
        try:
            # 解析奇偶比
            odd_count, even_count = self._parse_ratio(odd_even_ratio)
            
            # 解析大小比
            small_count, big_count = self._parse_ratio(size_ratio)
            
            # 验证比例
            if odd_count + even_count != 6 or small_count + big_count != 6:
                self.logger.warning(f"无效的比例: 奇偶比={odd_even_ratio}, 大小比={size_ratio}")
                return self._generate_random_red_balls()
            
            # 分类号码池
            odd_small = [x for x in self.red_ball_pool if x % 2 == 1 and x <= 17]
            odd_big = [x for x in self.red_ball_pool if x % 2 == 1 and x > 17]
            even_small = [x for x in self.red_ball_pool if x % 2 == 0 and x <= 17]
            even_big = [x for x in self.red_ball_pool if x % 2 == 0 and x > 17]
            
            # 使用动态规划方法分配号码
            selected_balls = self._allocate_balls_optimally(
                odd_count, even_count, small_count, big_count,
                odd_small, odd_big, even_small, even_big
            )
            
            if len(selected_balls) == 6:
                return sorted(selected_balls)
            else:
                self.logger.warning(f"分配失败，生成的号码数量: {len(selected_balls)}")
                return self._generate_random_red_balls()
                
        except Exception as e:
            self.logger.error(f"生成红球失败: {e}")
            return self._generate_random_red_balls()
    
    def _allocate_balls_optimally(self, odd_count: int, even_count: int, 
                                 small_count: int, big_count: int,
                                 odd_small: List[int], odd_big: List[int],
                                 even_small: List[int], even_big: List[int]) -> List[int]:
        """优化分配号码
        
        Args:
            odd_count: 需要的奇数个数
            even_count: 需要的偶数个数
            small_count: 需要的小数个数
            big_count: 需要的大数个数
            odd_small: 奇数小数池
            odd_big: 奇数大数池
            even_small: 偶数小数池
            even_big: 偶数大数池
            
        Returns:
            分配的号码列表
        """
        selected_balls = []
        
        # 计算各个区间需要的号码数量
        # 使用贪心算法进行分配
        
        # 优先分配交集较小的区间
        allocations = [
            (min(len(odd_small), odd_count, small_count), odd_small, "奇数小数"),
            (min(len(odd_big), odd_count - len([x for x in selected_balls if x % 2 == 1]), 
                 big_count), odd_big, "奇数大数"),
            (min(len(even_small), even_count, 
                 small_count - len([x for x in selected_balls if x <= 17])), even_small, "偶数小数"),
            (min(len(even_big), even_count - len([x for x in selected_balls if x % 2 == 0]), 
                 big_count - len([x for x in selected_balls if x > 17])), even_big, "偶数大数")
        ]
        
        # 动态分配
        remaining_odd = odd_count
        remaining_even = even_count
        remaining_small = small_count
        remaining_big = big_count
        
        # 奇数小数
        need_odd_small = min(len(odd_small), remaining_odd, remaining_small)
        if need_odd_small > 0:
            selected = random.sample(odd_small, need_odd_small)
            selected_balls.extend(selected)
            remaining_odd -= need_odd_small
            remaining_small -= need_odd_small
        
        # 偶数小数
        need_even_small = min(len(even_small), remaining_even, remaining_small)
        if need_even_small > 0:
            selected = random.sample(even_small, need_even_small)
            selected_balls.extend(selected)
            remaining_even -= need_even_small
            remaining_small -= need_even_small
        
        # 奇数大数
        need_odd_big = min(len(odd_big), remaining_odd, remaining_big)
        if need_odd_big > 0:
            selected = random.sample(odd_big, need_odd_big)
            selected_balls.extend(selected)
            remaining_odd -= need_odd_big
            remaining_big -= need_odd_big
        
        # 偶数大数
        need_even_big = min(len(even_big), remaining_even, remaining_big)
        if need_even_big > 0:
            selected = random.sample(even_big, need_even_big)
            selected_balls.extend(selected)
            remaining_even -= need_even_big
            remaining_big -= need_even_big
        
        # 如果还有缺口，随机补充
        if len(selected_balls) < 6:
            available_balls = [x for x in self.red_ball_pool if x not in selected_balls]
            need_more = 6 - len(selected_balls)
            if len(available_balls) >= need_more:
                selected_balls.extend(random.sample(available_balls, need_more))
        
        return selected_balls
    
    def _generate_blue_balls(self, size_target: str) -> List[int]:
        """生成蓝球号码
        
        Args:
            size_target: 目标大小（"大" 或 "小"）
            
        Returns:
            蓝球号码列表
        """
        try:
            if size_target == "大":
                # 选择大号蓝球（9-16）
                big_blues = [x for x in self.blue_ball_pool if x > 8]
                if big_blues:
                    return [random.choice(big_blues)]
            else:
                # 选择小号蓝球（1-8）
                small_blues = [x for x in self.blue_ball_pool if x <= 8]
                if small_blues:
                    return [random.choice(small_blues)]
            
            # 如果没有合适的号码，随机选择
            return [random.choice(self.blue_ball_pool)]
            
        except Exception as e:
            self.logger.error(f"生成蓝球失败: {e}")
            return [random.choice(self.blue_ball_pool)]
    
    def _parse_ratio(self, ratio_str: str) -> Tuple[int, int]:
        """解析比例字符串
        
        Args:
            ratio_str: 比例字符串（如"3:3"）
            
        Returns:
            (第一个数, 第二个数)
        """
        try:
            parts = ratio_str.split(':')
            if len(parts) == 2:
                return int(parts[0]), int(parts[1])
            else:
                self.logger.warning(f"无效的比例格式: {ratio_str}")
                return 3, 3  # 默认值
        except ValueError as e:
            self.logger.error(f"解析比例失败: {e}")
            return 3, 3  # 默认值
    
    def _generate_random_red_balls(self) -> List[int]:
        """生成随机红球号码
        
        Returns:
            随机红球号码列表
        """
        return sorted(random.sample(self.red_ball_pool, 6))
    
    def _generate_fallback_numbers(self) -> Tuple[List[int], List[int]]:
        """生成备选号码
        
        Returns:
            (红球列表, 蓝球列表)
        """
        red_balls = self._generate_random_red_balls()
        blue_balls = [random.choice(self.blue_ball_pool)]
        
        self.logger.warning("使用随机备选号码")
        return red_balls, blue_balls
    
    def get_predictor_name(self) -> str:
        """获取预测器名称
        
        Returns:
            预测器名称
        """
        return "ImprovedOddEvenTreePredictor"
    
    def get_predictor_version(self) -> str:
        """获取预测器版本
        
        Returns:
            预测器版本
        """
        return "2.0.0"
    
    def get_strategy_description(self) -> str:
        """获取策略描述
        
        Returns:
            策略描述
        """
        return (
            "改进版奇偶比决策树预测器：\n"
            "- 基于历史数据的奇偶比、大小比和蓝球大小模式\n"
            "- 使用决策树算法进行模式识别\n"
            "- 支持特征缓存和性能监控\n"
            "- 采用优化的号码分配算法\n"
            "- 集成配置管理和错误处理"
        )
    
    def get_configuration_summary(self) -> Dict[str, Any]:
        """获取配置摘要
        
        Returns:
            配置摘要字典
        """
        return {
            'predictor_name': self.get_predictor_name(),
            'predictor_version': self.get_predictor_version(),
            'config': self.config.to_dict(),
            'performance_metrics': {
                'training_time': self.performance_metrics.training_time,
                'prediction_time': self.performance_metrics.prediction_time,
                'feature_extraction_time': self.performance_metrics.feature_extraction_time,
                'cache_hit_rate': self.performance_metrics.cache_hit_rate,
                'total_predictions': self.performance_metrics.total_predictions,
                'successful_predictions': self.performance_metrics.successful_predictions,
                'success_rate': self.performance_metrics.success_rate
            },
            'feature_cache_enabled': self.feature_cache is not None,
            'models_trained': self._is_trained()
        }