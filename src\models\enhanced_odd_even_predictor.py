#!/usr/bin/env python3
"""
增强奇偶比预测器
基于优化分析结果，使用周期预测策略
预期成功率从10%提升到30%+
"""

import numpy as np
from typing import List, Dict, Tuple
from collections import Counter, defaultdict
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.utils.utils import get_all_red_states


class EnhancedOddEvenPredictor:
    """增强奇偶比预测器 - 基于优化分析结果"""
    
    def __init__(self):
        self.name = "增强奇偶比预测器"
        
        # 基于历史分析的状态权重
        self.state_weights = {
            '3:2': 0.369,  # 历史频率36.9%
            '2:3': 0.315,  # 历史频率31.5%
            '4:1': 0.151,  # 历史频率15.1%
            '1:4': 0.123,  # 历史频率12.3%
            '5:0': 0.025,  # 历史频率2.5%
            '0:5': 0.017   # 历史频率1.7%
        }
        
        # 优化后的状态转移矩阵
        self.transition_matrix = {
            '3:2': {'3:2': 0.360, '2:3': 0.315, '4:1': 0.159, '1:4': 0.127, '5:0': 0.022, '0:5': 0.018},
            '2:3': {'3:2': 0.373, '2:3': 0.305, '4:1': 0.167, '1:4': 0.127, '5:0': 0.015, '0:5': 0.013},
            '4:1': {'3:2': 0.388, '2:3': 0.326, '1:4': 0.110, '4:1': 0.101, '5:0': 0.044, '0:5': 0.031},
            '1:4': {'3:2': 0.348, '2:3': 0.315, '4:1': 0.163, '1:4': 0.136, '5:0': 0.027, '0:5': 0.011},
            '5:0': {'2:3': 0.378, '3:2': 0.378, '5:0': 0.081, '4:1': 0.081, '1:4': 0.054, '0:5': 0.027},
            '0:5': {'3:2': 0.423, '2:3': 0.346, '4:1': 0.154, '1:4': 0.077, '5:0': 0.000, '0:5': 0.000}
        }
        
        # 优化后的权重配置
        self.markov_weight = 0.3      # 马尔可夫权重
        self.anti_weight = 0.3        # 反连续权重  
        self.frequency_weight = 0.4   # 频率权重
        
        # 周期预测参数
        self.cycle_lengths = [3, 5, 7]  # 检查的周期长度
        self.cycle_weight = 0.4         # 周期预测权重
        
        # 训练状态
        self.is_trained = False
        self.recent_states = []
        
    def train(self, state_sequence: List[str]):
        """
        训练预测器
        
        Args:
            state_sequence: 历史状态序列
        """
        self.recent_states = state_sequence[-50:]  # 保留最近50期状态
        self.is_trained = True
        
        # 更新状态权重（基于近期数据）
        if len(self.recent_states) >= 20:
            recent_counts = Counter(self.recent_states[-20:])
            total_recent = len(self.recent_states[-20:])
            
            # 动态调整权重
            for state in self.state_weights:
                recent_freq = recent_counts.get(state, 0) / total_recent
                historical_freq = self.state_weights[state]
                # 加权平均：70%历史 + 30%近期
                self.state_weights[state] = historical_freq * 0.7 + recent_freq * 0.3
    
    def predict(self, current_state: str = None) -> Tuple[str, float]:
        """
        预测下一期的奇偶比状态
        
        Args:
            current_state: 当前状态
            
        Returns:
            Tuple[str, float]: (预测状态, 置信度)
        """
        if not self.is_trained:
            return self._predict_by_frequency()
        
        # 多策略融合预测
        cycle_pred = self._predict_by_cycle()
        markov_pred = self._predict_by_markov(current_state)
        anti_pred = self._predict_by_anti_consecutive(current_state)
        frequency_pred = self._predict_by_frequency()
        
        # 融合预测结果
        final_probs = {}
        all_states = get_all_red_states()
        
        for state in all_states:
            prob = (
                cycle_pred[1].get(state, 0) * self.cycle_weight +
                markov_pred[1].get(state, 0) * self.markov_weight +
                anti_pred[1].get(state, 0) * self.anti_weight +
                frequency_pred[1].get(state, 0) * self.frequency_weight
            )
            final_probs[state] = prob
        
        # 归一化
        total_prob = sum(final_probs.values())
        if total_prob > 0:
            final_probs = {k: v/total_prob for k, v in final_probs.items()}
        
        # 选择最高概率的状态
        best_state = max(final_probs.items(), key=lambda x: x[1])
        return best_state[0], best_state[1]
    
    def _predict_by_cycle(self) -> Tuple[str, Dict[str, float]]:
        """基于周期模式的预测（表现最佳的策略）"""
        probs = {state: 0 for state in get_all_red_states()}
        
        if len(self.recent_states) < 5:
            # 不足数据时使用频率预测
            return self._predict_by_frequency()
        
        # 检查不同周期长度
        cycle_votes = defaultdict(float)
        
        for cycle_len in self.cycle_lengths:
            if len(self.recent_states) >= cycle_len:
                # 获取周期前的状态
                cycle_state = self.recent_states[-cycle_len]
                
                # 计算该周期的可信度
                confidence = self._calculate_cycle_confidence(cycle_len)
                cycle_votes[cycle_state] += confidence
        
        # 如果有周期预测
        if cycle_votes:
            total_votes = sum(cycle_votes.values())
            for state, votes in cycle_votes.items():
                probs[state] = votes / total_votes
        else:
            # 回退到频率预测
            return self._predict_by_frequency()
        
        best_state = max(probs.items(), key=lambda x: x[1])
        return best_state[0], probs
    
    def _calculate_cycle_confidence(self, cycle_len: int) -> float:
        """计算周期预测的可信度"""
        if len(self.recent_states) < cycle_len * 2:
            return 0.1
        
        # 检查历史周期一致性
        matches = 0
        checks = 0
        
        for i in range(cycle_len, min(len(self.recent_states), cycle_len * 3)):
            if i - cycle_len >= 0:
                if self.recent_states[i] == self.recent_states[i - cycle_len]:
                    matches += 1
                checks += 1
        
        if checks == 0:
            return 0.1
        
        consistency = matches / checks
        return max(0.1, consistency)
    
    def _predict_by_markov(self, current_state: str) -> Tuple[str, Dict[str, float]]:
        """基于马尔可夫链的预测"""
        if not current_state or current_state not in self.transition_matrix:
            return self._predict_by_frequency()
        
        probs = self.transition_matrix[current_state].copy()
        best_state = max(probs.items(), key=lambda x: x[1])
        return best_state[0], probs
    
    def _predict_by_anti_consecutive(self, current_state: str) -> Tuple[str, Dict[str, float]]:
        """基于反连续性的预测"""
        probs = self.state_weights.copy()
        
        if current_state:
            # 降低当前状态的概率
            probs[current_state] *= 0.3
            
            # 如果最近2期相同，进一步降低
            if len(self.recent_states) >= 2 and self.recent_states[-1] == self.recent_states[-2]:
                probs[current_state] *= 0.1
        
        # 归一化
        total = sum(probs.values())
        if total > 0:
            probs = {k: v/total for k, v in probs.items()}
        
        best_state = max(probs.items(), key=lambda x: x[1])
        return best_state[0], probs
    
    def _predict_by_frequency(self) -> Tuple[str, Dict[str, float]]:
        """基于历史频率的预测"""
        probs = self.state_weights.copy()
        best_state = max(probs.items(), key=lambda x: x[1])
        return best_state[0], probs
    
    def predict_top_k(self, current_state: str = None, k: int = 2) -> List[Tuple[str, float]]:
        """
        预测前K个最可能的状态
        
        Args:
            current_state: 当前状态
            k: 返回前K个预测
            
        Returns:
            List[Tuple[str, float]]: 前K个预测结果
        """
        if not self.is_trained:
            freq_pred = self._predict_by_frequency()
            return [(freq_pred[0], freq_pred[1])]
        
        # 获取所有状态的概率
        cycle_pred = self._predict_by_cycle()
        markov_pred = self._predict_by_markov(current_state)
        anti_pred = self._predict_by_anti_consecutive(current_state)
        frequency_pred = self._predict_by_frequency()
        
        # 融合预测结果
        final_probs = {}
        all_states = get_all_red_states()
        
        for state in all_states:
            prob = (
                cycle_pred[1].get(state, 0) * self.cycle_weight +
                markov_pred[1].get(state, 0) * self.markov_weight +
                anti_pred[1].get(state, 0) * self.anti_weight +
                frequency_pred[1].get(state, 0) * self.frequency_weight
            )
            final_probs[state] = prob
        
        # 归一化
        total_prob = sum(final_probs.values())
        if total_prob > 0:
            final_probs = {k: v/total_prob for k, v in final_probs.items()}
        
        # 返回前K个
        sorted_probs = sorted(final_probs.items(), key=lambda x: x[1], reverse=True)
        return sorted_probs[:k]
    
    def get_prediction_explanation(self, current_state: str = None) -> Dict:
        """获取预测解释"""
        if not self.is_trained:
            return {"strategy": "frequency", "confidence": "low", "reason": "未训练"}
        
        cycle_pred = self._predict_by_cycle()
        markov_pred = self._predict_by_markov(current_state)
        
        return {
            "primary_strategy": "cycle",
            "cycle_prediction": cycle_pred[0],
            "markov_prediction": markov_pred[0],
            "current_state": current_state,
            "recent_pattern": self.recent_states[-5:] if len(self.recent_states) >= 5 else self.recent_states,
            "confidence": "medium" if len(self.recent_states) >= 20 else "low"
        }
