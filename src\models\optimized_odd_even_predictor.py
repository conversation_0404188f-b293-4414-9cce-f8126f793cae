"""
优化的红球奇偶比预测器
基于历史数据分析结果进行参数优化
"""

import numpy as np
from typing import List, Dict, Tuple
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.utils.utils import get_all_red_states


class OptimizedOddEvenPredictor:
    """优化的红球奇偶比预测器"""
    
    def __init__(self):
        self.name = "优化奇偶比预测器"
        
        # 基于历史分析的优化参数
        self.state_weights = {
            '3:2': 0.369,  # 历史频率36.9%
            '2:3': 0.315,  # 历史频率31.5%
            '4:1': 0.151,  # 历史频率15.1%
            '1:4': 0.123,  # 历史频率12.3%
            '5:0': 0.025,  # 历史频率2.5%
            '0:5': 0.017   # 历史频率1.7%
        }
        
        # 状态转移概率矩阵（基于历史分析）
        self.transition_matrix = {
            '3:2': {'3:2': 0.361, '2:3': 0.319, '4:1': 0.159, '1:4': 0.116, '5:0': 0.025, '0:5': 0.020},
            '2:3': {'3:2': 0.368, '2:3': 0.304, '4:1': 0.156, '1:4': 0.123, '5:0': 0.030, '0:5': 0.019},
            '4:1': {'3:2': 0.35, '2:3': 0.30, '4:1': 0.20, '1:4': 0.10, '5:0': 0.03, '0:5': 0.02},
            '1:4': {'3:2': 0.35, '2:3': 0.30, '4:1': 0.10, '1:4': 0.20, '5:0': 0.03, '0:5': 0.02},
            '5:0': {'3:2': 0.40, '2:3': 0.25, '4:1': 0.25, '1:4': 0.05, '5:0': 0.03, '0:5': 0.02},
            '0:5': {'3:2': 0.40, '2:3': 0.25, '4:1': 0.05, '1:4': 0.25, '5:0': 0.03, '0:5': 0.02}
        }
        
        # 优化参数
        self.markov_weight = 0.5      # 增加马尔科夫权重
        self.frequency_weight = 0.3   # 频率权重
        self.trend_weight = 0.2       # 趋势权重
        
        # 近期趋势权重（基于分析：2:3占55%，3:2占25%）
        self.recent_trend_bias = {
            '2:3': 1.2,  # 增强2:3的权重
            '3:2': 1.0,  # 保持3:2的权重
            '4:1': 0.8,  # 降低其他状态权重
            '1:4': 0.8,
            '5:0': 0.6,
            '0:5': 0.6
        }
        
        self.is_trained = False
        self.recent_states = []
    
    def train(self, state_sequence: List[str]):
        """
        训练预测器
        
        Args:
            state_sequence: 状态序列（从新到旧）
        """
        self.recent_states = state_sequence[:20]  # 保存最近20期状态
        self.is_trained = True
    
    def predict(self, current_state: str = None) -> Tuple[str, float]:
        """
        预测下一期的奇偶比状态
        
        Args:
            current_state: 当前状态
            
        Returns:
            Tuple[str, float]: (预测状态, 置信度)
        """
        if not self.is_trained:
            return self._predict_by_frequency()
        
        # 多策略融合预测
        markov_pred = self._predict_by_markov(current_state)
        frequency_pred = self._predict_by_frequency()
        trend_pred = self._predict_by_trend()
        
        # 融合预测结果
        final_probs = {}
        all_states = get_all_red_states()
        
        for state in all_states:
            prob = (
                markov_pred[1].get(state, 0) * self.markov_weight +
                frequency_pred[1].get(state, 0) * self.frequency_weight +
                trend_pred[1].get(state, 0) * self.trend_weight
            )
            
            # 应用近期趋势偏置
            prob *= self.recent_trend_bias.get(state, 1.0)
            
            final_probs[state] = prob
        
        # 归一化
        total_prob = sum(final_probs.values())
        if total_prob > 0:
            final_probs = {k: v/total_prob for k, v in final_probs.items()}
        
        # 选择概率最高的状态
        best_state = max(final_probs.items(), key=lambda x: x[1])
        
        # 计算置信度（基于最高概率和分布集中度）
        confidence = self._calculate_confidence(final_probs, best_state[1])
        
        return best_state[0], confidence
    
    def _predict_by_markov(self, current_state: str) -> Tuple[str, Dict[str, float]]:
        """基于马尔科夫链预测"""
        if not current_state or current_state not in self.transition_matrix:
            return self._predict_by_frequency()
        
        probs = self.transition_matrix[current_state].copy()
        best_state = max(probs.items(), key=lambda x: x[1])
        
        return best_state[0], probs
    
    def _predict_by_frequency(self) -> Tuple[str, Dict[str, float]]:
        """基于历史频率预测"""
        probs = self.state_weights.copy()
        best_state = max(probs.items(), key=lambda x: x[1])
        
        return best_state[0], probs
    
    def _predict_by_trend(self) -> Tuple[str, Dict[str, float]]:
        """基于近期趋势预测"""
        if len(self.recent_states) < 5:
            return self._predict_by_frequency()
        
        # 分析最近5期的状态分布
        recent_5 = self.recent_states[:5]
        state_count = {}
        for state in recent_5:
            state_count[state] = state_count.get(state, 0) + 1
        
        # 基于反向逻辑：如果某状态最近出现较多，下期可能转向其他状态
        all_states = get_all_red_states()
        probs = {}
        
        for state in all_states:
            recent_freq = state_count.get(state, 0) / len(recent_5)
            # 反向权重：最近出现多的状态，下期概率降低
            if recent_freq > 0.4:  # 如果最近出现频率超过40%
                probs[state] = self.state_weights[state] * 0.7  # 降低权重
            else:
                probs[state] = self.state_weights[state] * 1.2  # 提高权重
        
        # 归一化
        total = sum(probs.values())
        if total > 0:
            probs = {k: v/total for k, v in probs.items()}
        
        best_state = max(probs.items(), key=lambda x: x[1])
        return best_state[0], probs
    
    def _calculate_confidence(self, probs: Dict[str, float], max_prob: float) -> float:
        """
        计算预测置信度
        
        Args:
            probs: 概率分布
            max_prob: 最大概率
            
        Returns:
            float: 置信度
        """
        # 基础置信度：最大概率
        base_confidence = max_prob
        
        # 分布集中度：概率分布越集中，置信度越高
        entropy = -sum(p * np.log(p + 1e-10) for p in probs.values() if p > 0)
        max_entropy = np.log(len(probs))
        concentration = 1 - (entropy / max_entropy)
        
        # 历史准确性加权（基于分析结果，设定基准置信度）
        historical_accuracy = 0.45  # 目标准确率45%
        
        # 综合置信度
        confidence = (
            base_confidence * 0.4 +
            concentration * 0.3 +
            historical_accuracy * 0.3
        )
        
        # 确保置信度在合理范围内
        confidence = max(0.3, min(0.8, confidence))
        
        return confidence
    
    def get_prediction_details(self, current_state: str = None) -> Dict:
        """获取详细的预测信息"""
        if not self.is_trained:
            return {"error": "预测器未训练"}
        
        markov_pred = self._predict_by_markov(current_state)
        frequency_pred = self._predict_by_frequency()
        trend_pred = self._predict_by_trend()
        final_pred = self.predict(current_state)
        
        return {
            "markov_prediction": markov_pred,
            "frequency_prediction": frequency_pred,
            "trend_prediction": trend_pred,
            "final_prediction": final_pred,
            "current_state": current_state,
            "recent_states": self.recent_states[:5],
            "weights": {
                "markov": self.markov_weight,
                "frequency": self.frequency_weight,
                "trend": self.trend_weight
            }
        }
