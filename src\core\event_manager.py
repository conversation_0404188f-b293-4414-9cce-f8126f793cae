#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一事件管理系统

提供标准化的事件发布、订阅和处理机制，支持同步和异步事件处理。

主要功能：
- 事件发布和订阅
- 同步和异步事件处理
- 事件过滤和路由
- 事件持久化和重放
- 事件监控和统计
- 事件优先级和延迟处理

作者: Assistant
创建时间: 2024
"""

import asyncio
import json
import time
import threading
import uuid
from abc import ABC, abstractmethod
from collections import defaultdict, deque
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Callable, Set, Tuple
from functools import wraps
import logging
from concurrent.futures import ThreadPoolExecutor, Future


class EventType(Enum):
    """事件类型"""

    SYSTEM = "system"
    USER = "user"
    APPLICATION = "application"
    NETWORK = "network"
    DATABASE = "database"
    CACHE = "cache"
    SECURITY = "security"
    CUSTOM = "custom"


class EventPriority(Enum):
    """事件优先级"""

    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4
    EMERGENCY = 5


class EventStatus(Enum):
    """事件状态"""

    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class HandlerType(Enum):
    """处理器类型"""

    SYNC = "sync"
    ASYNC = "async"
    BACKGROUND = "background"


@dataclass
class EventConfig:
    """事件配置"""

    max_queue_size: int = 10000
    max_handlers_per_event: int = 100
    enable_persistence: bool = False
    persistence_path: Optional[str] = None
    enable_replay: bool = False
    max_replay_events: int = 1000
    enable_stats: bool = True
    worker_threads: int = 4
    async_timeout: float = 30.0
    retry_attempts: int = 3
    retry_delay: float = 1.0


@dataclass
class Event:
    """事件对象"""

    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    type: EventType = EventType.CUSTOM
    priority: EventPriority = EventPriority.NORMAL
    data: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: float = field(default_factory=time.time)
    source: Optional[str] = None
    target: Optional[str] = None
    correlation_id: Optional[str] = None
    parent_id: Optional[str] = None
    status: EventStatus = EventStatus.PENDING
    retry_count: int = 0
    delay_until: Optional[float] = None
    expires_at: Optional[float] = None

    def is_expired(self) -> bool:
        """检查事件是否过期"""
        if self.expires_at is None:
            return False
        return time.time() > self.expires_at

    def is_ready(self) -> bool:
        """检查事件是否准备好处理"""
        if self.delay_until is None:
            return True
        return time.time() >= self.delay_until

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "type": self.type.value,
            "priority": self.priority.value,
            "data": self.data,
            "metadata": self.metadata,
            "timestamp": self.timestamp,
            "source": self.source,
            "target": self.target,
            "correlation_id": self.correlation_id,
            "parent_id": self.parent_id,
            "status": self.status.value,
            "retry_count": self.retry_count,
            "delay_until": self.delay_until,
            "expires_at": self.expires_at,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Event":
        """从字典创建事件"""
        event = cls(
            id=data.get("id", str(uuid.uuid4())),
            name=data.get("name", ""),
            type=EventType(data.get("type", EventType.CUSTOM.value)),
            priority=EventPriority(data.get("priority", EventPriority.NORMAL.value)),
            data=data.get("data", {}),
            metadata=data.get("metadata", {}),
            timestamp=data.get("timestamp", time.time()),
            source=data.get("source"),
            target=data.get("target"),
            correlation_id=data.get("correlation_id"),
            parent_id=data.get("parent_id"),
            status=EventStatus(data.get("status", EventStatus.PENDING.value)),
            retry_count=data.get("retry_count", 0),
            delay_until=data.get("delay_until"),
            expires_at=data.get("expires_at"),
        )
        return event


@dataclass
class EventStats:
    """事件统计信息"""

    total_events: int = 0
    processed_events: int = 0
    failed_events: int = 0
    pending_events: int = 0
    avg_processing_time: float = 0.0
    events_per_second: float = 0.0
    handler_count: int = 0
    queue_size: int = 0
    last_reset: float = field(default_factory=time.time)

    def calculate_rates(self, time_window: float = 60.0) -> None:
        """计算事件处理速率"""
        elapsed = time.time() - self.last_reset
        if elapsed > 0:
            self.events_per_second = self.processed_events / elapsed


class EventFilter(ABC):
    """事件过滤器抽象基类"""

    @abstractmethod
    def should_process(self, event: Event) -> bool:
        """判断是否应该处理事件"""
        pass


class TypeEventFilter(EventFilter):
    """按类型过滤事件"""

    def __init__(self, event_types: Set[EventType]):
        self.event_types = event_types

    def should_process(self, event: Event) -> bool:
        return event.type in self.event_types


class PriorityEventFilter(EventFilter):
    """按优先级过滤事件"""

    def __init__(self, min_priority: EventPriority):
        self.min_priority = min_priority

    def should_process(self, event: Event) -> bool:
        return event.priority.value >= self.min_priority.value


class NameEventFilter(EventFilter):
    """按名称过滤事件"""

    def __init__(self, event_names: Set[str]):
        self.event_names = event_names

    def should_process(self, event: Event) -> bool:
        return event.name in self.event_names


class EventHandler:
    """事件处理器"""

    def __init__(
        self,
        handler_func: Callable,
        handler_type: HandlerType = HandlerType.SYNC,
        filters: Optional[List[EventFilter]] = None,
        priority: int = 0,
        max_retries: int = 3,
    ):
        self.handler_func = handler_func
        self.handler_type = handler_type
        self.filters = filters or []
        self.priority = priority
        self.max_retries = max_retries
        self.call_count = 0
        self.error_count = 0
        self.total_time = 0.0
        self.last_called = None

    def can_handle(self, event: Event) -> bool:
        """检查是否可以处理事件"""
        for filter_obj in self.filters:
            if not filter_obj.should_process(event):
                return False
        return True

    async def handle_async(self, event: Event) -> Any:
        """异步处理事件"""
        start_time = time.time()
        try:
            self.call_count += 1
            self.last_called = start_time

            if self.handler_type == HandlerType.ASYNC:
                result = await self.handler_func(event)
            else:
                result = self.handler_func(event)

            self.total_time += time.time() - start_time
            return result
        except Exception as e:
            self.error_count += 1
            self.total_time += time.time() - start_time
            raise e

    def handle_sync(self, event: Event) -> Any:
        """同步处理事件"""
        start_time = time.time()
        try:
            self.call_count += 1
            self.last_called = start_time

            result = self.handler_func(event)
            self.total_time += time.time() - start_time
            return result
        except Exception as e:
            self.error_count += 1
            self.total_time += time.time() - start_time
            raise e

    def get_avg_time(self) -> float:
        """获取平均处理时间"""
        if self.call_count == 0:
            return 0.0
        return self.total_time / self.call_count


class EventPersistence(ABC):
    """事件持久化抽象基类"""

    @abstractmethod
    def save_event(self, event: Event) -> bool:
        """保存事件"""
        pass

    @abstractmethod
    def load_events(self, limit: Optional[int] = None) -> List[Event]:
        """加载事件"""
        pass

    @abstractmethod
    def clear_events(self) -> bool:
        """清空事件"""
        pass


class FilePersistence(EventPersistence):
    """文件持久化"""

    def __init__(self, file_path: str):
        self.file_path = Path(file_path)
        self.file_path.parent.mkdir(parents=True, exist_ok=True)
        self.lock = threading.Lock()

    def save_event(self, event: Event) -> bool:
        try:
            with self.lock:
                with open(self.file_path, "a", encoding="utf-8") as f:
                    json.dump(event.to_dict(), f, ensure_ascii=False)
                    f.write("\n")
            return True
        except Exception as e:
            logging.error(f"Failed to save event {event.id}: {e}")
            return False

    def load_events(self, limit: Optional[int] = None) -> List[Event]:
        events = []
        try:
            if not self.file_path.exists():
                return events

            with open(self.file_path, "r", encoding="utf-8") as f:
                for line_num, line in enumerate(f):
                    if limit and len(events) >= limit:
                        break

                    try:
                        data = json.loads(line.strip())
                        event = Event.from_dict(data)
                        events.append(event)
                    except json.JSONDecodeError as e:
                        logging.warning(
                            f"Failed to parse event at line {line_num + 1}: {e}"
                        )
                        continue
        except Exception as e:
            logging.error(f"Failed to load events: {e}")

        return events

    def clear_events(self) -> bool:
        try:
            if self.file_path.exists():
                self.file_path.unlink()
            return True
        except Exception as e:
            logging.error(f"Failed to clear events: {e}")
            return False


class EventManager:
    """统一事件管理器"""

    def __init__(self, config: EventConfig):
        self.config = config
        self.handlers: Dict[str, List[EventHandler]] = defaultdict(list)
        self.global_handlers: List[EventHandler] = []
        self.event_queue: deque = deque(maxlen=config.max_queue_size)
        self.delayed_queue: deque = deque()
        self.stats = EventStats()
        self.lock = threading.RLock()
        self.running = False
        self.worker_threads: List[threading.Thread] = []
        self.executor = ThreadPoolExecutor(max_workers=config.worker_threads)

        # 持久化
        self.persistence: Optional[EventPersistence] = None
        if config.enable_persistence and config.persistence_path:
            self.persistence = FilePersistence(config.persistence_path)

        # 重放事件存储
        self.replay_events: deque = deque(maxlen=config.max_replay_events)

        # 启动工作线程
        self.start()

    def start(self) -> None:
        """启动事件管理器"""
        if self.running:
            return

        self.running = True

        # 启动事件处理线程
        for i in range(self.config.worker_threads):
            thread = threading.Thread(
                target=self._event_worker, name=f"EventWorker-{i}", daemon=True
            )
            thread.start()
            self.worker_threads.append(thread)

        # 启动延迟事件处理线程
        delayed_thread = threading.Thread(
            target=self._delayed_event_worker, name="DelayedEventWorker", daemon=True
        )
        delayed_thread.start()
        self.worker_threads.append(delayed_thread)

        logging.info(f"Event manager started with {self.config.worker_threads} workers")

    def stop(self) -> None:
        """停止事件管理器"""
        self.running = False

        # 等待工作线程结束
        for thread in self.worker_threads:
            thread.join(timeout=5.0)

        # 关闭线程池
        self.executor.shutdown(wait=True)

        logging.info("Event manager stopped")

    def subscribe(
        self,
        event_name: str,
        handler: Callable,
        handler_type: HandlerType = HandlerType.SYNC,
        filters: Optional[List[EventFilter]] = None,
        priority: int = 0,
    ) -> str:
        """订阅事件"""
        handler_obj = EventHandler(
            handler_func=handler,
            handler_type=handler_type,
            filters=filters,
            priority=priority,
        )

        with self.lock:
            self.handlers[event_name].append(handler_obj)
            # 按优先级排序
            self.handlers[event_name].sort(key=lambda h: h.priority, reverse=True)
            self.stats.handler_count += 1

        handler_id = f"{event_name}:{id(handler_obj)}"
        logging.debug(f"Subscribed handler {handler_id} for event {event_name}")
        return handler_id

    def subscribe_global(
        self,
        handler: Callable,
        handler_type: HandlerType = HandlerType.SYNC,
        filters: Optional[List[EventFilter]] = None,
        priority: int = 0,
    ) -> str:
        """订阅全局事件（所有事件）"""
        handler_obj = EventHandler(
            handler_func=handler,
            handler_type=handler_type,
            filters=filters,
            priority=priority,
        )

        with self.lock:
            self.global_handlers.append(handler_obj)
            self.global_handlers.sort(key=lambda h: h.priority, reverse=True)
            self.stats.handler_count += 1

        handler_id = f"global:{id(handler_obj)}"
        logging.debug(f"Subscribed global handler {handler_id}")
        return handler_id

    def unsubscribe(self, event_name: str, handler_id: str) -> bool:
        """取消订阅事件"""
        with self.lock:
            if event_name in self.handlers:
                original_count = len(self.handlers[event_name])
                self.handlers[event_name] = [
                    h
                    for h in self.handlers[event_name]
                    if f"{event_name}:{id(h)}" != handler_id
                ]
                removed = original_count - len(self.handlers[event_name])
                if removed > 0:
                    self.stats.handler_count -= removed
                    return True
        return False

    def publish(
        self,
        event_name: str,
        data: Optional[Dict[str, Any]] = None,
        event_type: EventType = EventType.CUSTOM,
        priority: EventPriority = EventPriority.NORMAL,
        delay: Optional[float] = None,
        ttl: Optional[float] = None,
        **kwargs,
    ) -> str:
        """发布事件"""
        event = Event(
            name=event_name,
            type=event_type,
            priority=priority,
            data=data or {},
            **kwargs,
        )

        # 设置延迟
        if delay:
            event.delay_until = time.time() + delay

        # 设置过期时间
        if ttl:
            event.expires_at = time.time() + ttl

        return self.publish_event(event)

    def publish_event(self, event: Event) -> str:
        """发布事件对象"""
        if event.is_expired():
            logging.warning(f"Event {event.id} is already expired")
            return event.id

        with self.lock:
            if event.delay_until and not event.is_ready():
                self.delayed_queue.append(event)
            else:
                self.event_queue.append(event)

            self.stats.total_events += 1
            self.stats.pending_events += 1
            self.stats.queue_size = len(self.event_queue)

        # 持久化事件
        if self.persistence:
            self.persistence.save_event(event)

        logging.debug(f"Published event {event.name} with ID {event.id}")
        return event.id

    def _event_worker(self) -> None:
        """事件处理工作线程"""
        while self.running:
            try:
                event = None
                with self.lock:
                    if self.event_queue:
                        event = self.event_queue.popleft()
                        self.stats.pending_events -= 1
                        self.stats.queue_size = len(self.event_queue)

                if event:
                    self._process_event(event)
                else:
                    time.sleep(0.1)  # 没有事件时短暂休眠
            except Exception as e:
                logging.error(f"Error in event worker: {e}")
                time.sleep(1.0)

    def _delayed_event_worker(self) -> None:
        """延迟事件处理工作线程"""
        while self.running:
            try:
                ready_events = []
                with self.lock:
                    # 检查延迟队列中准备好的事件
                    remaining_events = deque()
                    while self.delayed_queue:
                        event = self.delayed_queue.popleft()
                        if event.is_expired():
                            continue  # 跳过过期事件
                        elif event.is_ready():
                            ready_events.append(event)
                        else:
                            remaining_events.append(event)

                    self.delayed_queue = remaining_events

                    # 将准备好的事件移到主队列
                    for event in ready_events:
                        self.event_queue.append(event)
                        self.stats.pending_events += 1

                    self.stats.queue_size = len(self.event_queue)

                time.sleep(1.0)  # 每秒检查一次延迟事件
            except Exception as e:
                logging.error(f"Error in delayed event worker: {e}")
                time.sleep(1.0)

    def _process_event(self, event: Event) -> None:
        """处理单个事件"""
        start_time = time.time()
        event.status = EventStatus.PROCESSING

        try:
            # 获取事件处理器
            handlers = self._get_handlers_for_event(event)

            if not handlers:
                logging.debug(f"No handlers found for event {event.name}")
                event.status = EventStatus.COMPLETED
                return

            # 处理事件
            for handler in handlers:
                try:
                    if handler.handler_type == HandlerType.ASYNC:
                        # 异步处理
                        future = self.executor.submit(
                            self._run_async_handler, handler, event
                        )
                        # 可以选择等待结果或继续处理其他处理器
                    elif handler.handler_type == HandlerType.BACKGROUND:
                        # 后台处理
                        self.executor.submit(handler.handle_sync, event)
                    else:
                        # 同步处理
                        handler.handle_sync(event)
                except Exception as e:
                    logging.error(f"Handler error for event {event.name}: {e}")
                    event.retry_count += 1

                    if event.retry_count < self.config.retry_attempts:
                        # 重试
                        event.delay_until = time.time() + self.config.retry_delay
                        event.status = EventStatus.PENDING
                        with self.lock:
                            self.delayed_queue.append(event)
                        return
                    else:
                        event.status = EventStatus.FAILED
                        self.stats.failed_events += 1
                        return

            event.status = EventStatus.COMPLETED
            self.stats.processed_events += 1

            # 添加到重放队列
            if self.config.enable_replay:
                with self.lock:
                    self.replay_events.append(event)

        except Exception as e:
            logging.error(f"Error processing event {event.name}: {e}")
            event.status = EventStatus.FAILED
            self.stats.failed_events += 1

        finally:
            # 更新统计信息
            processing_time = time.time() - start_time
            total_processed = self.stats.processed_events + self.stats.failed_events
            if total_processed > 0:
                self.stats.avg_processing_time = (
                    self.stats.avg_processing_time * (total_processed - 1)
                    + processing_time
                ) / total_processed

    def _run_async_handler(self, handler: EventHandler, event: Event) -> Any:
        """运行异步处理器"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(
                asyncio.wait_for(
                    handler.handle_async(event), timeout=self.config.async_timeout
                )
            )
        finally:
            loop.close()

    def _get_handlers_for_event(self, event: Event) -> List[EventHandler]:
        """获取事件的处理器"""
        handlers = []

        # 添加特定事件的处理器
        if event.name in self.handlers:
            for handler in self.handlers[event.name]:
                if handler.can_handle(event):
                    handlers.append(handler)

        # 添加全局处理器
        for handler in self.global_handlers:
            if handler.can_handle(event):
                handlers.append(handler)

        # 按优先级排序
        handlers.sort(key=lambda h: h.priority, reverse=True)
        return handlers

    def get_stats(self) -> EventStats:
        """获取统计信息"""
        with self.lock:
            self.stats.queue_size = len(self.event_queue)
            self.stats.calculate_rates()
        return self.stats

    def reset_stats(self) -> None:
        """重置统计信息"""
        with self.lock:
            self.stats = EventStats()

    def get_pending_events(self) -> List[Event]:
        """获取待处理事件"""
        with self.lock:
            return list(self.event_queue) + list(self.delayed_queue)

    def get_replay_events(self, limit: Optional[int] = None) -> List[Event]:
        """获取重放事件"""
        with self.lock:
            events = list(self.replay_events)
            if limit:
                events = events[-limit:]
            return events

    def replay_events(self, events: Optional[List[Event]] = None) -> int:
        """重放事件"""
        if not self.config.enable_replay:
            return 0

        if events is None:
            events = self.get_replay_events()

        count = 0
        for event in events:
            # 重置事件状态
            event.status = EventStatus.PENDING
            event.retry_count = 0
            event.delay_until = None

            self.publish_event(event)
            count += 1

        return count

    def clear_events(self) -> bool:
        """清空所有事件"""
        with self.lock:
            self.event_queue.clear()
            self.delayed_queue.clear()
            self.replay_events.clear()
            self.stats.pending_events = 0
            self.stats.queue_size = 0

        if self.persistence:
            return self.persistence.clear_events()

        return True


# 全局事件管理器实例
_event_manager: Optional[EventManager] = None
_event_lock = threading.Lock()


def get_event_manager(config: Optional[EventConfig] = None) -> EventManager:
    """获取事件管理器单例"""
    global _event_manager

    if _event_manager is None:
        with _event_lock:
            if _event_manager is None:
                _event_manager = EventManager(config or EventConfig())

    return _event_manager


def event_handler(
    event_name: str,
    handler_type: HandlerType = HandlerType.SYNC,
    filters: Optional[List[EventFilter]] = None,
    priority: int = 0,
):
    """事件处理器装饰器"""

    def decorator(func: Callable) -> Callable:
        manager = get_event_manager()
        manager.subscribe(
            event_name=event_name,
            handler=func,
            handler_type=handler_type,
            filters=filters,
            priority=priority,
        )
        return func

    return decorator


def global_event_handler(
    handler_type: HandlerType = HandlerType.SYNC,
    filters: Optional[List[EventFilter]] = None,
    priority: int = 0,
):
    """全局事件处理器装饰器"""

    def decorator(func: Callable) -> Callable:
        manager = get_event_manager()
        manager.subscribe_global(
            handler=func, handler_type=handler_type, filters=filters, priority=priority
        )
        return func

    return decorator


def publish_event(
    event_name: str,
    data: Optional[Dict[str, Any]] = None,
    event_type: EventType = EventType.CUSTOM,
    priority: EventPriority = EventPriority.NORMAL,
    **kwargs,
) -> str:
    """发布事件的便捷函数"""
    manager = get_event_manager()
    return manager.publish(
        event_name=event_name,
        data=data,
        event_type=event_type,
        priority=priority,
        **kwargs,
    )


def emit_event(event: Event) -> str:
    """发布事件对象的便捷函数"""
    manager = get_event_manager()
    return manager.publish_event(event)


def get_event_stats() -> EventStats:
    """获取事件统计信息"""
    manager = get_event_manager()
    return manager.get_stats()


def wait_for_event(
    event_name: str,
    timeout: Optional[float] = None,
    condition: Optional[Callable[[Event], bool]] = None,
) -> Optional[Event]:
    """等待特定事件"""
    result_event = None
    event_received = threading.Event()

    def handler(event: Event):
        nonlocal result_event
        if condition is None or condition(event):
            result_event = event
            event_received.set()

    manager = get_event_manager()
    handler_id = manager.subscribe(event_name, handler)

    try:
        if event_received.wait(timeout):
            return result_event
        return None
    finally:
        manager.unsubscribe(event_name, handler_id)
