"""
V4.0 Transformer预测器
基于先进Transformer架构的彩票预测系统
集成多任务学习、自适应采样和不确定性建模
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
import logging
from dataclasses import asdict
import json
from pathlib import Path

# TensorFlow导入（可选）
try:
    import tensorflow as tf
    from tensorflow import keras
    from tensorflow.keras import layers
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False
    tf = None
    keras = None
    layers = None

# 添加项目根目录到Python路径
import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '..', '..', '..')
if project_root not in sys.path:
    sys.path.insert(0, project_root)

try:
    from .v4_transformer_config import V4TransformerConfig, SamplingStrategy
except ImportError:
    from v4_transformer_config import V4TransformerConfig, SamplingStrategy

try:
    from src.core.interfaces import PredictionResult, PredictionType, BallType
except ImportError:
    try:
        from core.interfaces import PredictionResult, PredictionType, BallType
    except ImportError:
        # 最后的备用方案：定义简化版本
        from enum import Enum
        from dataclasses import dataclass
        from typing import Any, Dict

        class PredictionType(Enum):
            DEEP_LEARNING = "deep_learning"

        class BallType(Enum):
            RED = "red"
            BLUE = "blue"

        @dataclass
        class PredictionResult:
            prediction_type: PredictionType
            ball_type: BallType
            value: Any
            confidence: float
            metadata: Dict[str, Any] = None

            def __post_init__(self):
                if self.metadata is None:
                    self.metadata = {}

# 基础预测器类
class BasePredictor:
    """基础预测器类"""
    def __init__(self, name: str, prediction_type: PredictionType):
        self.name = name
        self.prediction_type = prediction_type
        self.version = "1.0"
        self.is_trained = False
        self.confidence_threshold = 0.5
        self.model_params = {}

    def predict(self, data: pd.DataFrame, target_index: int, **kwargs) -> PredictionResult:
        """预测接口（需要子类实现）"""
        raise NotImplementedError("子类必须实现predict方法")


# TensorFlow相关类定义（仅在TensorFlow可用时定义）
if TENSORFLOW_AVAILABLE:
    class PositionalEncoding(layers.Layer):
        """位置编码层"""

        def __init__(self, max_len: int, d_model: int, **kwargs):
            super().__init__(**kwargs)
            self.max_len = max_len
            self.d_model = d_model

            # 创建位置编码矩阵
            position = np.arange(max_len)[:, np.newaxis]
            div_term = np.exp(np.arange(0, d_model, 2) * -(np.log(10000.0) / d_model))

            pe = np.zeros((max_len, d_model))
            pe[:, 0::2] = np.sin(position * div_term)
            pe[:, 1::2] = np.cos(position * div_term)

            self.pe = tf.constant(pe, dtype=tf.float32)

        def call(self, x):
            seq_len = tf.shape(x)[1]
            return x + self.pe[:seq_len, :]
else:
    # TensorFlow不可用时的占位符类
    class PositionalEncoding:
        def __init__(self, *args, **kwargs):
            pass


class MultiHeadSelfAttention(layers.Layer):
    """多头自注意力层"""
    
    def __init__(self, d_model: int, num_heads: int, dropout_rate: float = 0.1, **kwargs):
        super().__init__(**kwargs)
        self.d_model = d_model
        self.num_heads = num_heads
        self.dropout_rate = dropout_rate
        
        assert d_model % num_heads == 0
        self.depth = d_model // num_heads
        
        self.wq = layers.Dense(d_model)
        self.wk = layers.Dense(d_model)
        self.wv = layers.Dense(d_model)
        self.dense = layers.Dense(d_model)
        self.dropout = layers.Dropout(dropout_rate)
    
    def split_heads(self, x, batch_size):
        x = tf.reshape(x, (batch_size, -1, self.num_heads, self.depth))
        return tf.transpose(x, perm=[0, 2, 1, 3])
    
    def call(self, x, training=None):
        batch_size = tf.shape(x)[0]
        
        q = self.wq(x)
        k = self.wk(x)
        v = self.wv(x)
        
        q = self.split_heads(q, batch_size)
        k = self.split_heads(k, batch_size)
        v = self.split_heads(v, batch_size)
        
        # 计算注意力
        matmul_qk = tf.matmul(q, k, transpose_b=True)
        dk = tf.cast(self.depth, tf.float32)
        scaled_attention_logits = matmul_qk / tf.math.sqrt(dk)
        
        attention_weights = tf.nn.softmax(scaled_attention_logits, axis=-1)
        attention_weights = self.dropout(attention_weights, training=training)
        
        attention = tf.matmul(attention_weights, v)
        attention = tf.transpose(attention, perm=[0, 2, 1, 3])
        
        concat_attention = tf.reshape(attention, (batch_size, -1, self.d_model))
        output = self.dense(concat_attention)
        
        return output, attention_weights


class CrossTaskAttention(layers.Layer):
    """跨任务注意力层"""
    
    def __init__(self, d_model: int, num_heads: int, **kwargs):
        super().__init__(**kwargs)
        self.attention = MultiHeadSelfAttention(d_model, num_heads)
        self.layer_norm = layers.LayerNormalization(epsilon=1e-6)
    
    def call(self, x, training=None):
        attn_output, _ = self.attention(x, training=training)
        return self.layer_norm(x + attn_output)


class FeedForward(layers.Layer):
    """前馈网络层"""
    
    def __init__(self, d_model: int, dff: int, dropout_rate: float = 0.1, **kwargs):
        super().__init__(**kwargs)
        self.dense1 = layers.Dense(dff, activation='relu')
        self.dense2 = layers.Dense(d_model)
        self.dropout = layers.Dropout(dropout_rate)
        self.layer_norm = layers.LayerNormalization(epsilon=1e-6)
    
    def call(self, x, training=None):
        ffn_output = self.dense1(x)
        ffn_output = self.dropout(ffn_output, training=training)
        ffn_output = self.dense2(ffn_output)
        return self.layer_norm(x + ffn_output)


class EnhancedTransformerBlock(layers.Layer):
    """增强Transformer块"""
    
    def __init__(self, d_model: int, num_heads: int, dff: int, dropout_rate: float = 0.1, **kwargs):
        super().__init__(**kwargs)
        self.self_attention = MultiHeadSelfAttention(d_model, num_heads, dropout_rate)
        self.cross_task_attention = CrossTaskAttention(d_model, num_heads)
        self.feed_forward = FeedForward(d_model, dff, dropout_rate)
        self.layer_norm1 = layers.LayerNormalization(epsilon=1e-6)
        self.layer_norm2 = layers.LayerNormalization(epsilon=1e-6)
        self.dropout1 = layers.Dropout(dropout_rate)
        self.dropout2 = layers.Dropout(dropout_rate)
    
    def call(self, x, training=None):
        # 自注意力
        attn_output, attention_weights = self.self_attention(x, training=training)
        attn_output = self.dropout1(attn_output, training=training)
        out1 = self.layer_norm1(x + attn_output)
        
        # 跨任务注意力
        cross_attn_output = self.cross_task_attention(out1, training=training)
        cross_attn_output = self.dropout2(cross_attn_output, training=training)
        out2 = self.layer_norm2(out1 + cross_attn_output)
        
        # 前馈网络
        ffn_output = self.feed_forward(out2, training=training)
        
        return ffn_output, attention_weights


class MultiTaskOutputHeads(layers.Layer):
    """多任务输出头"""
    
    def __init__(self, d_model: int, **kwargs):
        super().__init__(**kwargs)
        
        # 比例预测头
        self.red_odd_even_head = layers.Dense(6, activation='softmax', name='red_odd_even')
        self.red_size_head = layers.Dense(6, activation='softmax', name='red_size')
        self.blue_size_head = layers.Dense(2, activation='softmax', name='blue_size')
        
        # 号码生成头
        self.red_number_head = layers.Dense(35, activation='sigmoid', name='red_numbers')
        self.blue_number_head = layers.Dense(12, activation='sigmoid', name='blue_numbers')
        
        # 不确定性估计头
        self.uncertainty_head = layers.Dense(1, activation='sigmoid', name='uncertainty')
        
        # 全局池化层
        self.global_pool = layers.GlobalAveragePooling1D()
    
    def call(self, x, training=None):
        # 全局池化
        pooled = self.global_pool(x)
        
        # 多任务输出
        outputs = {
            'red_odd_even': self.red_odd_even_head(pooled),
            'red_size': self.red_size_head(pooled),
            'blue_size': self.blue_size_head(pooled),
            'red_numbers': self.red_number_head(pooled),
            'blue_numbers': self.blue_number_head(pooled),
            'uncertainty': self.uncertainty_head(pooled)
        }
        
        return outputs


class V4TransformerModel(keras.Model):
    """V4.0 Transformer模型"""
    
    def __init__(self, config: V4TransformerConfig, **kwargs):
        super().__init__(**kwargs)
        self.config = config
        
        # 输入处理
        self.input_projection = layers.Dense(config.d_model)
        self.positional_encoding = PositionalEncoding(
            config.max_position_encoding, 
            config.d_model
        )
        self.input_dropout = layers.Dropout(config.dropout_rate)
        
        # Transformer块
        self.transformer_blocks = [
            EnhancedTransformerBlock(
                config.d_model,
                config.num_heads,
                config.dff,
                config.dropout_rate
            ) for _ in range(config.num_layers)
        ]
        
        # 输出头
        self.output_heads = MultiTaskOutputHeads(config.d_model)
    
    def call(self, inputs, training=None):
        # 输入处理
        x = self.input_projection(inputs)
        x = self.positional_encoding(x)
        x = self.input_dropout(x, training=training)
        
        # Transformer块
        attention_weights = []
        for transformer_block in self.transformer_blocks:
            x, attn_weights = transformer_block(x, training=training)
            attention_weights.append(attn_weights)
        
        # 多任务输出
        outputs = self.output_heads(x, training=training)
        
        if training:
            return outputs
        else:
            return outputs, attention_weights


class V4TransformerPredictor(BasePredictor):
    """V4.0 Transformer预测器"""
    
    def __init__(self, config: Optional[V4TransformerConfig] = None):
        super().__init__("V4.0 Transformer预测器", PredictionType.DEEP_LEARNING)
        
        self.config = config or V4TransformerConfig()
        self.model = None
        self.is_trained = False
        self.feature_scaler = None
        self.training_history = []
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        
        # 验证配置
        if not self.config.validate():
            raise ValueError("V4 Transformer配置验证失败")
    
    def _build_model(self) -> V4TransformerModel:
        """构建模型"""
        model = V4TransformerModel(self.config)
        
        # 编译模型
        optimizer = keras.optimizers.Adam(
            learning_rate=self.config.learning_rate,
            beta_1=self.config.beta_1,
            beta_2=self.config.beta_2,
            epsilon=self.config.epsilon
        )
        
        # 多任务损失
        losses = {
            'red_odd_even': 'categorical_crossentropy',
            'red_size': 'categorical_crossentropy',
            'blue_size': 'categorical_crossentropy',
            'red_numbers': 'binary_crossentropy',
            'blue_numbers': 'binary_crossentropy',
            'uncertainty': 'mse'
        }
        
        model.compile(
            optimizer=optimizer,
            loss=losses,
            loss_weights=self.config.loss_weights,
            metrics=['accuracy']
        )
        
        return model
    
    def predict(self, data: pd.DataFrame, target_index: int, **kwargs) -> PredictionResult:
        """实现预测接口"""
        try:
            if not self.is_trained or not TENSORFLOW_AVAILABLE:
                self.logger.warning("模型未训练或TensorFlow不可用，使用随机预测")
                return self._random_prediction()

            # 准备输入数据
            input_data = self._prepare_input_data(data, target_index)

            if input_data is None:
                self.logger.error("输入数据准备失败")
                return self._random_prediction()

            # Monte Carlo预测
            predictions = self._monte_carlo_predict(input_data)

            # 解析预测结果
            result = self._parse_predictions(predictions)

            return result

        except Exception as e:
            self.logger.error(f"V4 Transformer预测失败: {e}")
            return self._random_prediction()
    
    def _prepare_input_data(self, data: pd.DataFrame, target_index: int) -> np.ndarray:
        """准备输入数据"""
        # 提取序列数据
        start_idx = max(0, target_index - self.config.sequence_length)
        sequence_data = data.iloc[start_idx:target_index]
        
        # 特征工程（简化版本）
        features = []
        for _, row in sequence_data.iterrows():
            # 基础特征
            red_balls = [row[f'红球{i}'] for i in range(1, 6)]
            blue_balls = [row[f'蓝球{i}'] for i in range(1, 3)]
            
            # 计算比例特征
            red_odd_count = sum(1 for x in red_balls if x % 2 == 1)
            red_large_count = sum(1 for x in red_balls if x > 17)
            blue_large_count = sum(1 for x in blue_balls if x > 7)
            
            period_features = [
                red_odd_count / 5,  # 奇数比例
                red_large_count / 5,  # 大数比例
                blue_large_count / 2,  # 蓝球大数比例
            ] + red_balls + blue_balls
            
            features.append(period_features)
        
        # 填充到固定长度
        while len(features) < self.config.sequence_length:
            features.insert(0, features[0] if features else [0] * 10)
        
        return np.array([features])  # 添加batch维度
    
    def _monte_carlo_predict(self, input_data: np.ndarray) -> Dict[str, np.ndarray]:
        """Monte Carlo预测"""
        predictions = {key: [] for key in ['red_odd_even', 'red_size', 'blue_size', 
                                         'red_numbers', 'blue_numbers', 'uncertainty']}
        
        for _ in range(self.config.monte_carlo_samples):
            pred = self.model(input_data, training=True)
            for key in predictions:
                predictions[key].append(pred[key].numpy())
        
        # 计算均值和不确定性
        result = {}
        for key, values in predictions.items():
            result[key] = np.mean(values, axis=0)
            result[f'{key}_std'] = np.std(values, axis=0)
        
        return result
    
    def _parse_predictions(self, predictions: Dict[str, np.ndarray]) -> PredictionResult:
        """解析预测结果"""
        try:
            # 比例预测
            red_odd_even_idx = np.argmax(predictions['red_odd_even'][0])
            red_size_idx = np.argmax(predictions['red_size'][0])
            blue_size_idx = np.argmax(predictions['blue_size'][0])

            # 转换为比例格式
            red_odd_even_ratio = f"{red_odd_even_idx}:{5-red_odd_even_idx}"
            red_size_ratio = f"{red_size_idx}:{5-red_size_idx}"
            blue_size_ratio = f"{blue_size_idx}:{2-blue_size_idx}"

            # 号码生成（选择概率最高的号码）
            red_probs = predictions['red_numbers'][0]
            blue_probs = predictions['blue_numbers'][0]

            red_numbers = np.argsort(red_probs)[-5:] + 1  # 选择概率最高的5个红球
            blue_numbers = np.argsort(blue_probs)[-2:] + 1  # 选择概率最高的2个蓝球

            # 构建结果字典
            result_dict = {
                'red_odd_even_ratio': red_odd_even_ratio,
                'red_size_ratio': red_size_ratio,
                'blue_size_ratio': blue_size_ratio,
                'red_ball_candidates': sorted(red_numbers.tolist()),
                'blue_ball_candidates': sorted(blue_numbers.tolist()),
                'predicted_hit_rate': float(1.0 - predictions['uncertainty'][0][0])
            }

            # 计算置信度
            confidence = float(1.0 - predictions['uncertainty'][0][0])
            confidence = max(0.1, min(0.95, confidence))

            return PredictionResult(
                prediction_type=PredictionType.DEEP_LEARNING,
                ball_type=BallType.RED,
                value=result_dict,
                confidence=confidence,
                metadata={
                    "success": True,
                    "predictions": result_dict,
                    "model_type": "V4.0 Transformer",
                    "method": "transformer_deep_learning"
                }
            )

        except Exception as e:
            self.logger.error(f"预测结果解析失败: {e}")
            return self._random_prediction()
    
    def _random_prediction(self) -> 'PredictionResult':
        """随机预测（备用方案）"""
        import random

        red_odd_count = random.randint(0, 5)
        red_large_count = random.randint(0, 5)

        result_dict = {
            'red_odd_even_ratio': f"{red_odd_count}:{5-red_odd_count}",
            'red_size_ratio': f"{red_large_count}:{5-red_large_count}",
            'blue_size_ratio': random.choice(["大", "小"]),
            'predicted_numbers': {
                'red': sorted(random.sample(range(1, 36), 5)),
                'blue': sorted(random.sample(range(1, 13), 2))
            }
        }

        # 使用与V3.2相同的PredictionResult格式
        from src.core.interfaces import PredictionResult, PredictionType, BallType

        return PredictionResult(
            prediction_type=PredictionType.DEEP_LEARNING,
            ball_type=BallType.RED,
            value=result_dict,
            confidence=0.1,
            metadata={
                "success": True,
                "predictions": result_dict,
                "model_type": "V4.0 Transformer (Random Fallback)",
                "method": "random_fallback"
            }
        )

    def train(self, data: pd.DataFrame, **kwargs) -> bool:
        """训练模型"""
        try:
            self.logger.info("开始V4.0 Transformer模型训练...")

            # 检查TensorFlow可用性
            if not self._check_tensorflow():
                self.logger.warning("TensorFlow不可用，跳过训练")
                return False

            # 准备训练数据
            X, y = self._prepare_training_data(data)

            if X is None or len(X) == 0:
                self.logger.error("训练数据准备失败")
                return False

            # 构建模型
            if self.model is None:
                self.model = self._build_model()

            # 设置回调函数
            callbacks = self._setup_callbacks()

            # 训练模型
            history = self.model.fit(
                X, y,
                batch_size=self.config.batch_size,
                epochs=self.config.epochs,
                validation_split=self.config.validation_split,
                callbacks=callbacks,
                verbose=1 if self.config.verbose_training else 0
            )

            self.training_history.append(history.history)
            self.is_trained = True

            self.logger.info(f"V4.0 Transformer训练完成，训练数据量: {len(X)}")
            return True

        except Exception as e:
            self.logger.error(f"V4.0 Transformer训练失败: {e}")
            return False

    def _check_tensorflow(self) -> bool:
        """检查TensorFlow可用性"""
        try:
            import tensorflow as tf
            return True
        except ImportError:
            return False

    def _prepare_training_data(self, data: pd.DataFrame) -> Tuple[Optional[np.ndarray], Optional[Dict[str, np.ndarray]]]:
        """准备训练数据"""
        try:
            sequences = []
            targets = {
                'red_odd_even': [],
                'red_size': [],
                'blue_size': [],
                'red_numbers': [],
                'blue_numbers': [],
                'uncertainty': []
            }

            seq_len = self.config.sequence_length

            for i in range(seq_len, len(data)):
                # 输入序列
                sequence_features = []
                for j in range(i - seq_len, i):
                    row = data.iloc[j]

                    # 提取红球和蓝球
                    red_balls = [row[f'红球{k}'] for k in range(1, 6)]
                    blue_balls = [row[f'蓝球{k}'] for k in range(1, 3)]

                    # 计算特征
                    red_odd_count = sum(1 for x in red_balls if x % 2 == 1)
                    red_large_count = sum(1 for x in red_balls if x > 17)
                    blue_large_count = sum(1 for x in blue_balls if x > 7)

                    period_features = [
                        red_odd_count / 5,      # 奇数比例
                        red_large_count / 5,    # 大数比例
                        blue_large_count / 2,   # 蓝球大数比例
                    ] + red_balls + blue_balls

                    sequence_features.append(period_features)

                sequences.append(sequence_features)

                # 目标值（下一期的结果）
                target_row = data.iloc[i]
                target_red_balls = [target_row[f'红球{k}'] for k in range(1, 6)]
                target_blue_balls = [target_row[f'蓝球{k}'] for k in range(1, 3)]

                # 比例目标
                target_red_odd = sum(1 for x in target_red_balls if x % 2 == 1)
                target_red_large = sum(1 for x in target_red_balls if x > 17)
                target_blue_large = sum(1 for x in target_blue_balls if x > 7)

                targets['red_odd_even'].append(target_red_odd)
                targets['red_size'].append(target_red_large)
                targets['blue_size'].append(target_blue_large)

                # 号码目标（one-hot编码）
                red_target = np.zeros(35)
                blue_target = np.zeros(12)

                for ball in target_red_balls:
                    red_target[ball - 1] = 1
                for ball in target_blue_balls:
                    blue_target[ball - 1] = 1

                targets['red_numbers'].append(red_target)
                targets['blue_numbers'].append(blue_target)

                # 不确定性目标（简化为随机值）
                targets['uncertainty'].append(np.random.random())

            if not sequences:
                return None, None

            X = np.array(sequences)

            # 转换目标为适当格式
            y = {}
            for task in ['red_odd_even', 'red_size']:
                # 分类任务：转换为one-hot
                y[task] = tf.keras.utils.to_categorical(targets[task], num_classes=6)

            y['blue_size'] = tf.keras.utils.to_categorical(targets['blue_size'], num_classes=2)
            y['red_numbers'] = np.array(targets['red_numbers'])
            y['blue_numbers'] = np.array(targets['blue_numbers'])
            y['uncertainty'] = np.array(targets['uncertainty']).reshape(-1, 1)

            return X, y

        except Exception as e:
            self.logger.error(f"训练数据准备失败: {e}")
            return None, None

    def _setup_callbacks(self) -> List:
        """设置训练回调函数"""
        callbacks = []

        # 早停
        early_stopping = tf.keras.callbacks.EarlyStopping(
            monitor='val_loss',
            patience=self.config.early_stopping_patience,
            restore_best_weights=True,
            verbose=1
        )
        callbacks.append(early_stopping)

        # 学习率调度
        lr_scheduler = tf.keras.callbacks.ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.5,
            patience=5,
            min_lr=1e-7,
            verbose=1
        )
        callbacks.append(lr_scheduler)

        # 模型检查点
        if self.config.model_save_path:
            checkpoint = tf.keras.callbacks.ModelCheckpoint(
                filepath=f"{self.config.model_save_path}/v4_transformer_best.h5",
                monitor='val_loss',
                save_best_only=self.config.save_best_only,
                save_weights_only=self.config.save_weights_only,
                verbose=1
            )
            callbacks.append(checkpoint)

        return callbacks

    def _adaptive_temperature_sampling(self, predictions: Dict[str, np.ndarray],
                                     uncertainty: float, diversity_score: float) -> Dict[str, np.ndarray]:
        """自适应温度采样"""
        try:
            # 计算自适应温度
            uncertainty_factor = uncertainty * 0.7
            diversity_factor = (1.0 - diversity_score) * 0.3

            adaptive_temp = (self.config.base_temperature +
                           uncertainty_factor + diversity_factor)
            adaptive_temp = np.clip(adaptive_temp, *self.config.temperature_range)

            # 应用温度采样
            sampled_predictions = {}

            for task, pred in predictions.items():
                if task in ['red_odd_even', 'red_size', 'blue_size']:
                    # 分类任务：应用温度softmax
                    logits = np.log(pred + 1e-8) / adaptive_temp
                    exp_logits = np.exp(logits - np.max(logits))
                    sampled_predictions[task] = exp_logits / np.sum(exp_logits)
                else:
                    # 回归任务：添加温度调节的噪声
                    noise_scale = adaptive_temp * 0.1
                    noise = np.random.normal(0, noise_scale, pred.shape)
                    sampled_predictions[task] = pred + noise

            return sampled_predictions

        except Exception as e:
            self.logger.error(f"自适应温度采样失败: {e}")
            return predictions

    def _calculate_diversity_score(self, current_prediction: Dict[str, Any],
                                 history: List[Dict[str, Any]]) -> float:
        """计算预测多样性分数"""
        if not history:
            return 1.0

        try:
            # 比较当前预测与历史预测的相似度
            similarities = []

            for hist_pred in history[-5:]:  # 只考虑最近5次预测
                similarity = 0.0
                count = 0

                # 比较比例预测
                for key in ['red_odd_even_ratio', 'red_size_ratio', 'blue_size_ratio']:
                    if key in current_prediction and key in hist_pred:
                        if current_prediction[key] == hist_pred[key]:
                            similarity += 1.0
                        count += 1

                if count > 0:
                    similarities.append(similarity / count)

            if not similarities:
                return 1.0

            # 多样性分数 = 1 - 平均相似度
            avg_similarity = np.mean(similarities)
            diversity_score = 1.0 - avg_similarity

            return max(0.0, min(1.0, diversity_score))

        except Exception as e:
            self.logger.error(f"多样性分数计算失败: {e}")
            return 0.5

    def _enhanced_sampling(self, predictions: Dict[str, np.ndarray]) -> Dict[str, Any]:
        """增强采样策略"""
        try:
            sampled_results = {}

            # 比例预测采样
            for task in ['red_odd_even', 'red_size', 'blue_size']:
                if task in predictions:
                    probs = predictions[task][0]

                    if self.config.sampling_strategy == SamplingStrategy.TOP_K:
                        # Top-K采样
                        top_k_indices = np.argsort(probs)[-self.config.top_k:]
                        sampled_idx = np.random.choice(top_k_indices, p=probs[top_k_indices]/np.sum(probs[top_k_indices]))
                    elif self.config.sampling_strategy == SamplingStrategy.NUCLEUS:
                        # Nucleus采样
                        sorted_indices = np.argsort(probs)[::-1]
                        cumsum_probs = np.cumsum(probs[sorted_indices])
                        nucleus_indices = sorted_indices[cumsum_probs <= self.config.nucleus_p]
                        if len(nucleus_indices) == 0:
                            nucleus_indices = [sorted_indices[0]]
                        sampled_idx = np.random.choice(nucleus_indices, p=probs[nucleus_indices]/np.sum(probs[nucleus_indices]))
                    else:
                        # 标准采样
                        sampled_idx = np.random.choice(len(probs), p=probs)

                    sampled_results[task] = sampled_idx

            # 号码生成采样
            for task in ['red_numbers', 'blue_numbers']:
                if task in predictions:
                    probs = predictions[task][0]

                    # 选择概率最高的号码，但添加随机性
                    num_select = 5 if task == 'red_numbers' else 2
                    num_candidates = min(len(probs), num_select * 3)  # 候选数量

                    top_indices = np.argsort(probs)[-num_candidates:]
                    candidate_probs = probs[top_indices]
                    candidate_probs = candidate_probs / np.sum(candidate_probs)

                    selected_indices = np.random.choice(
                        top_indices,
                        size=num_select,
                        replace=False,
                        p=candidate_probs
                    )

                    sampled_results[task] = sorted(selected_indices + 1)  # 转换为1-based

            return sampled_results

        except Exception as e:
            self.logger.error(f"增强采样失败: {e}")
            return {}

    def _history_avoidance(self, predictions: Dict[str, Any],
                          history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """历史避免机制"""
        if not history or self.config.history_avoidance_strength == 0:
            return predictions

        try:
            adjusted_predictions = predictions.copy()

            # 统计历史预测模式
            pattern_counts = {}
            for hist_pred in history[-10:]:  # 考虑最近10次预测
                for key in ['red_odd_even_ratio', 'red_size_ratio', 'blue_size_ratio']:
                    if key in hist_pred:
                        pattern = hist_pred[key]
                        pattern_counts[pattern] = pattern_counts.get(pattern, 0) + 1

            # 对频繁出现的模式进行惩罚
            avoidance_strength = self.config.history_avoidance_strength

            for key in ['red_odd_even_ratio', 'red_size_ratio', 'blue_size_ratio']:
                if key in adjusted_predictions:
                    current_pattern = adjusted_predictions[key]
                    if current_pattern in pattern_counts:
                        penalty = pattern_counts[current_pattern] * avoidance_strength

                        # 如果当前预测模式出现过多，尝试调整
                        if penalty > 0.3:  # 阈值
                            # 简单的调整策略：随机选择其他模式
                            if key in ['red_odd_even_ratio', 'red_size_ratio']:
                                alternatives = [f"{i}:{5-i}" for i in range(6)]
                            else:  # blue_size_ratio
                                alternatives = ["大", "小"]

                            alternatives = [alt for alt in alternatives if alt != current_pattern]
                            if alternatives:
                                adjusted_predictions[key] = np.random.choice(alternatives)

            return adjusted_predictions

        except Exception as e:
            self.logger.error(f"历史避免机制失败: {e}")
            return predictions

    def save_model(self, filepath: str) -> bool:
        """保存模型"""
        try:
            if self.model is None:
                self.logger.error("模型未初始化，无法保存")
                return False

            # 创建保存目录
            save_path = Path(filepath)
            save_path.parent.mkdir(parents=True, exist_ok=True)

            # 保存模型
            self.model.save(str(save_path))

            # 保存配置
            config_path = save_path.parent / "config.json"
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config.to_dict(), f, indent=2, ensure_ascii=False)

            self.logger.info(f"V4.0 Transformer模型已保存到: {filepath}")
            return True

        except Exception as e:
            self.logger.error(f"模型保存失败: {e}")
            return False

    def load_model(self, filepath: str) -> bool:
        """加载模型"""
        try:
            if not self._check_tensorflow():
                self.logger.error("TensorFlow不可用，无法加载模型")
                return False

            model_path = Path(filepath)
            if not model_path.exists():
                self.logger.error(f"模型文件不存在: {filepath}")
                return False

            # 加载模型
            self.model = tf.keras.models.load_model(str(model_path))

            # 加载配置
            config_path = model_path.parent / "config.json"
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_dict = json.load(f)
                    self.config.update_from_dict(config_dict)

            self.is_trained = True
            self.logger.info(f"V4.0 Transformer模型已加载: {filepath}")
            return True

        except Exception as e:
            self.logger.error(f"模型加载失败: {e}")
            return False

    def get_model_summary(self) -> Dict[str, Any]:
        """获取模型摘要信息"""
        summary = {
            "model_name": self.name,
            "model_type": "V4.0 Transformer",
            "is_trained": self.is_trained,
            "config": self.config.get_model_summary()
        }

        if self.model is not None:
            try:
                # 获取模型参数数量
                total_params = self.model.count_params()
                summary["total_parameters"] = total_params

                # 获取训练历史
                if self.training_history:
                    last_history = self.training_history[-1]
                    summary["last_training"] = {
                        "epochs": len(last_history.get('loss', [])),
                        "final_loss": last_history.get('loss', [])[-1] if last_history.get('loss') else None,
                        "final_val_loss": last_history.get('val_loss', [])[-1] if last_history.get('val_loss') else None
                    }
            except Exception as e:
                self.logger.warning(f"获取模型详细信息失败: {e}")

        return summary
