"""
历史数据模式分析器
深入分析彩票历史数据，发现隐藏的模式和规律
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any
from collections import Counter, defaultdict
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.utils.utils import (
    parse_numbers, calculate_odd_even_ratio, calculate_size_ratio_red, 
    calculate_size_ratio_blue, ratio_to_state, get_all_red_states
)


class HistoricalPatternAnalyzer:
    """历史数据模式分析器"""
    
    def __init__(self):
        self.data = None
        self.analysis_results = {}
        
    def load_data(self, data: pd.DataFrame):
        """加载历史数据"""
        self.data = data.copy()
        print(f"[数据] 加载历史数据: {len(data)} 期")
        
    def analyze_red_odd_even_patterns(self) -> Dict[str, Any]:
        """分析红球奇偶比模式"""
        if self.data is None:
            return {}
        
        print("🔍 分析红球奇偶比模式...")
        
        # 提取所有奇偶比状态
        states = []
        for _, row in self.data.iterrows():
            red_balls, _ = parse_numbers(row)
            red_odd, red_even = calculate_odd_even_ratio(red_balls)
            state = ratio_to_state((red_odd, red_even))
            states.append(state)
        
        # 基础统计
        state_counts = Counter(states)
        total_count = len(states)
        
        # 频率分析
        frequency_analysis = {}
        for state in get_all_red_states():
            count = state_counts.get(state, 0)
            frequency = count / total_count if total_count > 0 else 0
            frequency_analysis[state] = {
                'count': count,
                'frequency': frequency,
                'percentage': frequency * 100
            }
        
        # 转移概率分析
        transition_matrix = defaultdict(lambda: defaultdict(int))
        for i in range(len(states) - 1):
            current = states[i]
            next_state = states[i + 1]
            transition_matrix[current][next_state] += 1
        
        # 标准化转移概率
        transition_probabilities = {}
        for current_state in transition_matrix:
            total_transitions = sum(transition_matrix[current_state].values())
            transition_probabilities[current_state] = {}
            for next_state in transition_matrix[current_state]:
                prob = transition_matrix[current_state][next_state] / total_transitions
                transition_probabilities[current_state][next_state] = prob
        
        # 连续状态分析
        consecutive_analysis = self._analyze_consecutive_states(states)
        
        # 周期性分析
        periodicity_analysis = self._analyze_periodicity(states)
        
        # 最近趋势分析
        recent_trend = self._analyze_recent_trend(states, window=20)
        
        results = {
            'frequency_analysis': frequency_analysis,
            'transition_probabilities': transition_probabilities,
            'consecutive_analysis': consecutive_analysis,
            'periodicity_analysis': periodicity_analysis,
            'recent_trend': recent_trend,
            'total_periods': total_count,
            'unique_states': len(state_counts),
            'most_common_state': state_counts.most_common(1)[0] if state_counts else None,
            'least_common_state': state_counts.most_common()[-1] if state_counts else None
        }
        
        self.analysis_results['red_odd_even'] = results
        return results
    
    def _analyze_consecutive_states(self, states: List[str]) -> Dict[str, Any]:
        """分析连续状态模式"""
        consecutive_counts = defaultdict(int)
        current_state = None
        current_count = 0
        
        for state in states:
            if state == current_state:
                current_count += 1
            else:
                if current_state is not None:
                    consecutive_counts[f"{current_state}_{current_count}"] += 1
                current_state = state
                current_count = 1
        
        # 处理最后一个序列
        if current_state is not None:
            consecutive_counts[f"{current_state}_{current_count}"] += 1
        
        # 分析每个状态的最大连续次数
        max_consecutive = defaultdict(int)
        for key, count in consecutive_counts.items():
            state, length = key.rsplit('_', 1)
            length = int(length)
            max_consecutive[state] = max(max_consecutive[state], length)
        
        return {
            'consecutive_counts': dict(consecutive_counts),
            'max_consecutive': dict(max_consecutive)
        }
    
    def _analyze_periodicity(self, states: List[str], max_period: int = 50) -> Dict[str, Any]:
        """分析周期性模式"""
        periodicity_scores = {}
        
        for period in range(2, min(max_period, len(states) // 4)):
            score = 0
            comparisons = 0
            
            for i in range(len(states) - period):
                if states[i] == states[i + period]:
                    score += 1
                comparisons += 1
            
            if comparisons > 0:
                periodicity_scores[period] = score / comparisons
        
        # 找出最强的周期性
        best_period = None
        best_score = 0
        if periodicity_scores:
            best_period = max(periodicity_scores.items(), key=lambda x: x[1])
            best_score = best_period[1]
            best_period = best_period[0]
        
        return {
            'periodicity_scores': periodicity_scores,
            'best_period': best_period,
            'best_score': best_score
        }
    
    def _analyze_recent_trend(self, states: List[str], window: int = 20) -> Dict[str, Any]:
        """分析最近趋势"""
        if len(states) < window:
            return {'insufficient_data': True}
        
        recent_states = states[-window:]
        recent_counts = Counter(recent_states)
        
        # 与整体分布比较
        overall_counts = Counter(states)
        total_count = len(states)
        
        trend_analysis = {}
        for state in get_all_red_states():
            recent_freq = recent_counts.get(state, 0) / window
            overall_freq = overall_counts.get(state, 0) / total_count
            
            trend_analysis[state] = {
                'recent_frequency': recent_freq,
                'overall_frequency': overall_freq,
                'trend_ratio': recent_freq / overall_freq if overall_freq > 0 else 0,
                'is_trending_up': recent_freq > overall_freq * 1.2,
                'is_trending_down': recent_freq < overall_freq * 0.8
            }
        
        return trend_analysis
    
    def analyze_correlations(self) -> Dict[str, Any]:
        """分析不同特征之间的关联性"""
        if self.data is None:
            return {}
        
        print("🔍 分析特征关联性...")
        
        # 提取各种特征
        features = []
        for _, row in self.data.iterrows():
            red_balls, blue_balls = parse_numbers(row)
            
            # 红球特征
            red_odd, red_even = calculate_odd_even_ratio(red_balls)
            red_small, red_large = calculate_size_ratio_red(red_balls)
            red_sum = sum(red_balls)
            red_span = max(red_balls) - min(red_balls)
            
            # 蓝球特征
            blue_small, blue_large = calculate_size_ratio_blue(blue_balls)
            blue_sum = sum(blue_balls)
            
            features.append({
                'red_odd_even': ratio_to_state((red_odd, red_even)),
                'red_size': ratio_to_state((red_small, red_large)),
                'blue_size': ratio_to_state((blue_small, blue_large)),
                'red_sum': red_sum,
                'red_span': red_span,
                'blue_sum': blue_sum
            })
        
        # 分析红球奇偶比与其他特征的关联
        correlations = {}
        
        # 与红球大小比的关联
        odd_even_size_correlation = defaultdict(lambda: defaultdict(int))
        for feature in features:
            odd_even_size_correlation[feature['red_odd_even']][feature['red_size']] += 1
        
        correlations['red_odd_even_vs_red_size'] = dict(odd_even_size_correlation)
        
        # 与红球和值的关联
        odd_even_sum_ranges = defaultdict(list)
        for feature in features:
            odd_even_sum_ranges[feature['red_odd_even']].append(feature['red_sum'])
        
        sum_analysis = {}
        for state, sums in odd_even_sum_ranges.items():
            if sums:
                sum_analysis[state] = {
                    'mean': np.mean(sums),
                    'std': np.std(sums),
                    'min': min(sums),
                    'max': max(sums)
                }
        
        correlations['red_odd_even_vs_sum'] = sum_analysis
        
        return correlations
    
    def generate_insights(self) -> List[str]:
        """生成数据洞察"""
        insights = []
        
        if 'red_odd_even' in self.analysis_results:
            analysis = self.analysis_results['red_odd_even']
            
            # 频率洞察
            freq_analysis = analysis['frequency_analysis']
            most_common = max(freq_analysis.items(), key=lambda x: x[1]['frequency'])
            least_common = min(freq_analysis.items(), key=lambda x: x[1]['frequency'])
            
            insights.append(f"[数据] 最常见的红球奇偶比: {most_common[0]} ({most_common[1]['percentage']:.1f}%)")
            insights.append(f"[数据] 最少见的红球奇偶比: {least_common[0]} ({least_common[1]['percentage']:.1f}%)")
            
            # 周期性洞察
            if analysis['periodicity_analysis']['best_period']:
                period = analysis['periodicity_analysis']['best_period']
                score = analysis['periodicity_analysis']['best_score']
                insights.append(f"[循环] 发现周期性模式: 每{period}期重复概率{score:.1%}")
            
            # 趋势洞察
            recent_trend = analysis['recent_trend']
            trending_up = [state for state, data in recent_trend.items() 
                          if isinstance(data, dict) and data.get('is_trending_up', False)]
            if trending_up:
                insights.append(f"[提升] 近期上升趋势: {', '.join(trending_up)}")
        
        return insights
    
    def print_analysis_summary(self):
        """打印分析摘要"""
        print("\n" + "="*80)
        print("[数据] 历史数据模式分析报告")
        print("="*80)
        
        insights = self.generate_insights()
        for insight in insights:
            print(insight)
        
        if 'red_odd_even' in self.analysis_results:
            analysis = self.analysis_results['red_odd_even']
            print(f"\n[提升] 数据统计:")
            print(f"   总期数: {analysis['total_periods']}")
            print(f"   状态种类: {analysis['unique_states']}")
            
            print(f"\n[数据] 频率分布:")
            freq_analysis = analysis['frequency_analysis']
            for state in sorted(freq_analysis.keys()):
                data = freq_analysis[state]
                print(f"   {state}: {data['count']}次 ({data['percentage']:.1f}%)")
        
        print("="*80)
