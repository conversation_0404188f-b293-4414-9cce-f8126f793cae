#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Demo script for the interactive launcher
"""

import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def demo_launcher():
    """Demonstrate the launcher functionality"""
    import launcher
    
    print("=" * 80)
    print("DEMO: Interactive Launcher Functionality")
    print("=" * 80)
    
    print("\n1. System Banner:")
    launcher.display_banner()
    
    print("\n2. Main Menu:")
    launcher.display_main_menu()
    
    print("\n3. Action Menu:")
    launcher.display_action_menu()
    
    print("\n4. Mode Menu (for Ultimate System):")
    launcher.display_mode_menu()
    
    print("\n5. Available Systems Test:")
    systems_to_test = [
        ("Main System", "src.systems.main", "LotteryPredictor"),
        ("Ultimate System", "src.systems.main_ultimate_fixed", "UltimateLotteryPredictor"),
        ("Refactored System", "src.systems.refactored_main", "RefactoredLotterySystem"),
    ]
    
    for system_name, module_name, class_name in systems_to_test:
        try:
            module = __import__(module_name, fromlist=[class_name])
            system_class = getattr(module, class_name)
            print(f"   [OK] {system_name} - Available")
        except Exception as e:
            print(f"   [FAIL] {system_name} - Not available: {e}")
    
    print("\n" + "=" * 80)
    print("USAGE INSTRUCTIONS:")
    print("=" * 80)
    print("To use the interactive launcher, run:")
    print("   python launcher.py")
    print()
    print("Features:")
    print("- Interactive menu-driven interface")
    print("- No command line arguments needed")
    print("- Support for all prediction systems")
    print("- Multiple operation modes")
    print("- User-friendly navigation")
    print("=" * 80)

if __name__ == "__main__":
    demo_launcher()