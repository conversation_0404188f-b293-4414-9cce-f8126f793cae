"""
专用适配器
为特定类型的旧预测器提供专门的适配功能
"""

import pandas as pd
from typing import Dict, Any, List, Tuple, Optional
from abc import ABC

from .interfaces import (
    IStandardPredictor,
    PredictionResult as StandardPredictionResult,
    ValidationResult,
    PredictionType,
    BallType,
)
from .exceptions import ValidationException, PredictionException
from .predictor_adapter import LegacyPredictorAdapter


class OddEvenPredictorAdapter(LegacyPredictorAdapter):
    """奇偶比预测器专用适配器

    专门适配返回(state, confidence)格式的奇偶比预测器
    """

    def __init__(
        self, legacy_predictor, name: Optional[str] = None, version: str = "1.0"
    ):
        super().__init__(legacy_predictor, name, version)

    def get_prediction_type(self) -> PredictionType:
        """获取预测类型"""
        return PredictionType.ODD_EVEN_RATIO

    def predict(
        self, data: pd.DataFrame, target_index: int, **kwargs
    ) -> StandardPredictionResult:
        """执行预测"""
        try:
            # 检查旧预测器是否需要训练
            if (
                hasattr(self.legacy_predictor, "is_trained")
                and not self.legacy_predictor.is_trained
            ):
                # 从数据中提取状态序列进行训练
                state_sequence = self._extract_state_sequence(data)
                if hasattr(self.legacy_predictor, "train"):
                    self.legacy_predictor.train(state_sequence)

            # 获取当前状态
            current_state = self._get_current_state(data, target_index)

            # 调用旧预测器的predict方法
            if hasattr(self.legacy_predictor, "predict"):
                # 尝试不同的调用方式
                try:
                    # 方式1: 带current_state参数
                    result = self.legacy_predictor.predict(current_state)
                except TypeError:
                    try:
                        # 方式2: 无参数
                        result = self.legacy_predictor.predict()
                    except TypeError:
                        # 方式3: 带data参数
                        result = self.legacy_predictor.predict(data)

                # 转换结果格式
                return self._convert_odd_even_result(result)
            else:
                raise PredictionException("旧预测器没有predict方法")

        except Exception as e:
            raise PredictionException(f"奇偶比适配器预测失败: {e}")

    def _extract_state_sequence(self, data: pd.DataFrame) -> List[str]:
        """从数据中提取奇偶比状态序列"""
        states = []
        for _, row in data.iterrows():
            # 计算红球奇偶比
            red_balls = [row[f"红球{i}"] for i in range(1, 6)]
            odd_count = sum(1 for ball in red_balls if ball % 2 == 1)
            even_count = 5 - odd_count
            state = f"{odd_count}:{even_count}"
            states.append(state)
        return states[::-1]  # 返回从新到旧的序列

    def _get_current_state(self, data: pd.DataFrame, target_index: int) -> str:
        """获取当前状态"""
        if target_index > 0:
            row = data.iloc[target_index - 1]
            red_balls = [row[f"红球{i}"] for i in range(1, 6)]
            odd_count = sum(1 for ball in red_balls if ball % 2 == 1)
            even_count = 5 - odd_count
            return f"{odd_count}:{even_count}"
        return "3:2"  # 默认状态

    def _convert_odd_even_result(self, result) -> StandardPredictionResult:
        """转换奇偶比预测结果"""
        if isinstance(result, tuple) and len(result) == 2:
            # (state, confidence) 格式
            state, confidence = result
            return StandardPredictionResult(
                prediction_type=PredictionType.ODD_EVEN_RATIO,
                ball_type=BallType.RED,
                value=state,
                confidence=float(confidence),
                metadata={
                    "adapter_type": "OddEvenPredictorAdapter",
                    "original_result": result,
                },
            )
        elif isinstance(result, str):
            # 只有状态，没有置信度
            return StandardPredictionResult(
                prediction_type=PredictionType.ODD_EVEN_RATIO,
                ball_type=BallType.RED,
                value=result,
                confidence=0.5,
                metadata={
                    "adapter_type": "OddEvenPredictorAdapter",
                    "original_result": result,
                    "confidence_estimated": True,
                },
            )
        else:
            # 其他格式，尝试转换为字符串
            return StandardPredictionResult(
                prediction_type=PredictionType.ODD_EVEN_RATIO,
                ball_type=BallType.RED,
                value=str(result),
                confidence=0.5,
                metadata={
                    "adapter_type": "OddEvenPredictorAdapter",
                    "original_result": result,
                    "format_converted": True,
                },
            )


class RatioPredictorAdapter(LegacyPredictorAdapter):
    """比值预测器专用适配器

    适配返回比值字典的预测器
    """

    def get_prediction_type(self) -> PredictionType:
        """获取预测类型"""
        return PredictionType.RATIO

    def predict(
        self, data: pd.DataFrame, target_index: int, **kwargs
    ) -> StandardPredictionResult:
        """执行预测"""
        try:
            # 调用旧预测器
            if hasattr(self.legacy_predictor, "predict_ratios"):
                result = self.legacy_predictor.predict_ratios(data)
            elif hasattr(self.legacy_predictor, "predict"):
                result = self.legacy_predictor.predict(data)
            else:
                raise PredictionException("旧预测器没有predict或predict_ratios方法")

            return self._convert_ratio_result(result)

        except Exception as e:
            raise PredictionException(f"比值适配器预测失败: {e}")

    def _convert_ratio_result(self, result) -> StandardPredictionResult:
        """转换比值预测结果"""
        if isinstance(result, dict):
            # 计算平均置信度
            confidence = 0.7  # 默认置信度

            return StandardPredictionResult(
                prediction_type=PredictionType.RATIO,
                ball_type=BallType.RED,
                value=result,
                confidence=confidence,
                metadata={
                    "adapter_type": "RatioPredictorAdapter",
                    "original_result": result,
                },
            )
        else:
            return StandardPredictionResult(
                prediction_type=PredictionType.RATIO,
                ball_type=BallType.RED,
                value={"ratio": str(result)},
                confidence=0.5,
                metadata={
                    "adapter_type": "RatioPredictorAdapter",
                    "original_result": result,
                    "format_converted": True,
                },
            )


def create_specialized_adapter(
    legacy_predictor,
    predictor_type: Optional[str] = None,
    name: Optional[str] = None,
    version: str = "1.0",
):
    """创建专用适配器的工厂函数

    Args:
        legacy_predictor: 旧的预测器实例
        predictor_type: 预测器类型 ('odd_even', 'ratio', 'general')
        name: 适配器名称
        version: 版本号

    Returns:
        适配器实例
    """
    # 自动检测预测器类型
    if predictor_type is None:
        class_name = legacy_predictor.__class__.__name__.lower()
        if "odd" in class_name and "even" in class_name:
            predictor_type = "odd_even"
        elif "ratio" in class_name:
            predictor_type = "ratio"
        else:
            predictor_type = "general"

    # 创建对应的适配器
    if predictor_type == "odd_even":
        return OddEvenPredictorAdapter(legacy_predictor, name, version)
    elif predictor_type == "ratio":
        return RatioPredictorAdapter(legacy_predictor, name, version)
    else:
        return LegacyPredictorAdapter(legacy_predictor, name, version)


__all__ = [
    "OddEvenPredictorAdapter",
    "RatioPredictorAdapter",
    "create_specialized_adapter",
]
