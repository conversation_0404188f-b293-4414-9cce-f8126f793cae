# 彩票预测系统 - 项目结构说明

## 📁 重构后的标准项目结构

```
lottery-prediction-system/
├── src/                          # 源代码目录
│   ├── apps/                     # 主应用程序
│   │   ├── __init__.py
│   │   ├── main.py              # 主程序入口
│   │   └── advanced_probabilistic_system.py
│   ├── tools/                    # 优化工具
│   │   ├── __init__.py
│   │   ├── optimize_bayes_parameters.py
│   │   ├── optimize_predictor_parameters_unified.py
│   │   └── blue_ball_analysis_system.py
│   ├── core/                     # 核心模块
│   │   ├── analyzer.py
│   │   ├── data_manager.py
│   │   └── ...
│   ├── models/                   # 预测模型
│   │   ├── improved_predictor.py
│   │   ├── neural_predictor.py
│   │   └── ...
│   ├── generators/               # 号码生成器
│   ├── systems/                  # 系统模块
│   ├── utils/                    # 工具函数
│   └── framework/                # 统一框架
├── tests/                        # 测试代码
│   ├── unit/                     # 单元测试
│   ├── integration/              # 集成测试
│   ├── performance/              # 性能测试
│   └── ...
├── scripts/                      # 独立脚本
│   ├── auto_fix_config.py
│   ├── config_consistency_checker.py
│   └── ...
├── data/                         # 数据文件
│   ├── raw/                      # 原始数据
│   ├── processed/                # 处理后数据
│   ├── results/                  # 结果文件
│   └── backups/                  # 备份文件
├── docs/                         # 文档
│   ├── reports/                  # 报告文档
│   ├── api/                      # API文档
│   └── user_guide/               # 用户指南
├── logs/                         # 日志文件
├── config/                       # 配置文件
├── notebooks/                    # Jupyter笔记本
├── deployment/                   # 部署相关
├── main_entry.py                 # 主程序入口点
├── optimize_entry.py             # 优化工具入口点
├── requirements.txt              # 依赖包列表
├── setup.py                      # 安装脚本
├── README.md                     # 项目说明
└── PROJECT_STRUCTURE.md          # 本文件
```

## 🚀 使用方法

### 运行主程序
```bash
python main_entry.py
```

### 运行优化工具
```bash
# 贝叶斯参数优化
python optimize_entry.py bayes

# 预测器参数优化
python optimize_entry.py predictor

# 蓝球分析
python optimize_entry.py blue
```

### 运行测试
```bash
# 运行所有测试
python -m pytest tests/

# 运行特定测试
python -m pytest tests/unit/
```

## 📋 重构改进

1. **标准化结构**: 遵循Python项目最佳实践
2. **模块化设计**: 清晰的功能分离
3. **向后兼容**: 保持原有使用方式
4. **易于维护**: 代码组织更清晰
5. **测试友好**: 测试代码独立组织

## 🔧 开发指南

- 新功能代码放在 `src/` 对应模块下
- 测试代码放在 `tests/` 对应目录下
- 脚本工具放在 `scripts/` 目录下
- 文档更新放在 `docs/` 目录下
- 遵循模块化导入规范
