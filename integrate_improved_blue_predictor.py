#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将改进的蓝球预测器集成到主系统中
"""

import sys
import os
import pandas as pd
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_improved_blue_predictor():
    """测试改进的蓝球预测器"""
    print("测试改进的蓝球预测器...")
    print("=" * 50)
    
    try:
        # 导入改进的蓝球预测器
        from src.models.improved_blue_predictor import ImprovedBlueBallPredictor
        
        # 加载数据
        data_file = project_root / "data" / "raw" / "dlt_data.csv"
        if not data_file.exists():
            print(f"[错误] 数据文件不存在: {data_file}")
            return False
        
        data = pd.read_csv(data_file)
        print(f"[成功] 加载数据: {len(data)} 期")
        
        # 初始化预测器
        predictor = ImprovedBlueBallPredictor()
        
        # 训练预测器
        train_data = data.iloc[:-10]  # 使用前面的数据训练
        predictor.train(train_data)
        print(f"[成功] 训练完成: {len(train_data)} 期数据")
        
        # 测试预测
        test_data = data.iloc[-10:]  # 使用最后10期测试
        correct_predictions = 0
        total_predictions = 0
        
        print("\n[测试] 预测结果:")
        print("期号\t\t实际\t预测\t置信度\t结果")
        print("-" * 50)
        
        for i, (_, row) in enumerate(test_data.iterrows()):
            if i == 0:
                continue  # 跳过第一期，因为需要历史数据
            
            # 获取历史数据
            history_data = data.iloc[:len(train_data) + i]
            recent_history = []
            
            for _, hist_row in history_data.tail(5).iterrows():
                blue1, blue2 = int(hist_row['蓝球1']), int(hist_row['蓝球2'])
                large_count = sum(1 for b in [blue1, blue2] if b >= 8)
                small_count = 2 - large_count
                ratio = f"{large_count}:{small_count}"
                recent_history.append(ratio)
            
            # 预测
            predicted_ratio, confidence = predictor.predict(recent_history)
            
            # 计算实际比例
            blue1, blue2 = int(row['蓝球1']), int(row['蓝球2'])
            actual_large = sum(1 for b in [blue1, blue2] if b >= 8)
            actual_small = 2 - actual_large
            actual_ratio = f"{actual_large}:{actual_small}"
            
            # 判断预测是否正确
            is_correct = predicted_ratio == actual_ratio
            if is_correct:
                correct_predictions += 1
            total_predictions += 1
            
            result_symbol = "✅" if is_correct else "❌"
            period = row.get('期号', f'期{i}')
            
            print(f"{period}\t{actual_ratio}\t{predicted_ratio}\t{confidence:.3f}\t{result_symbol}")
        
        accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0
        print(f"\n[结果] 预测准确率: {accuracy:.1%} ({correct_predictions}/{total_predictions})")
        
        return accuracy > 0.4  # 如果准确率超过40%认为成功
        
    except Exception as e:
        print(f"[错误] 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def integrate_to_main_system():
    """集成到主系统"""
    print("\n集成改进的蓝球预测器到主系统...")
    print("=" * 50)
    
    try:
        # 检查主系统文件
        main_system_file = project_root / "src" / "apps" / "advanced_probabilistic_system.py"
        if not main_system_file.exists():
            print(f"[错误] 主系统文件不存在: {main_system_file}")
            return False
        
        # 读取主系统文件
        with open(main_system_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经集成
        if "ImprovedBlueBallPredictor" in content:
            print("[信息] 改进的蓝球预测器已经集成到主系统")
            return True
        
        # 添加导入语句
        import_line = "from src.models.improved_blue_predictor import ImprovedBlueBallPredictor"
        
        if import_line not in content:
            # 找到合适的位置插入导入语句
            lines = content.split('\n')
            insert_index = 0
            
            for i, line in enumerate(lines):
                if line.startswith('from src.') or line.startswith('import '):
                    insert_index = i + 1
            
            lines.insert(insert_index, import_line)
            content = '\n'.join(lines)
            print("[成功] 添加导入语句")
        
        # 在初始化方法中添加改进的蓝球预测器
        init_addition = """
        # 改进的蓝球预测器
        try:
            self.improved_blue_predictor = ImprovedBlueBallPredictor()
            print("[成功] 改进的蓝球预测器已初始化")
        except Exception as e:
            print(f"[警告] 改进的蓝球预测器初始化失败: {e}")
            self.improved_blue_predictor = None"""
        
        # 找到__init__方法的结尾
        if "self.improved_blue_predictor" not in content:
            # 查找合适的插入位置
            init_pattern = "def __init__(self"
            init_start = content.find(init_pattern)
            
            if init_start != -1:
                # 找到初始化方法的结尾
                lines = content.split('\n')
                for i, line in enumerate(lines):
                    if init_pattern in line:
                        # 找到这个方法的结尾
                        indent_level = len(line) - len(line.lstrip())
                        for j in range(i + 1, len(lines)):
                            if lines[j].strip() == "":
                                continue
                            current_indent = len(lines[j]) - len(lines[j].lstrip())
                            if current_indent <= indent_level and lines[j].strip():
                                # 在这里插入
                                lines.insert(j, init_addition)
                                content = '\n'.join(lines)
                                print("[成功] 添加改进的蓝球预测器初始化代码")
                                break
                        break
        
        # 写回文件
        with open(main_system_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("[成功] 改进的蓝球预测器已集成到主系统")
        return True
        
    except Exception as e:
        print(f"[错误] 集成失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration():
    """测试集成效果"""
    print("\n测试集成效果...")
    print("=" * 50)
    
    try:
        # 导入主系统
        from src.apps.advanced_probabilistic_system import AdvancedProbabilisticSystem
        
        # 初始化系统
        system = AdvancedProbabilisticSystem()
        
        # 检查改进的蓝球预测器是否存在
        if hasattr(system, 'improved_blue_predictor') and system.improved_blue_predictor:
            print("[成功] 改进的蓝球预测器已成功集成到主系统")
            
            # 测试预测功能
            try:
                # 加载数据进行训练
                data_file = project_root / "data" / "raw" / "dlt_data.csv"
                data = pd.read_csv(data_file)
                
                # 训练改进的预测器
                system.improved_blue_predictor.train(data.iloc[:-1])
                
                # 测试预测
                recent_history = ["1:1", "0:2", "1:1", "2:0", "1:1"]
                prediction, confidence = system.improved_blue_predictor.predict(recent_history)
                
                print(f"[测试] 预测结果: {prediction}, 置信度: {confidence:.3f}")
                print("[成功] 改进的蓝球预测器功能正常")
                return True
                
            except Exception as e:
                print(f"[警告] 预测功能测试失败: {e}")
                return False
        else:
            print("[失败] 改进的蓝球预测器未正确集成")
            return False
            
    except Exception as e:
        print(f"[错误] 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("改进蓝球预测器集成工具")
    print("=" * 60)
    
    # 步骤1: 测试改进的蓝球预测器
    if not test_improved_blue_predictor():
        print("[失败] 改进的蓝球预测器测试失败，停止集成")
        return 1
    
    # 步骤2: 集成到主系统
    if not integrate_to_main_system():
        print("[失败] 集成到主系统失败")
        return 1
    
    # 步骤3: 测试集成效果
    if not test_integration():
        print("[失败] 集成测试失败")
        return 1
    
    print("\n" + "=" * 60)
    print("[完成] 改进的蓝球预测器已成功集成到主系统")
    print("建议:")
    print("1. 运行 python test_main_system.py 验证整体系统功能")
    print("2. 运行主系统进行实际预测测试")
    print("3. 监控蓝球预测准确率的改善情况")
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
