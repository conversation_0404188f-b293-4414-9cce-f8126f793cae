"""
多样化号码生成器
解决号码选择过度集中的问题，提高2+1命中率
"""

import numpy as np
from typing import List, Tuple, Set
from collections import Counter, defaultdict
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.utils.utils import (
    parse_numbers, calculate_odd_even_ratio, calculate_size_ratio_red,
    calculate_size_ratio_blue, ratio_to_state, state_to_ratio, load_data
)


class DiversifiedGenerator:
    """多样化号码生成器"""
    
    def __init__(self):
        """初始化生成器"""
        self.red_range = list(range(1, 36))
        self.blue_range = list(range(1, 13))
        
        # 分析历史数据，找出真正的热号和冷号
        self.data = load_data()
        self._analyze_historical_frequency()
        
        # 多样化策略权重
        self.strategy_weights = {
            'hot_numbers': 0.3,      # 热号策略
            'balanced': 0.4,         # 均衡策略  
            'cold_numbers': 0.2,     # 冷号策略
            'random': 0.1           # 随机策略
        }
    
    def _analyze_historical_frequency(self) -> None:
        """分析历史频率，找出真正的热号冷号"""
        red_freq = Counter()
        blue_freq = Counter()
        
        # 分析最近100期的数据
        recent_data = self.data.head(100) if len(self.data) > 100 else self.data
        
        for _, row in recent_data.iterrows():
            red_balls, blue_balls = parse_numbers(row)
            red_freq.update(red_balls)
            blue_freq.update(blue_balls)
        
        # 红球分类
        self.red_hot = [num for num, _ in red_freq.most_common(12)]  # 前12个热号
        self.red_cold = [num for num, _ in red_freq.most_common()[-12:]]  # 后12个冷号
        self.red_balanced = [num for num in self.red_range if num not in self.red_hot and num not in self.red_cold]
        
        # 蓝球分类
        self.blue_hot = [num for num, _ in blue_freq.most_common(4)]  # 前4个热号
        self.blue_cold = [num for num, _ in blue_freq.most_common()[-4:]]  # 后4个冷号
        self.blue_balanced = [num for num in self.blue_range if num not in self.blue_hot and num not in self.blue_cold]
        
        print(f"红球热号: {sorted(self.red_hot)}")
        print(f"红球冷号: {sorted(self.red_cold)}")
        print(f"蓝球热号: {sorted(self.blue_hot)}")
        print(f"蓝球冷号: {sorted(self.blue_cold)}")
    
    def generate_diversified_numbers(self,
                                   red_odd_even_state: str,
                                   red_size_state: str,
                                   blue_size_state: str,
                                   red_kill_numbers: List[int] = None,
                                   blue_kill_numbers: List[int] = None,
                                   seed: int = 0) -> Tuple[List[int], List[int]]:
        """
        生成多样化号码组合（优化版 - 直接传入杀号列表）

        Args:
            red_odd_even_state: 红球奇偶比状态
            red_size_state: 红球大小比状态
            blue_size_state: 蓝球大小比状态
            red_kill_numbers: 红球杀号列表
            blue_kill_numbers: 蓝球杀号列表
            seed: 随机种子

        Returns:
            Tuple[List[int], List[int]]: (红球, 蓝球)
        """
        np.random.seed(seed)
        
        # 生成多个候选组合
        candidates = []
        
        # 策略1: 热号为主
        hot_red, hot_blue = self._generate_hot_combination(
            red_odd_even_state, red_size_state, blue_size_state, red_kill_numbers or [], blue_kill_numbers or [], seed
        )
        candidates.append((hot_red, hot_blue, 'hot'))

        # 策略2: 均衡组合
        balanced_red, balanced_blue = self._generate_balanced_combination(
            red_odd_even_state, red_size_state, blue_size_state, red_kill_numbers or [], blue_kill_numbers or [], seed + 1
        )
        candidates.append((balanced_red, balanced_blue, 'balanced'))

        # 策略3: 冷号为主
        cold_red, cold_blue = self._generate_cold_combination(
            red_odd_even_state, red_size_state, blue_size_state, red_kill_numbers or [], blue_kill_numbers or [], seed + 2
        )
        candidates.append((cold_red, cold_blue, 'cold'))

        # 策略4: 随机组合
        random_red, random_blue = self._generate_random_combination(
            red_odd_even_state, red_size_state, blue_size_state, red_kill_numbers or [], blue_kill_numbers or [], seed + 3
        )
        candidates.append((random_red, random_blue, 'random'))
        
        # 根据权重选择最终组合
        weights = list(self.strategy_weights.values())
        chosen_idx = np.random.choice(len(candidates), p=weights)
        chosen_red, chosen_blue, strategy = candidates[chosen_idx]
        
        print(f"  选择策略: {strategy}")
        
        return chosen_red, chosen_blue
    
    def _generate_hot_combination(self, odd_even_state: str, size_state: str,
                                blue_size_state: str, red_kill_numbers: List[int], blue_kill_numbers: List[int], seed: int) -> Tuple[List[int], List[int]]:
        """生成热号为主的组合"""
        np.random.seed(seed)
        
        # 红球：70%热号 + 30%其他（应用杀号过滤）
        red_candidates = [n for n in self.red_hot if n not in red_kill_numbers]
        if len(red_candidates) < 4:
            red_candidates.extend([n for n in self.red_balanced if n not in red_candidates and n not in red_kill_numbers][:4-len(red_candidates)])

        red_balls = self._select_red_with_constraints(
            red_candidates, odd_even_state, size_state, 4, seed
        )

        # 补充1个非热号
        other_candidates = [n for n in self.red_range if n not in self.red_hot and n not in red_balls and n not in red_kill_numbers]
        if other_candidates:
            red_balls.append(np.random.choice(other_candidates))

        # 蓝球：优先热号（应用杀号过滤）
        blue_candidates = [n for n in self.blue_hot if n not in blue_kill_numbers]
        if len(blue_candidates) < 2:
            blue_candidates.extend([n for n in self.blue_balanced if n not in blue_candidates and n not in blue_kill_numbers][:2-len(blue_candidates)])
        
        blue_balls = self._select_blue_with_constraints(blue_candidates, blue_size_state, seed)
        
        return sorted(red_balls[:5]), sorted(blue_balls[:2])
    
    def _generate_balanced_combination(self, odd_even_state: str, size_state: str,
                                     blue_size_state: str, red_kill_numbers: List[int], blue_kill_numbers: List[int], seed: int) -> Tuple[List[int], List[int]]:
        """生成均衡组合"""
        np.random.seed(seed)
        
        # 红球：各类型均衡选择（应用杀号过滤）
        red_candidates = []
        red_candidates.extend([n for n in self.red_hot[:6] if n not in red_kill_numbers])  # 2个热号
        red_candidates.extend([n for n in self.red_balanced[:6] if n not in red_kill_numbers])  # 2个均衡号
        red_candidates.extend([n for n in self.red_cold[:6] if n not in red_kill_numbers])  # 1个冷号

        # 去重并随机选择
        red_candidates = list(set(red_candidates))
        if len(red_candidates) >= 5:
            red_balls = list(np.random.choice(red_candidates, 5, replace=False))
        else:
            red_balls = red_candidates[:]
            remaining = [n for n in self.red_range if n not in red_balls and n not in red_kill_numbers]
            needed = 5 - len(red_balls)
            if len(remaining) >= needed:
                red_balls.extend(list(np.random.choice(remaining, needed, replace=False)))

        # 蓝球：均衡选择（应用杀号过滤）
        blue_candidates = []
        blue_candidates.extend([n for n in self.blue_hot[:2] if n not in blue_kill_numbers])
        blue_candidates.extend([n for n in self.blue_balanced[:2] if n not in blue_kill_numbers])

        blue_candidates = list(set(blue_candidates))
        if len(blue_candidates) >= 2:
            blue_balls = list(np.random.choice(blue_candidates, 2, replace=False))
        else:
            blue_balls = blue_candidates[:]
            remaining = [n for n in self.blue_range if n not in blue_balls and n not in blue_kill_numbers]
            needed = 2 - len(blue_balls)
            if len(remaining) >= needed:
                blue_balls.extend(list(np.random.choice(remaining, needed, replace=False)))
        
        return sorted(red_balls[:5]), sorted(blue_balls[:2])
    
    def _generate_cold_combination(self, odd_even_state: str, size_state: str,
                                 blue_size_state: str, red_kill_numbers: List[int], blue_kill_numbers: List[int], seed: int) -> Tuple[List[int], List[int]]:
        """生成冷号为主的组合"""
        np.random.seed(seed)
        
        # 红球：70%冷号 + 30%其他（应用杀号过滤）
        red_candidates = [n for n in self.red_cold if n not in red_kill_numbers]
        if len(red_candidates) < 4:
            red_candidates.extend([n for n in self.red_balanced if n not in red_candidates and n not in red_kill_numbers][:4-len(red_candidates)])

        red_balls = self._select_red_with_constraints(
            red_candidates, odd_even_state, size_state, 4, seed
        )

        # 补充1个非冷号
        other_candidates = [n for n in self.red_range if n not in self.red_cold and n not in red_balls and n not in red_kill_numbers]
        if other_candidates:
            red_balls.append(np.random.choice(other_candidates))

        # 蓝球：优先冷号（应用杀号过滤）
        blue_candidates = [n for n in self.blue_cold if n not in blue_kill_numbers]
        if len(blue_candidates) < 2:
            blue_candidates.extend([n for n in self.blue_balanced if n not in blue_candidates and n not in blue_kill_numbers][:2-len(blue_candidates)])
        
        blue_balls = self._select_blue_with_constraints(blue_candidates, blue_size_state, seed)
        
        return sorted(red_balls[:5]), sorted(blue_balls[:2])
    
    def _generate_random_combination(self, odd_even_state: str, size_state: str,
                                   blue_size_state: str, red_kill_numbers: List[int], blue_kill_numbers: List[int], seed: int) -> Tuple[List[int], List[int]]:
        """生成随机组合"""
        np.random.seed(seed)
        
        # 红球：完全随机（应用杀号过滤）
        red_candidates = [n for n in self.red_range if n not in red_kill_numbers]
        red_balls = list(np.random.choice(red_candidates, min(5, len(red_candidates)), replace=False))

        # 蓝球：完全随机（应用杀号过滤）
        blue_candidates = [n for n in self.blue_range if n not in blue_kill_numbers]
        blue_balls = list(np.random.choice(blue_candidates, min(2, len(blue_candidates)), replace=False))
        
        return sorted(red_balls), sorted(blue_balls)
    

    
    def _select_red_with_constraints(self, candidates: List[int], odd_even_state: str, 
                                   size_state: str, target_count: int, seed: int) -> List[int]:
        """在约束条件下选择红球"""
        if len(candidates) <= target_count:
            return candidates[:]
        
        np.random.seed(seed)
        
        # 尝试满足奇偶比和大小比约束
        odd_count, even_count = state_to_ratio(odd_even_state)
        small_count, big_count = state_to_ratio(size_state)
        
        # 按奇偶分类
        odd_candidates = [n for n in candidates if n % 2 == 1]
        even_candidates = [n for n in candidates if n % 2 == 0]
        
        selected = []
        
        # 选择奇数
        if odd_count > 0 and odd_candidates:
            selected.extend(list(np.random.choice(
                odd_candidates, 
                min(odd_count, len(odd_candidates), target_count), 
                replace=False
            )))
        
        # 选择偶数
        remaining_count = target_count - len(selected)
        if even_count > 0 and even_candidates and remaining_count > 0:
            available_even = [n for n in even_candidates if n not in selected]
            if available_even:
                selected.extend(list(np.random.choice(
                    available_even,
                    min(even_count, len(available_even), remaining_count),
                    replace=False
                )))
        
        # 如果还需要更多号码，随机补充
        remaining_count = target_count - len(selected)
        if remaining_count > 0:
            remaining_candidates = [n for n in candidates if n not in selected]
            if remaining_candidates:
                selected.extend(list(np.random.choice(
                    remaining_candidates,
                    min(remaining_count, len(remaining_candidates)),
                    replace=False
                )))
        
        return selected[:target_count]
    
    def _select_blue_with_constraints(self, candidates: List[int], size_state: str, seed: int) -> List[int]:
        """在约束条件下选择蓝球"""
        if len(candidates) <= 2:
            return candidates[:]
        
        np.random.seed(seed)
        
        small_count, big_count = state_to_ratio(size_state)
        
        # 按大小分类
        small_candidates = [n for n in candidates if 1 <= n <= 7]
        big_candidates = [n for n in candidates if 7 <= n <= 12]
        
        selected = []
        
        # 选择小号
        if small_count > 0 and small_candidates:
            selected.extend(list(np.random.choice(
                small_candidates,
                min(small_count, len(small_candidates)),
                replace=False
            )))
        
        # 选择大号
        remaining_count = 2 - len(selected)
        if big_count > 0 and big_candidates and remaining_count > 0:
            available_big = [n for n in big_candidates if n not in selected]
            if available_big:
                selected.extend(list(np.random.choice(
                    available_big,
                    min(big_count, len(available_big), remaining_count),
                    replace=False
                )))
        
        # 如果还需要更多号码，随机补充
        remaining_count = 2 - len(selected)
        if remaining_count > 0:
            remaining_candidates = [n for n in candidates if n not in selected]
            if remaining_candidates:
                selected.extend(list(np.random.choice(
                    remaining_candidates,
                    min(remaining_count, len(remaining_candidates)),
                    replace=False
                )))
        
        return selected[:2]


def test_diversified_generator():
    """测试多样化生成器"""
    generator = DiversifiedGenerator()
    
    print("测试多样化生成器...")
    
    for i in range(5):
        red, blue = generator.generate_diversified_numbers(
            "3:2", "2:3", "1:1", seed=i
        )
        print(f"第{i+1}次: 红球{red}, 蓝球{blue}")


if __name__ == "__main__":
    test_diversified_generator()
