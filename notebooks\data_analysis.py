"""
深度数据分析模块
分析历史数据模式，找出预测失败的原因
"""

import pandas as pd
import numpy as np
from collections import Counter, defaultdict
from typing import Dict, List, Tuple
# import matplotlib.pyplot as plt  # 暂时注释掉
from utils import load_data, parse_numbers, calculate_odd_even_ratio, calculate_size_ratio_red, ratio_to_state
from analyzer import LotteryAnalyzer


class DeepDataAnalyzer:
    """深度数据分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.data = load_data()
        self.analyzer = LotteryAnalyzer(self.data)
    
    def analyze_state_transition_patterns(self) -> None:
        """分析状态转移模式"""
        print("=" * 60)
        print("状态转移模式分析")
        print("=" * 60)
        
        # 分析红球奇偶比转移
        red_odd_even_seq = self.analyzer.get_feature_sequence('red_odd_even')
        print(f"\n红球奇偶比状态序列（前20期）: {red_odd_even_seq[:20]}")
        
        # 统计转移频率
        transitions = defaultdict(Counter)
        for i in range(len(red_odd_even_seq) - 1):
            current = red_odd_even_seq[i]
            next_state = red_odd_even_seq[i + 1]
            transitions[current][next_state] += 1
        
        print("\n红球奇偶比状态转移频率:")
        for current_state in sorted(transitions.keys()):
            print(f"从 {current_state} 转移到:")
            total = sum(transitions[current_state].values())
            for next_state, count in transitions[current_state].most_common():
                prob = count / total
                print(f"  {next_state}: {count}次 ({prob:.1%})")
        
        # 分析红球大小比转移
        red_size_seq = self.analyzer.get_feature_sequence('red_size')
        print(f"\n红球大小比状态序列（前20期）: {red_size_seq[:20]}")
        
        size_transitions = defaultdict(Counter)
        for i in range(len(red_size_seq) - 1):
            current = red_size_seq[i]
            next_state = red_size_seq[i + 1]
            size_transitions[current][next_state] += 1
        
        print("\n红球大小比状态转移频率:")
        for current_state in sorted(size_transitions.keys()):
            print(f"从 {current_state} 转移到:")
            total = sum(size_transitions[current_state].values())
            for next_state, count in size_transitions[current_state].most_common():
                prob = count / total
                print(f"  {next_state}: {count}次 ({prob:.1%})")
    
    def analyze_state_distribution(self) -> None:
        """分析状态分布"""
        print("\n" + "=" * 60)
        print("状态分布分析")
        print("=" * 60)
        
        # 红球奇偶比分布
        red_odd_even_freq = self.analyzer.calculate_state_frequencies('red_odd_even')
        print("\n红球奇偶比分布:")
        for state, freq in sorted(red_odd_even_freq.items()):
            print(f"  {state}: {freq:.1%}")
        
        # 红球大小比分布
        red_size_freq = self.analyzer.calculate_state_frequencies('red_size')
        print("\n红球大小比分布:")
        for state, freq in sorted(red_size_freq.items()):
            print(f"  {state}: {freq:.1%}")
        
        # 蓝球大小比分布
        blue_size_freq = self.analyzer.calculate_state_frequencies('blue_size')
        print("\n蓝球大小比分布:")
        for state, freq in sorted(blue_size_freq.items()):
            print(f"  {state}: {freq:.1%}")
    
    def analyze_prediction_difficulty(self) -> None:
        """分析预测难度"""
        print("\n" + "=" * 60)
        print("预测难度分析")
        print("=" * 60)
        
        # 计算状态变化频率
        features = ['red_odd_even', 'red_size', 'blue_size']
        
        for feature in features:
            sequence = self.analyzer.get_feature_sequence(feature)
            if len(sequence) < 2:
                continue
            
            # 计算连续相同状态的长度
            consecutive_lengths = []
            current_length = 1
            
            for i in range(1, len(sequence)):
                if sequence[i] == sequence[i-1]:
                    current_length += 1
                else:
                    consecutive_lengths.append(current_length)
                    current_length = 1
            consecutive_lengths.append(current_length)
            
            # 计算变化频率
            changes = sum(1 for i in range(1, len(sequence)) if sequence[i] != sequence[i-1])
            change_rate = changes / (len(sequence) - 1) if len(sequence) > 1 else 0
            
            print(f"\n{feature}:")
            print(f"  状态变化率: {change_rate:.1%}")
            print(f"  平均连续长度: {np.mean(consecutive_lengths):.1f}")
            print(f"  最长连续长度: {max(consecutive_lengths)}")
            print(f"  连续长度分布: {Counter(consecutive_lengths)}")
    
    def analyze_number_patterns(self) -> None:
        """分析号码模式"""
        print("\n" + "=" * 60)
        print("号码模式分析")
        print("=" * 60)
        
        all_red_numbers = []
        all_blue_numbers = []
        
        for _, row in self.data.iterrows():
            red_balls, blue_balls = parse_numbers(row)
            all_red_numbers.extend(red_balls)
            all_blue_numbers.extend(blue_balls)
        
        # 红球频率分析
        red_freq = Counter(all_red_numbers)
        print("\n红球出现频率（前10名）:")
        for num, count in red_freq.most_common(10):
            freq = count / len(self.data)
            print(f"  {num:02d}: {count}次 ({freq:.2f}次/期)")
        
        print("\n红球出现频率（后10名）:")
        for num, count in red_freq.most_common()[-10:]:
            freq = count / len(self.data)
            print(f"  {num:02d}: {count}次 ({freq:.2f}次/期)")
        
        # 蓝球频率分析
        blue_freq = Counter(all_blue_numbers)
        print("\n蓝球出现频率:")
        for num in range(1, 13):
            count = blue_freq.get(num, 0)
            freq = count / len(self.data)
            print(f"  {num:02d}: {count}次 ({freq:.2f}次/期)")
    
    def analyze_recent_trends(self, periods: int = 50) -> None:
        """分析最近期数的趋势"""
        print(f"\n" + "=" * 60)
        print(f"最近{periods}期趋势分析")
        print("=" * 60)
        
        recent_data = self.data.head(periods)
        recent_analyzer = LotteryAnalyzer(recent_data)
        
        # 对比整体分布和最近分布
        features = ['red_odd_even', 'red_size', 'blue_size']
        
        for feature in features:
            print(f"\n{feature}分布对比:")
            
            # 整体分布
            overall_freq = self.analyzer.calculate_state_frequencies(feature)
            recent_freq = recent_analyzer.calculate_state_frequencies(feature)
            
            print("  状态    整体频率  最近频率  差异")
            print("  " + "-" * 35)
            
            all_states = set(overall_freq.keys()) | set(recent_freq.keys())
            for state in sorted(all_states):
                overall = overall_freq.get(state, 0)
                recent = recent_freq.get(state, 0)
                diff = recent - overall
                print(f"  {state:6s}  {overall:7.1%}   {recent:7.1%}  {diff:+6.1%}")
    
    def analyze_prediction_errors(self) -> None:
        """分析预测错误模式"""
        print("\n" + "=" * 60)
        print("预测错误模式分析")
        print("=" * 60)
        
        # 模拟简单的马尔科夫预测
        features = ['red_odd_even', 'red_size', 'blue_size']
        
        for feature in features:
            sequence = self.analyzer.get_feature_sequence(feature)
            if len(sequence) < 20:
                continue
            
            print(f"\n{feature}预测错误分析:")
            
            # 统计转移概率
            transitions = defaultdict(Counter)
            for i in range(len(sequence) - 1):
                current = sequence[i]
                next_state = sequence[i + 1]
                transitions[current][next_state] += 1
            
            # 计算预测准确率
            correct_predictions = 0
            total_predictions = 0
            error_patterns = defaultdict(int)
            
            for i in range(10, len(sequence) - 1):  # 从第10期开始预测
                current_state = sequence[i]
                actual_next = sequence[i + 1]
                
                # 使用马尔科夫预测
                if current_state in transitions:
                    predicted_next = transitions[current_state].most_common(1)[0][0]
                    
                    if predicted_next == actual_next:
                        correct_predictions += 1
                    else:
                        error_patterns[f"{current_state}->{predicted_next}(实际:{actual_next})"] += 1
                    
                    total_predictions += 1
            
            accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0
            print(f"  马尔科夫预测准确率: {accuracy:.1%}")
            
            print("  常见预测错误:")
            for error, count in sorted(error_patterns.items(), key=lambda x: x[1], reverse=True)[:5]:
                print(f"    {error}: {count}次")
    
    def find_hidden_patterns(self) -> None:
        """寻找隐藏模式"""
        print("\n" + "=" * 60)
        print("隐藏模式发现")
        print("=" * 60)
        
        # 分析周期性模式
        red_odd_even_seq = self.analyzer.get_feature_sequence('red_odd_even')
        
        print("\n检查周期性模式:")
        for period in [3, 5, 7, 10]:
            pattern_matches = 0
            total_checks = 0
            
            for i in range(period, len(red_odd_even_seq)):
                if red_odd_even_seq[i] == red_odd_even_seq[i - period]:
                    pattern_matches += 1
                total_checks += 1
            
            if total_checks > 0:
                match_rate = pattern_matches / total_checks
                print(f"  {period}期周期匹配率: {match_rate:.1%}")
        
        # 分析组合模式
        print("\n分析状态组合模式:")
        combined_states = []
        red_odd_even = self.analyzer.get_feature_sequence('red_odd_even')
        red_size = self.analyzer.get_feature_sequence('red_size')
        
        min_len = min(len(red_odd_even), len(red_size))
        for i in range(min_len):
            combined_states.append(f"{red_odd_even[i]}+{red_size[i]}")
        
        combined_freq = Counter(combined_states)
        print("  最常见的状态组合:")
        for combo, count in combined_freq.most_common(10):
            freq = count / len(combined_states)
            print(f"    {combo}: {count}次 ({freq:.1%})")
    
    def generate_insights(self) -> None:
        """生成洞察和建议"""
        print("\n" + "=" * 60)
        print("洞察和改进建议")
        print("=" * 60)
        
        # 计算各特征的可预测性
        features = ['red_odd_even', 'red_size', 'blue_size']
        predictability_scores = {}
        
        for feature in features:
            sequence = self.analyzer.get_feature_sequence(feature)
            if len(sequence) < 10:
                continue
            
            # 计算状态分布的熵
            freq = self.analyzer.calculate_state_frequencies(feature)
            entropy = -sum(p * np.log2(p + 1e-10) for p in freq.values() if p > 0)
            
            # 计算状态变化的规律性
            changes = sum(1 for i in range(1, len(sequence)) if sequence[i] != sequence[i-1])
            change_rate = changes / (len(sequence) - 1) if len(sequence) > 1 else 0
            
            # 综合可预测性分数（熵越低、变化率适中越好预测）
            predictability = (1 - entropy / 3) * (1 - abs(change_rate - 0.5))
            predictability_scores[feature] = predictability
        
        print("\n特征可预测性评分:")
        for feature, score in sorted(predictability_scores.items(), key=lambda x: x[1], reverse=True):
            print(f"  {feature}: {score:.3f}")
        
        print("\n改进建议:")
        print("1. 蓝球预测相对准确，应该重点优化红球预测算法")
        print("2. 红球状态变化较为随机，需要考虑更长的历史周期")
        print("3. 可以尝试基于号码频率而非状态比例的预测方法")
        print("4. 考虑引入外部因素（如日期、节假日等）")
        print("5. 使用集成学习方法结合多种预测策略")


def main():
    """主函数"""
    analyzer = DeepDataAnalyzer()
    
    print("开始深度数据分析...")
    
    # 执行各种分析
    analyzer.analyze_state_distribution()
    analyzer.analyze_state_transition_patterns()
    analyzer.analyze_prediction_difficulty()
    analyzer.analyze_number_patterns()
    analyzer.analyze_recent_trends(50)
    analyzer.analyze_prediction_errors()
    analyzer.find_hidden_patterns()
    analyzer.generate_insights()
    
    print("\n分析完成！")


if __name__ == "__main__":
    main()
