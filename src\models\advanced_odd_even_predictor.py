"""
高级红球奇偶比预测器
采用更激进的优化策略，包括反向预测和模式识别
"""

import numpy as np
from typing import List, Dict, Tuple
from collections import Counter, deque
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.utils.utils import get_all_red_states


class AdvancedOddEvenPredictor:
    """高级红球奇偶比预测器"""
    
    def __init__(self):
        self.name = "高级奇偶比预测器"
        
        # 基于深度分析的激进优化参数
        self.state_weights = {
            '3:2': 0.369,  # 历史频率36.9%
            '2:3': 0.315,  # 历史频率31.5%
            '4:1': 0.151,  # 历史频率15.1%
            '1:4': 0.123,  # 历史频率12.3%
            '5:0': 0.025,  # 历史频率2.5%
            '0:5': 0.017   # 历史频率1.7%
        }
        
        # 反向预测权重（基于观察：连续性较低）
        self.anti_consecutive_weight = 0.7  # 强化反连续性
        
        # 周期性模式权重
        self.cycle_patterns = {
            2: 0.3,  # 2期周期
            3: 0.4,  # 3期周期
            5: 0.2,  # 5期周期
            7: 0.1   # 7期周期
        }
        
        # 状态转移的反向权重（避免最常见的转移）
        self.anti_transition_matrix = {
            '3:2': {'2:3': 0.4, '4:1': 0.3, '1:4': 0.2, '3:2': 0.1},  # 避免3:2->3:2
            '2:3': {'3:2': 0.4, '1:4': 0.3, '4:1': 0.2, '2:3': 0.1},  # 避免2:3->2:3
            '4:1': {'2:3': 0.4, '3:2': 0.3, '1:4': 0.2, '4:1': 0.1},
            '1:4': {'3:2': 0.4, '2:3': 0.3, '4:1': 0.2, '1:4': 0.1},
            '5:0': {'2:3': 0.5, '3:2': 0.3, '1:4': 0.2},
            '0:5': {'3:2': 0.5, '2:3': 0.3, '4:1': 0.2}
        }
        
        # 热度衰减因子
        self.heat_decay = 0.8
        
        self.is_trained = False
        self.recent_states = []
        self.state_heat = {}  # 状态热度追踪
    
    def train(self, state_sequence: List[str]):
        """
        训练预测器
        
        Args:
            state_sequence: 状态序列（从新到旧）
        """
        self.recent_states = state_sequence[:30]  # 保存最近30期状态
        self._calculate_state_heat()
        self._analyze_patterns()
        self.is_trained = True
    
    def _calculate_state_heat(self):
        """计算状态热度"""
        self.state_heat = {}
        
        # 计算最近10期的状态热度
        recent_10 = self.recent_states[:10]
        for i, state in enumerate(recent_10):
            weight = (10 - i) / 10  # 越近的权重越高
            self.state_heat[state] = self.state_heat.get(state, 0) + weight
        
        # 归一化
        total_heat = sum(self.state_heat.values())
        if total_heat > 0:
            self.state_heat = {k: v/total_heat for k, v in self.state_heat.items()}
    
    def _analyze_patterns(self):
        """分析周期性模式"""
        self.pattern_scores = {}
        
        for cycle_len in self.cycle_patterns.keys():
            if len(self.recent_states) >= cycle_len * 2:
                score = 0
                for i in range(cycle_len, min(len(self.recent_states), cycle_len * 3)):
                    if self.recent_states[i] == self.recent_states[i - cycle_len]:
                        score += 1
                
                self.pattern_scores[cycle_len] = score / (len(self.recent_states) - cycle_len)
    
    def predict(self, current_state: str = None) -> Tuple[str, float]:
        """
        预测下一期的奇偶比状态
        
        Args:
            current_state: 当前状态
            
        Returns:
            Tuple[str, float]: (预测状态, 置信度)
        """
        if not self.is_trained:
            return self._predict_by_frequency()
        
        # 多策略融合预测
        anti_consecutive_pred = self._predict_anti_consecutive(current_state)
        pattern_pred = self._predict_by_pattern()
        heat_pred = self._predict_by_heat()
        frequency_pred = self._predict_by_frequency()
        
        # 融合预测结果
        final_probs = {}
        all_states = get_all_red_states()
        
        for state in all_states:
            prob = (
                anti_consecutive_pred[1].get(state, 0) * 0.4 +  # 反连续性权重最高
                pattern_pred[1].get(state, 0) * 0.3 +           # 模式权重
                heat_pred[1].get(state, 0) * 0.2 +              # 热度权重
                frequency_pred[1].get(state, 0) * 0.1           # 频率权重最低
            )
            final_probs[state] = prob
        
        # 归一化
        total_prob = sum(final_probs.values())
        if total_prob > 0:
            final_probs = {k: v/total_prob for k, v in final_probs.items()}
        
        # 选择概率最高的状态
        best_state = max(final_probs.items(), key=lambda x: x[1])
        
        # 计算置信度
        confidence = self._calculate_advanced_confidence(final_probs, best_state[1], current_state)
        
        return best_state[0], confidence
    
    def _predict_anti_consecutive(self, current_state: str) -> Tuple[str, Dict[str, float]]:
        """反连续性预测"""
        if not current_state or current_state not in self.anti_transition_matrix:
            return self._predict_by_frequency()
        
        # 使用反转移矩阵
        probs = self.anti_transition_matrix[current_state].copy()
        
        # 如果当前状态在最近3期出现过多次，进一步降低其权重
        recent_3 = self.recent_states[:3]
        current_count = recent_3.count(current_state)
        
        if current_count >= 2:  # 如果最近3期出现2次以上
            probs[current_state] = probs.get(current_state, 0.1) * 0.3  # 大幅降低
        
        # 归一化
        total = sum(probs.values())
        if total > 0:
            probs = {k: v/total for k, v in probs.items()}
        
        best_state = max(probs.items(), key=lambda x: x[1])
        return best_state[0], probs
    
    def _predict_by_pattern(self) -> Tuple[str, Dict[str, float]]:
        """基于周期性模式预测"""
        probs = {state: 0 for state in get_all_red_states()}
        
        for cycle_len, cycle_weight in self.cycle_patterns.items():
            if len(self.recent_states) > cycle_len:
                pattern_state = self.recent_states[cycle_len]
                pattern_score = self.pattern_scores.get(cycle_len, 0)
                probs[pattern_state] += cycle_weight * pattern_score
        
        # 如果没有明显模式，使用频率分布
        if sum(probs.values()) == 0:
            probs = self.state_weights.copy()
        else:
            # 归一化
            total = sum(probs.values())
            probs = {k: v/total for k, v in probs.items()}
        
        best_state = max(probs.items(), key=lambda x: x[1])
        return best_state[0], probs
    
    def _predict_by_heat(self) -> Tuple[str, Dict[str, float]]:
        """基于状态热度的反向预测"""
        probs = {}
        
        # 反向热度：热度高的状态，下期概率降低
        for state in get_all_red_states():
            heat = self.state_heat.get(state, 0)
            # 反向权重：热度越高，下期概率越低
            probs[state] = self.state_weights[state] * (1 - heat * self.heat_decay)
        
        # 归一化
        total = sum(probs.values())
        if total > 0:
            probs = {k: v/total for k, v in probs.items()}
        
        best_state = max(probs.items(), key=lambda x: x[1])
        return best_state[0], probs
    
    def _predict_by_frequency(self) -> Tuple[str, Dict[str, float]]:
        """基于历史频率预测"""
        probs = self.state_weights.copy()
        best_state = max(probs.items(), key=lambda x: x[1])
        return best_state[0], probs
    
    def _calculate_advanced_confidence(self, probs: Dict[str, float], max_prob: float, current_state: str) -> float:
        """
        计算高级置信度
        
        Args:
            probs: 概率分布
            max_prob: 最大概率
            current_state: 当前状态
            
        Returns:
            float: 置信度
        """
        # 基础置信度
        base_confidence = max_prob
        
        # 反连续性加成
        predicted_state = max(probs.items(), key=lambda x: x[1])[0]
        anti_consecutive_bonus = 0
        if current_state and predicted_state != current_state:
            anti_consecutive_bonus = 0.15  # 反连续性加成
        
        # 模式一致性加成
        pattern_bonus = 0
        for cycle_len in [2, 3, 5]:
            if len(self.recent_states) > cycle_len:
                if self.recent_states[cycle_len] == predicted_state:
                    pattern_score = self.pattern_scores.get(cycle_len, 0)
                    pattern_bonus += pattern_score * 0.1
        
        # 热度反向加成
        heat_bonus = 0
        predicted_heat = self.state_heat.get(predicted_state, 0)
        if predicted_heat < 0.2:  # 如果预测状态热度较低
            heat_bonus = 0.1
        
        # 综合置信度
        confidence = (
            base_confidence * 0.6 +
            anti_consecutive_bonus +
            pattern_bonus +
            heat_bonus
        )
        
        # 确保置信度在合理范围内
        confidence = max(0.35, min(0.75, confidence))
        
        return confidence
    
    def get_prediction_details(self, current_state: str = None) -> Dict:
        """获取详细的预测信息"""
        if not self.is_trained:
            return {"error": "预测器未训练"}
        
        anti_consecutive_pred = self._predict_anti_consecutive(current_state)
        pattern_pred = self._predict_by_pattern()
        heat_pred = self._predict_by_heat()
        frequency_pred = self._predict_by_frequency()
        final_pred = self.predict(current_state)
        
        return {
            "anti_consecutive_prediction": anti_consecutive_pred,
            "pattern_prediction": pattern_pred,
            "heat_prediction": heat_pred,
            "frequency_prediction": frequency_pred,
            "final_prediction": final_pred,
            "current_state": current_state,
            "recent_states": self.recent_states[:10],
            "state_heat": self.state_heat,
            "pattern_scores": getattr(self, 'pattern_scores', {}),
            "weights": {
                "anti_consecutive": 0.4,
                "pattern": 0.3,
                "heat": 0.2,
                "frequency": 0.1
            }
        }
