"""
V4.0 Transformer预测器测试脚本
验证基本功能和接口兼容性
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

import pandas as pd
import numpy as np
from typing import Dict, Any

try:
    from .v4_transformer_config import V4TransformerConfig, V4ConfigTemplates
    from .v4_transformer_predictor import V4TransformerPredictor
except ImportError:
    # 直接导入（用于独立运行）
    from v4_transformer_config import V4TransformerConfig, V4ConfigTemplates
    from v4_transformer_predictor import V4TransformerPredictor


def create_sample_data(num_periods: int = 100) -> pd.DataFrame:
    """创建示例彩票数据"""
    data = []
    
    for i in range(num_periods):
        period = f"240{i:02d}"
        
        # 生成随机红球（1-35，选5个）
        red_balls = sorted(np.random.choice(range(1, 36), 5, replace=False))
        
        # 生成随机蓝球（1-12，选2个）
        blue_balls = sorted(np.random.choice(range(1, 13), 2, replace=False))
        
        row = {
            '期号': period,
            '红球1': red_balls[0],
            '红球2': red_balls[1],
            '红球3': red_balls[2],
            '红球4': red_balls[3],
            '红球5': red_balls[4],
            '蓝球1': blue_balls[0],
            '蓝球2': blue_balls[1]
        }
        
        data.append(row)
    
    return pd.DataFrame(data)


def test_v4_transformer_config():
    """测试V4配置类"""
    print("=== 测试V4 Transformer配置 ===")
    
    # 测试默认配置
    config = V4TransformerConfig()
    print(f"默认配置验证: {config.validate()}")
    print(f"模型摘要: {config.get_model_summary()}")
    
    # 测试配置模板
    fast_config = V4ConfigTemplates.get_fast_training_config()
    print(f"快速训练配置验证: {fast_config.validate()}")
    
    high_acc_config = V4ConfigTemplates.get_high_accuracy_config()
    print(f"高精度配置验证: {high_acc_config.validate()}")
    
    lightweight_config = V4ConfigTemplates.get_lightweight_config()
    print(f"轻量级配置验证: {lightweight_config.validate()}")
    
    print("[成功] V4配置测试通过\n")


def test_v4_transformer_predictor():
    """测试V4预测器"""
    print("=== 测试V4 Transformer预测器 ===")
    
    # 创建预测器
    config = V4ConfigTemplates.get_lightweight_config()  # 使用轻量级配置测试
    predictor = V4TransformerPredictor(config)
    
    print(f"预测器名称: {predictor.name}")
    print(f"预测器类型: {predictor.prediction_type}")
    print(f"是否已训练: {predictor.is_trained}")
    
    # 创建测试数据
    test_data = create_sample_data(50)
    print(f"测试数据形状: {test_data.shape}")
    
    # 测试预测（未训练状态）
    target_index = 30
    result = predictor.predict(test_data, target_index)
    
    print(f"预测结果类型: {type(result)}")
    print(f"预测成功: {result.metadata.get('success', False)}")
    print(f"预测方法: {result.metadata.get('method', 'unknown')}")
    print(f"置信度: {result.confidence}")
    
    if result.value:
        print("预测内容:")
        for key, value in result.value.items():
            print(f"  {key}: {value}")
    
    print("[成功] V4预测器基础测试通过\n")


def test_v4_transformer_training():
    """测试V4训练功能"""
    print("=== 测试V4 Transformer训练 ===")
    
    # 创建预测器
    config = V4ConfigTemplates.get_fast_training_config()
    config.epochs = 2  # 减少训练轮数用于测试
    config.verbose_training = True
    
    predictor = V4TransformerPredictor(config)
    
    # 创建训练数据
    train_data = create_sample_data(80)
    print(f"训练数据形状: {train_data.shape}")
    
    # 尝试训练
    print("开始训练...")
    training_success = predictor.train(train_data)
    
    print(f"训练结果: {'成功' if training_success else '失败'}")
    print(f"训练后状态: {predictor.is_trained}")
    
    if training_success:
        # 测试训练后的预测
        target_index = 50
        result = predictor.predict(train_data, target_index)
        
        print(f"训练后预测成功: {result.metadata.get('success', False)}")
        print(f"预测方法: {result.metadata.get('method', 'unknown')}")
        print(f"置信度: {result.confidence}")
    
    print("[成功] V4训练测试完成\n")


def test_v4_model_summary():
    """测试模型摘要功能"""
    print("=== 测试V4模型摘要 ===")
    
    predictor = V4TransformerPredictor()
    summary = predictor.get_model_summary()
    
    print("模型摘要:")
    for key, value in summary.items():
        if isinstance(value, dict):
            print(f"  {key}:")
            for sub_key, sub_value in value.items():
                print(f"    {sub_key}: {sub_value}")
        else:
            print(f"  {key}: {value}")
    
    print("[成功] 模型摘要测试通过\n")


def main():
    """主测试函数"""
    print("[启动] V4.0 Transformer预测器测试开始\n")
    
    try:
        # 测试配置
        test_v4_transformer_config()
        
        # 测试预测器基础功能
        test_v4_transformer_predictor()
        
        # 测试训练功能
        test_v4_transformer_training()
        
        # 测试模型摘要
        test_v4_model_summary()
        
        print("[完成] 所有测试通过！V4.0 Transformer预测器基础功能正常")
        
    except Exception as e:
        print(f"[失败] 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()