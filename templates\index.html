<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大乐透彩票预测系统</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #333; margin-bottom: 30px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .button:hover { background: #0056b3; }
        .result { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .number { display: inline-block; background: #dc3545; color: white; padding: 5px 10px; margin: 2px; border-radius: 50%; font-weight: bold; }
        .blue-number { background: #007bff; }
        .loading { color: #666; font-style: italic; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; }
        .success { color: #155724; background: #d4edda; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 大乐透彩票预测系统</h1>
            <p>基于机器学习的智能预测系统</p>
        </div>
        
        <div class="section">
            <h3>📊 系统信息</h3>
            <div id="dataInfo">加载中...</div>
        </div>
        
        <div class="section">
            <h3>🎯 号码预测</h3>
            <button class="button" onclick="predict()">开始预测</button>
            <div id="predictionResult"></div>
        </div>
        
        <div class="section">
            <h3>📈 准确率统计</h3>
            <button class="button" onclick="showAccuracy()">查看统计</button>
            <div id="accuracyResult"></div>
        </div>
    </div>

    <script>
        // 加载数据信息
        function loadDataInfo() {
            fetch('/api/data_info')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('dataInfo').innerHTML = `
                            <p>📊 总期数: ${data.total_periods}</p>
                            <p>📅 最新期号: ${data.latest_period}</p>
                            <p>📈 数据范围: ${data.data_range}</p>
                        `;
                    } else {
                        document.getElementById('dataInfo').innerHTML = `<div class="error">❌ ${data.error}</div>`;
                    }
                })
                .catch(error => {
                    document.getElementById('dataInfo').innerHTML = `<div class="error">❌ 加载失败: ${error}</div>`;
                });
        }
        
        // 预测功能
        function predict() {
            document.getElementById('predictionResult').innerHTML = '<div class="loading">🔄 预测中...</div>';
            
            fetch('/api/predict', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        let html = `<div class="success">✅ 预测完成！期号: ${data.period}</div>`;
                        
                        // 比例预测
                        html += '<h4>📊 比例预测:</h4>';
                        const predictions = data.predictions;
                        if (predictions.red_odd_even) {
                            html += `<p>红球奇偶比: ${predictions.red_odd_even[0]} (置信度: ${predictions.red_odd_even[1].toFixed(3)})</p>`;
                        }
                        if (predictions.red_size) {
                            html += `<p>红球大小比: ${predictions.red_size[0]} (置信度: ${predictions.red_size[1].toFixed(3)})</p>`;
                        }
                        if (predictions.blue_size) {
                            html += `<p>蓝球大小比: ${predictions.blue_size[0]} (置信度: ${predictions.blue_size[1].toFixed(3)})</p>`;
                        }
                        
                        // 推荐号码
                        if (data.enhanced_selection) {
                            const [red, blue] = data.enhanced_selection;
                            html += '<h4>⭐ 推荐号码:</h4><div>';
                            red.forEach(num => html += `<span class="number">${num.toString().padStart(2, '0')}</span>`);
                            html += ' + ';
                            blue.forEach(num => html += `<span class="number blue-number">${num.toString().padStart(2, '0')}</span>`);
                            html += '</div>';
                        }
                        
                        // 贝叶斯推荐
                        if (data.bayes_selected && data.bayes_selected.length > 0) {
                            html += '<h4>🎯 贝叶斯推荐组合:</h4>';
                            data.bayes_selected.forEach((combo, i) => {
                                html += `<p>第${i+1}名: `;
                                combo.red_balls.forEach(num => html += `<span class="number">${num.toString().padStart(2, '0')}</span>`);
                                html += ' + ';
                                combo.blue_balls.forEach(num => html += `<span class="number blue-number">${num.toString().padStart(2, '0')}</span>`);
                                html += ` (置信度: ${combo.confidence}%)</p>`;
                            });
                        }
                        
                        html += `<p>🔪 杀号成功率: ${(data.kill_success_rate * 100).toFixed(1)}%</p>`;
                        
                        document.getElementById('predictionResult').innerHTML = html;
                    } else {
                        document.getElementById('predictionResult').innerHTML = `<div class="error">❌ ${data.error}</div>`;
                    }
                })
                .catch(error => {
                    document.getElementById('predictionResult').innerHTML = `<div class="error">❌ 预测失败: ${error}</div>`;
                });
        }
        
        // 准确率统计
        function showAccuracy() {
            document.getElementById('accuracyResult').innerHTML = '<div class="loading">🔄 加载中...</div>';
            
            fetch('/api/accuracy')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        if (data.report.error) {
                            document.getElementById('accuracyResult').innerHTML = `<div class="error">❌ ${data.report.error}</div>`;
                        } else {
                            let html = `<div class="success">📊 总预测次数: ${data.report.总预测次数}</div>`;
                            // 这里可以添加更多统计信息的显示
                            html += '<p>详细统计信息请查看控制台输出</p>';
                            document.getElementById('accuracyResult').innerHTML = html;
                        }
                    } else {
                        document.getElementById('accuracyResult').innerHTML = `<div class="error">❌ ${data.error}</div>`;
                    }
                })
                .catch(error => {
                    document.getElementById('accuracyResult').innerHTML = `<div class="error">❌ 加载失败: ${error}</div>`;
                });
        }
        
        // 页面加载时初始化
        window.onload = function() {
            loadDataInfo();
        };
    </script>
</body>
</html>