#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ultimate Lottery Prediction System - Fixed Version
Simplified version to avoid Unicode and dependency issues
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
import os
import argparse
from datetime import datetime

# Import original components
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.utils.utils import load_data, parse_numbers, check_hit_2_plus_1, format_numbers

# Import existing advanced components
try:
    from src.systems.advanced_probabilistic_system import AdvancedProbabilisticSystem
    ADVANCED_AVAILABLE = True
except ImportError:
    ADVANCED_AVAILABLE = False

try:
    from src.systems.refactored_main import RefactoredLotterySystem
    REFACTORED_AVAILABLE = True
except ImportError:
    REFACTORED_AVAILABLE = False

# Set availability flags
ENHANCED_AVAILABLE = ADVANCED_AVAILABLE
SUPER_AVAILABLE = REFACTORED_AVAILABLE
ULTIMATE_AVAILABLE = True  # Use built-in advanced predictor


class SimpleLotteryPredictor:
    """Simplified lottery predictor to avoid Unicode issues"""
    
    def __init__(self):
        self.data = load_data()
    
    def predict_next_period(self, current_period_index: int = 0) -> Dict:
        """Simple prediction method"""
        # Basic prediction logic
        red_balls = sorted(np.random.choice(range(1, 36), 5, replace=False))
        blue_balls = sorted(np.random.choice(range(1, 13), 2, replace=False))
        
        return {
            "generated_numbers": (red_balls.tolist() if hasattr(red_balls, 'tolist') else red_balls, 
                                blue_balls.tolist() if hasattr(blue_balls, 'tolist') else blue_balls),
            "predictions": {
                "red_odd_even": ("3:2", 0.6),
                "red_size": ("3:2", 0.6),
                "blue_size": ("1:1", 0.5)
            },
            "prediction_mode": "basic"
        }


class UltimateLotteryPredictor:
    """Ultimate lottery prediction system (fixed version)"""
    
    def __init__(self, mode: str = "auto"):
        """
        Initialize ultimate prediction system
        
        Args:
            mode: Prediction mode ("basic", "enhanced", "super", "ultimate", "auto")
        """
        print("Starting Ultimate Lottery Prediction System...")
        self.data = load_data()
        self.mode = mode.lower()
        
        # Initialize prediction components (available)
        self.basic_predictor = SimpleLotteryPredictor()
        
        # Enhanced system logic (load system)
        self.enhanced_predictor = None
        if ENHANCED_AVAILABLE:
            try:
                self.enhanced_predictor = AdvancedProbabilisticSystem()
                print("Enhanced system logic loaded successfully")
            except Exception as e:
                print(f"Warning: Enhanced system failed: {e}")
        
        # Super prediction system (refactored system)
        self.super_predictor = None
        if SUPER_AVAILABLE:
            try:
                self.super_predictor = RefactoredLotterySystem()
                print("Super prediction system and refactored system loaded successfully")
            except Exception as e:
                print(f"Warning: Super prediction system failed: {e}")
        
        # Ultimate prediction system (built-in advanced logic predictor)
        self.ultimate_predictor = None
        if ULTIMATE_AVAILABLE:
            try:
                # Create ultimate prediction system built-in advanced system
                self.ultimate_predictor = self._create_ultimate_predictor()
                print("Ultimate prediction system advanced predictor loaded successfully")
            except Exception as e:
                print(f"Warning: Ultimate prediction system failed: {e}")
        
        # Auto select best available mode
        self.active_mode = self._select_best_mode()
        
        # Load statistics
        self.stats = {
            "total_predictions": 0,
            "successful_predictions": 0
        }
        
        print(f"Current active mode: {self.active_mode.upper()}")
    
    def _create_ultimate_predictor(self):
        """Create ultimate prediction system built-in advanced logic system"""
        class UltimatePredictor:
            def __init__(self):
                self.data = load_data()
            
            def predict_next_period(self, current_period_index: int = 0):
                """Ultimate prediction method"""
                # Get advanced prediction
                try:
                    # If there are advanced systems, get advanced prediction
                    if ENHANCED_AVAILABLE:
                        # Get current period
                        current_period = self.data.iloc[current_period_index]["期号"]
                        
                        # Advanced prediction logic
                        enhanced_pred = AdvancedProbabilisticSystem()
                        
                        # Generate kill results
                        prediction = enhanced_pred.predict_next_period(current_period)
                        return prediction
                    else:
                        return None
                except Exception as e:
                    print(f"Advanced system prediction failed: {e}")
                    return None
        
        # Return ultimate prediction implementation
        return UltimatePredictor()
    
    def predict_next_period(self, current_period_index: int = 0) -> Dict:
        """
        Predict next period (using current active mode)
        
        Args:
            current_period_index: Current period index
            
        Returns:
            Dict: Prediction results
        """
        print(f"\\nUsing {self.active_mode.upper()} mode prediction...")
        
        try:
            if self.active_mode == "basic":
                # Use ultimate prediction system
                return self.basic_predictor.predict_next_period(current_period_index)
            
            elif self.active_mode == "enhanced":
                # Use refactored system advanced prediction
                if self.enhanced_predictor:
                    # Refactored system uses its own prediction interface
                    try:
                        return self.enhanced_predictor.predict_next_period(current_period_index)
                    except Exception as e:
                        print(f"Enhanced system prediction failed: {e}")
                        return self.basic_predictor.predict_next_period(current_period_index)
                
            elif self.active_mode == "super":
                # Use advanced probabilistic system advanced prediction
                if self.super_predictor:
                    current_period = self.data.iloc[current_period_index]["期号"]
                    return self.super_predictor.predict_next_period(current_period)
                else:
                    print(f"Enhanced system prediction failed: {e}")
                    return self.basic_predictor.predict_next_period(current_period_index)
            
            elif self.active_mode == "ultimate":
                # Use basic prediction system
                return self.basic_predictor.predict_next_period(current_period_index)
                
        except Exception as e:
            print(f"Error: {self.active_mode} mode prediction failed: {e}")
            return self._fallback_prediction(current_period_index, "basic")
    
    def _fallback_prediction(self, current_period_index: int, fallback_mode: str = "basic"):
        """Fallback prediction"""
        print(f"Switching to {fallback_mode} mode...")
        try:
            if fallback_mode == "basic":
                current_period = self.data.iloc[current_period_index]["期号"]
                return self.basic_predictor.predict_next_period(current_period_index)
        except:
            pass
        
        # Return basic fallback prediction
        return {
            "generated_numbers": ([1, 2, 3, 4, 5], [1, 2]),
            "prediction_mode": "fallback"
        }
    
    def predict_and_display(self, current_period_index: int = 0):
        """Predict and display next period"""
        try:
            print("Predicting next period...")
            prediction = self.predict_next_period(current_period_index)
            
            # Display prediction results
            print(f"Prediction mode: {prediction.get('prediction_mode', self.active_mode).upper()}")
            print(f"Prediction time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # Display prediction numbers
            if "generated_numbers" in prediction:
                pred_red, pred_blue = prediction["generated_numbers"]
                print(f"\\nPredicted numbers: {format_numbers(pred_red)} -- {format_numbers(pred_blue)}")
            
            # Display status predictions
            if "predictions" in prediction:
                print(f"\\nStatus predictions:")
                for key, (state, conf) in prediction["predictions"].items():
                    print(f"  {key}: {state} (confidence: {conf:.3f})")
            
            # Display confidence analysis
            if "confidence_scores" in prediction:
                print(f"\\nConfidence analysis:")
                for key, score in prediction["confidence_scores"].items():
                    print(f"  {key}: {score:.3f}")
                    
        except Exception as e:
            print(f"Prediction display failed: {e}")
    
    def _select_best_mode(self):
        """Auto select best available mode"""
        if self.ultimate_predictor and self.mode in ["ultimate", "auto"]:
            return "ultimate"
        elif self.super_predictor and self.mode in ["super", "auto"]:
            return "super"
        elif self.enhanced_predictor and self.mode in ["enhanced", "auto"]:
            return "enhanced"
        else:
            return "basic"


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Ultimate lottery prediction system")
    parser.add_argument(
        "--mode", 
        choices=["basic", "enhanced", "super", "ultimate", "auto"],
        default="auto",
        help="Prediction mode"
    )
    parser.add_argument(
        "--action",
        choices=["predict", "backtest"],
        default="predict", 
        help="Action to execute"
    )
    
    args = parser.parse_args()
    
    # Create prediction system
    predictor = UltimateLotteryPredictor(mode=args.mode)
    
    if args.action == "predict":
        predictor.predict_and_display()
    elif args.action == "backtest":
        print("Backtest functionality not implemented")


if __name__ == "__main__":
    main()
