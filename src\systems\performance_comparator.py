#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Performance Comparator - 性能对比器
对比深度学习和传统机器学习方法的性能
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Tuple, Optional
import logging
import time
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 统计分析相关导入
from scipy import stats
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path


class PerformanceComparator:
    """性能对比器"""
    
    def __init__(self, config):
        """
        初始化性能对比器
        
        Args:
            config: 系统配置
        """
        self.config = config
        self.logger = logging.getLogger(f"lottery_predictor.{self.__class__.__name__}")
        
        # 评估指标
        self.metrics = {
            'hit_rate': self._calculate_hit_rate,
            'roi': self._calculate_roi,
            'stability': self._calculate_stability,
            'accuracy': self._calculate_accuracy,
            'precision': self._calculate_precision
        }
        
        # 结果存储路径
        self.results_dir = Path("results/performance_comparison")
        self.results_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger.info("性能对比器初始化完成")
    
    def compare(self, dl_results: Dict[str, Any], ml_results: Dict[str, Any], 
                processed_data: Dict[str, Any]) -> Dict[str, float]:
        """
        对比深度学习和传统机器学习的性能
        
        Args:
            dl_results: 深度学习结果
            ml_results: 传统机器学习结果
            processed_data: 处理后的数据
            
        Returns:
            Dict: 对比指标
        """
        self.logger.info("开始性能对比分析...")
        
        # 进行回测评估
        dl_backtest = self._backtest_method(dl_results, processed_data, 'deep_learning')
        ml_backtest = self._backtest_method(ml_results, processed_data, 'traditional_ml')
        
        # 计算各项指标
        comparison_metrics = {}
        
        # 深度学习指标
        comparison_metrics.update(self._calculate_all_metrics(dl_backtest, 'dl'))
        
        # 传统机器学习指标
        comparison_metrics.update(self._calculate_all_metrics(ml_backtest, 'ml'))
        
        # 统计显著性检验
        significance_results = self._statistical_significance_test(dl_backtest, ml_backtest)
        comparison_metrics.update(significance_results)
        
        # 计算相对性能
        relative_performance = self._calculate_relative_performance(comparison_metrics)
        comparison_metrics.update(relative_performance)
        
        # 生成可视化报告
        if self.config.generate_report:
            self._generate_visualization(dl_backtest, ml_backtest, comparison_metrics)
        
        self.logger.info("性能对比分析完成")
        return comparison_metrics
    
    def _backtest_method(self, method_results: Dict[str, Any], processed_data: Dict[str, Any], 
                        method_name: str) -> Dict[str, Any]:
        """
        对指定方法进行回测
        
        Args:
            method_results: 方法结果
            processed_data: 处理后的数据
            method_name: 方法名称
            
        Returns:
            Dict: 回测结果
        """
        self.logger.info(f"开始 {method_name} 回测...")
        
        # 获取测试数据
        test_data = processed_data['test_data']
        test_periods = min(self.config.backtest_periods, len(test_data['features']))
        
        backtest_results = {
            'predictions': [],
            'actual_results': [],
            'hit_rates': [],
            'periods': [],
            'prediction_times': []
        }
        
        # 模拟回测过程
        for i in range(test_periods):
            start_time = time.time()
            
            # 获取当期实际结果
            if i < len(test_data['targets']['red_balls']):
                actual_red = test_data['targets']['red_balls'][i]
                actual_blue = test_data['targets']['blue_balls'][i]
                
                # 模拟预测（这里使用方法结果作为示例）
                predicted_red = method_results.get('red_balls', [1, 2, 3, 4, 5])
                predicted_blue = method_results.get('blue_balls', [1, 2])
                
                # 添加一些随机性来模拟不同期的预测差异
                if i > 0:
                    # 随机调整预测结果
                    predicted_red = self._adjust_prediction(predicted_red, 1, 35)
                    predicted_blue = self._adjust_prediction(predicted_blue, 1, 12)
                
                prediction_time = time.time() - start_time
                
                # 计算命中率
                red_hits = len(set(predicted_red) & set(actual_red))
                blue_hits = len(set(predicted_blue) & set(actual_blue))
                total_hits = red_hits + blue_hits
                hit_rate = total_hits / 7.0  # 总共7个号码
                
                # 存储结果
                backtest_results['predictions'].append({
                    'red_balls': predicted_red,
                    'blue_balls': predicted_blue
                })
                backtest_results['actual_results'].append({
                    'red_balls': actual_red.tolist() if hasattr(actual_red, 'tolist') else actual_red,
                    'blue_balls': actual_blue.tolist() if hasattr(actual_blue, 'tolist') else actual_blue
                })
                backtest_results['hit_rates'].append(hit_rate)
                backtest_results['periods'].append(i)
                backtest_results['prediction_times'].append(prediction_time)
        
        self.logger.info(f"{method_name} 回测完成，共测试 {len(backtest_results['hit_rates'])} 期")
        return backtest_results
    
    def _adjust_prediction(self, prediction: List[int], min_val: int, max_val: int) -> List[int]:
        """调整预测结果以增加多样性"""
        adjusted = prediction.copy()
        
        # 随机替换1-2个号码
        num_changes = np.random.randint(1, min(3, len(adjusted) + 1))
        
        for _ in range(num_changes):
            if len(adjusted) > 0:
                # 随机选择一个位置替换
                idx = np.random.randint(0, len(adjusted))
                new_num = np.random.randint(min_val, max_val + 1)
                
                # 确保不重复
                while new_num in adjusted:
                    new_num = np.random.randint(min_val, max_val + 1)
                
                adjusted[idx] = new_num
        
        return sorted(adjusted)
    
    def _calculate_all_metrics(self, backtest_results: Dict[str, Any], prefix: str) -> Dict[str, float]:
        """计算所有评估指标"""
        metrics = {}
        
        if not backtest_results['hit_rates']:
            return {f"{prefix}_{key}": 0.0 for key in self.metrics.keys()}
        
        hit_rates = backtest_results['hit_rates']
        prediction_times = backtest_results['prediction_times']
        
        # 基础指标
        metrics[f"{prefix}_hit_rate"] = np.mean(hit_rates)
        metrics[f"{prefix}_hit_rate_std"] = np.std(hit_rates)
        metrics[f"{prefix}_max_hit_rate"] = np.max(hit_rates)
        metrics[f"{prefix}_min_hit_rate"] = np.min(hit_rates)
        
        # ROI计算（简化版本）
        metrics[f"{prefix}_roi"] = self._calculate_roi(backtest_results)
        
        # 稳定性指标
        metrics[f"{prefix}_stability"] = self._calculate_stability(backtest_results)
        
        # 时间效率
        metrics[f"{prefix}_avg_prediction_time"] = np.mean(prediction_times)
        metrics[f"{prefix}_total_prediction_time"] = np.sum(prediction_times)
        
        # 一致性指标
        metrics[f"{prefix}_consistency"] = 1.0 - (np.std(hit_rates) / (np.mean(hit_rates) + 1e-8))
        
        return metrics
    
    def _calculate_hit_rate(self, backtest_results: Dict[str, Any]) -> float:
        """计算命中率"""
        if not backtest_results['hit_rates']:
            return 0.0
        return np.mean(backtest_results['hit_rates'])
    
    def _calculate_roi(self, backtest_results: Dict[str, Any]) -> float:
        """计算投资回报率（简化版本）"""
        if not backtest_results['hit_rates']:
            return 0.0
        
        # 简化的ROI计算
        # 假设每期投注2元，根据命中情况计算回报
        total_investment = len(backtest_results['hit_rates']) * 2  # 每期2元
        total_return = 0
        
        for hit_rate in backtest_results['hit_rates']:
            if hit_rate >= 0.4:  # 命中率超过40%认为有回报
                # 简化的奖金计算
                if hit_rate >= 0.8:  # 高命中率
                    total_return += 1000
                elif hit_rate >= 0.6:  # 中等命中率
                    total_return += 100
                elif hit_rate >= 0.4:  # 低命中率
                    total_return += 10
        
        roi = (total_return - total_investment) / total_investment if total_investment > 0 else 0
        return max(-1.0, min(10.0, roi))  # 限制ROI范围
    
    def _calculate_stability(self, backtest_results: Dict[str, Any]) -> float:
        """计算预测稳定性"""
        if not backtest_results['hit_rates'] or len(backtest_results['hit_rates']) < 2:
            return 0.0
        
        hit_rates = backtest_results['hit_rates']
        
        # 使用变异系数的倒数作为稳定性指标
        mean_hit_rate = np.mean(hit_rates)
        std_hit_rate = np.std(hit_rates)
        
        if mean_hit_rate == 0:
            return 0.0
        
        cv = std_hit_rate / mean_hit_rate  # 变异系数
        stability = 1.0 / (1.0 + cv)  # 稳定性指标
        
        return stability
    
    def _calculate_accuracy(self, backtest_results: Dict[str, Any]) -> float:
        """计算预测准确性"""
        return self._calculate_hit_rate(backtest_results)
    
    def _calculate_precision(self, backtest_results: Dict[str, Any]) -> float:
        """计算预测精确度"""
        if not backtest_results['predictions'] or not backtest_results['actual_results']:
            return 0.0
        
        total_precision = 0
        valid_periods = 0
        
        for pred, actual in zip(backtest_results['predictions'], backtest_results['actual_results']):
            try:
                pred_red = set(pred['red_balls'])
                actual_red = set(actual['red_balls'])
                pred_blue = set(pred['blue_balls'])
                actual_blue = set(actual['blue_balls'])
                
                # 计算精确度
                red_precision = len(pred_red & actual_red) / len(pred_red) if pred_red else 0
                blue_precision = len(pred_blue & actual_blue) / len(pred_blue) if pred_blue else 0
                
                period_precision = (red_precision * 5 + blue_precision * 2) / 7
                total_precision += period_precision
                valid_periods += 1
                
            except Exception as e:
                self.logger.warning(f"计算精确度时出错: {e}")
                continue
        
        return total_precision / valid_periods if valid_periods > 0 else 0.0
    
    def _statistical_significance_test(self, dl_backtest: Dict[str, Any], 
                                     ml_backtest: Dict[str, Any]) -> Dict[str, float]:
        """统计显著性检验"""
        results = {}
        
        try:
            dl_hit_rates = dl_backtest['hit_rates']
            ml_hit_rates = ml_backtest['hit_rates']
            
            if len(dl_hit_rates) > 1 and len(ml_hit_rates) > 1:
                # t检验
                t_stat, p_value = stats.ttest_ind(dl_hit_rates, ml_hit_rates)
                results['t_statistic'] = t_stat
                results['p_value'] = p_value
                results['significant'] = p_value < 0.05
                
                # 效应大小 (Cohen's d)
                pooled_std = np.sqrt(((len(dl_hit_rates) - 1) * np.var(dl_hit_rates) + 
                                    (len(ml_hit_rates) - 1) * np.var(ml_hit_rates)) / 
                                   (len(dl_hit_rates) + len(ml_hit_rates) - 2))
                
                if pooled_std > 0:
                    cohens_d = (np.mean(dl_hit_rates) - np.mean(ml_hit_rates)) / pooled_std
                    results['effect_size'] = cohens_d
                else:
                    results['effect_size'] = 0.0
                
                # Wilcoxon秩和检验（非参数检验）
                try:
                    w_stat, w_p_value = stats.ranksums(dl_hit_rates, ml_hit_rates)
                    results['wilcoxon_statistic'] = w_stat
                    results['wilcoxon_p_value'] = w_p_value
                except:
                    results['wilcoxon_statistic'] = 0.0
                    results['wilcoxon_p_value'] = 1.0
            
        except Exception as e:
            self.logger.warning(f"统计显著性检验失败: {e}")
            results = {
                't_statistic': 0.0,
                'p_value': 1.0,
                'significant': False,
                'effect_size': 0.0,
                'wilcoxon_statistic': 0.0,
                'wilcoxon_p_value': 1.0
            }
        
        return results
    
    def _calculate_relative_performance(self, metrics: Dict[str, float]) -> Dict[str, float]:
        """计算相对性能指标"""
        relative_metrics = {}
        
        # 命中率比较
        dl_hit_rate = metrics.get('dl_hit_rate', 0)
        ml_hit_rate = metrics.get('ml_hit_rate', 0)
        
        if ml_hit_rate > 0:
            relative_metrics['hit_rate_improvement'] = (dl_hit_rate - ml_hit_rate) / ml_hit_rate
        else:
            relative_metrics['hit_rate_improvement'] = 0.0
        
        # ROI比较
        dl_roi = metrics.get('dl_roi', 0)
        ml_roi = metrics.get('ml_roi', 0)
        
        if ml_roi != 0:
            relative_metrics['roi_improvement'] = (dl_roi - ml_roi) / abs(ml_roi)
        else:
            relative_metrics['roi_improvement'] = 0.0
        
        # 稳定性比较
        dl_stability = metrics.get('dl_stability', 0)
        ml_stability = metrics.get('ml_stability', 0)
        
        if ml_stability > 0:
            relative_metrics['stability_improvement'] = (dl_stability - ml_stability) / ml_stability
        else:
            relative_metrics['stability_improvement'] = 0.0
        
        # 时间效率比较
        dl_time = metrics.get('dl_total_prediction_time', 1)
        ml_time = metrics.get('ml_total_prediction_time', 1)
        
        relative_metrics['dl_training_time_ratio'] = dl_time / (dl_time + ml_time)
        relative_metrics['ml_training_time_ratio'] = ml_time / (dl_time + ml_time)
        
        return relative_metrics
    
    def _generate_visualization(self, dl_backtest: Dict[str, Any], ml_backtest: Dict[str, Any], 
                              metrics: Dict[str, float]):
        """生成可视化报告"""
        try:
            # 设置中文字体
            plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False
            
            # 创建图表
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
            fig.suptitle('深度学习 vs 传统机器学习 性能对比', fontsize=16)
            
            # 1. 命中率对比
            ax1 = axes[0, 0]
            periods = range(len(dl_backtest['hit_rates']))
            ax1.plot(periods, dl_backtest['hit_rates'], label='深度学习', marker='o', alpha=0.7)
            ax1.plot(periods, ml_backtest['hit_rates'], label='传统机器学习', marker='s', alpha=0.7)
            ax1.set_title('命中率变化趋势')
            ax1.set_xlabel('期数')
            ax1.set_ylabel('命中率')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            
            # 2. 性能指标对比
            ax2 = axes[0, 1]
            metrics_names = ['命中率', 'ROI', '稳定性']
            dl_values = [metrics.get('dl_hit_rate', 0), metrics.get('dl_roi', 0), metrics.get('dl_stability', 0)]
            ml_values = [metrics.get('ml_hit_rate', 0), metrics.get('ml_roi', 0), metrics.get('ml_stability', 0)]
            
            x = np.arange(len(metrics_names))
            width = 0.35
            
            ax2.bar(x - width/2, dl_values, width, label='深度学习', alpha=0.8)
            ax2.bar(x + width/2, ml_values, width, label='传统机器学习', alpha=0.8)
            ax2.set_title('关键性能指标对比')
            ax2.set_xticks(x)
            ax2.set_xticklabels(metrics_names)
            ax2.legend()
            ax2.grid(True, alpha=0.3)
            
            # 3. 命中率分布
            ax3 = axes[1, 0]
            ax3.hist(dl_backtest['hit_rates'], alpha=0.7, label='深度学习', bins=10)
            ax3.hist(ml_backtest['hit_rates'], alpha=0.7, label='传统机器学习', bins=10)
            ax3.set_title('命中率分布')
            ax3.set_xlabel('命中率')
            ax3.set_ylabel('频次')
            ax3.legend()
            ax3.grid(True, alpha=0.3)
            
            # 4. 预测时间对比
            ax4 = axes[1, 1]
            methods = ['深度学习', '传统机器学习']
            times = [
                metrics.get('dl_total_prediction_time', 0),
                metrics.get('ml_total_prediction_time', 0)
            ]
            
            bars = ax4.bar(methods, times, alpha=0.8, color=['blue', 'orange'])
            ax4.set_title('总预测时间对比')
            ax4.set_ylabel('时间 (秒)')
            
            # 在柱状图上添加数值标签
            for bar, time_val in zip(bars, times):
                height = bar.get_height()
                ax4.text(bar.get_x() + bar.get_width()/2., height,
                        f'{time_val:.2f}s', ha='center', va='bottom')
            
            plt.tight_layout()
            
            # 保存图表
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            chart_path = self.results_dir / f'performance_comparison_{timestamp}.png'
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            self.logger.info(f"可视化报告已保存: {chart_path}")
            
        except Exception as e:
            self.logger.warning(f"生成可视化报告失败: {e}")
            plt.close('all')  # 确保关闭所有图表
