"""
V4.0 Transformer与主系统集成测试
Integration Test for V4.0 Transformer with Main System
"""

import sys
import os
from pathlib import Path
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Tuple
import json
import time

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '..', '..', '..')
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入V4.0组件
from v4_transformer_config import V4TransformerConfig, V4ConfigTemplates
from v4_transformer_predictor import V4TransformerPredictor

# 导入主系统组件
try:
    from src.systems.refactored_main import RefactoredLotterySystem
except ImportError:
    print("[WARN] 无法导入主系统，将使用简化测试")
    RefactoredLotterySystem = None


class V4IntegrationTester:
    """V4.0 Transformer集成测试器"""
    
    def __init__(self):
        self.project_root = Path(project_root)
        self.data_path = self.project_root / "data" / "raw" / "dlt_data.csv"
        self.results = {}
        
    def load_real_data(self) -> pd.DataFrame:
        """加载真实历史数据"""
        print("[INFO] 加载真实历史数据...")
        
        if not self.data_path.exists():
            raise FileNotFoundError(f"数据文件不存在: {self.data_path}")
        
        # 读取CSV数据
        df = pd.read_csv(self.data_path, header=None)
        
        # 设置列名
        df.columns = ['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '蓝球1', '蓝球2', '日期']
        
        # 确保数值列是正确的数据类型
        numeric_cols = ['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '蓝球1', '蓝球2']
        for col in numeric_cols:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        print(f"[OK] 成功加载 {len(df)} 期历史数据")
        print(f"[OK] 数据范围: {df['期号'].min()} - {df['期号'].max()}")
        
        return df
    
    def test_v4_data_compatibility(self, data: pd.DataFrame) -> bool:
        """测试V4.0与真实数据的兼容性"""
        print("\n=== V4.0数据兼容性测试 ===")
        
        try:
            # 检查数据格式
            required_columns = ['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '蓝球1', '蓝球2']
            for col in required_columns:
                if col not in data.columns:
                    print(f"[ERROR] 缺少必需列: {col}")
                    return False
            
            print("[OK] 数据列格式检查通过")
            
            # 检查数据范围
            for i in range(1, 6):
                col = f'红球{i}'
                # 确保数据是数值类型
                data[col] = pd.to_numeric(data[col], errors='coerce')
                min_val, max_val = data[col].min(), data[col].max()
                if not (1 <= min_val and max_val <= 35):
                    print(f"[ERROR] {col}数据范围异常: {min_val}-{max_val}")
                    return False
            
            for i in range(1, 3):
                col = f'蓝球{i}'
                # 确保数据是数值类型
                data[col] = pd.to_numeric(data[col], errors='coerce')
                min_val, max_val = data[col].min(), data[col].max()
                if not (1 <= min_val and max_val <= 12):
                    print(f"[ERROR] {col}数据范围异常: {min_val}-{max_val}")
                    return False
            
            print("[OK] 数据范围检查通过")
            
            # 检查数据完整性
            null_counts = data.isnull().sum()
            if null_counts.sum() > 0:
                print(f"[WARN] 发现空值: {null_counts[null_counts > 0].to_dict()}")
            else:
                print("[OK] 数据完整性检查通过")
            
            return True
            
        except Exception as e:
            print(f"[ERROR] 数据兼容性测试失败: {e}")
            return False
    
    def test_v4_predictor_with_real_data(self, data: pd.DataFrame) -> Dict[str, Any]:
        """使用真实数据测试V4.0预测器"""
        print("\n=== V4.0预测器真实数据测试 ===")
        
        results = {
            "success": False,
            "predictions": [],
            "performance": {},
            "errors": []
        }
        
        try:
            # 创建V4.0预测器
            config = V4TransformerConfig()
            predictor = V4TransformerPredictor(config)
            
            print(f"[OK] V4.0预测器创建成功: {predictor.name}")
            
            # 选择测试数据（最后50期用于测试）
            test_size = 50
            if len(data) < test_size + config.sequence_length:
                test_size = max(10, len(data) - config.sequence_length)
            
            test_indices = list(range(len(data) - test_size, len(data)))
            print(f"[INFO] 使用最后 {len(test_indices)} 期数据进行测试")
            
            # 执行预测测试
            predictions = []
            prediction_times = []
            
            for i, test_idx in enumerate(test_indices[:10]):  # 限制测试数量
                start_time = time.time()
                
                try:
                    result = predictor.predict(data, test_idx)
                    prediction_time = time.time() - start_time
                    
                    prediction_info = {
                        "period": data.iloc[test_idx]['期号'],
                        "success": result.metadata.get('success', False),
                        "confidence": result.confidence,
                        "prediction_time": prediction_time,
                        "predictions": result.value if result.value else {}
                    }
                    
                    predictions.append(prediction_info)
                    prediction_times.append(prediction_time)
                    
                    print(f"[OK] 期号 {prediction_info['period']} 预测完成，耗时: {prediction_time:.3f}s")
                    
                except Exception as e:
                    error_info = f"期号 {data.iloc[test_idx]['期号']} 预测失败: {e}"
                    results["errors"].append(error_info)
                    print(f"[ERROR] {error_info}")
            
            # 计算性能统计
            if prediction_times:
                results["performance"] = {
                    "avg_prediction_time": np.mean(prediction_times),
                    "max_prediction_time": np.max(prediction_times),
                    "min_prediction_time": np.min(prediction_times),
                    "total_predictions": len(predictions),
                    "success_rate": sum(1 for p in predictions if p["success"]) / len(predictions)
                }
            
            results["predictions"] = predictions
            results["success"] = len(predictions) > 0
            
            print(f"[OK] 预测测试完成，成功率: {results['performance'].get('success_rate', 0):.2%}")
            
        except Exception as e:
            error_msg = f"V4.0预测器测试失败: {e}"
            results["errors"].append(error_msg)
            print(f"[ERROR] {error_msg}")
        
        return results
    
    def test_main_system_integration(self, data: pd.DataFrame) -> Dict[str, Any]:
        """测试与主系统的集成"""
        print("\n=== 主系统集成测试 ===")
        
        results = {
            "success": False,
            "main_system_available": RefactoredLotterySystem is not None,
            "integration_results": {},
            "errors": []
        }
        
        if not RefactoredLotterySystem:
            results["errors"].append("主系统不可用，跳过集成测试")
            print("[WARN] 主系统不可用，跳过集成测试")
            return results
        
        try:
            # 创建主系统实例
            main_system = RefactoredLotterySystem()
            print("[OK] 主系统创建成功")
            
            # 测试数据加载
            if hasattr(main_system, 'load_data'):
                data_loaded = main_system.load_data()
                print(f"[OK] 主系统数据加载: {'成功' if data_loaded else '失败'}")
                results["integration_results"]["data_loading"] = data_loaded
            
            # 测试V4.0预测器注册（如果支持）
            if hasattr(main_system, 'register_predictor') or hasattr(main_system, 'predictors'):
                try:
                    v4_predictor = V4TransformerPredictor(V4TransformerConfig())
                    
                    # 尝试注册V4.0预测器
                    if hasattr(main_system, 'register_predictor'):
                        main_system.register_predictor('v4_transformer', v4_predictor)
                        print("[OK] V4.0预测器注册成功")
                        results["integration_results"]["predictor_registration"] = True
                    elif hasattr(main_system, 'predictors'):
                        main_system.predictors['v4_transformer'] = v4_predictor
                        print("[OK] V4.0预测器添加到主系统")
                        results["integration_results"]["predictor_registration"] = True
                    
                except Exception as e:
                    error_msg = f"V4.0预测器注册失败: {e}"
                    results["errors"].append(error_msg)
                    print(f"[ERROR] {error_msg}")
                    results["integration_results"]["predictor_registration"] = False
            
            results["success"] = True
            print("[OK] 主系统集成测试完成")
            
        except Exception as e:
            error_msg = f"主系统集成测试失败: {e}"
            results["errors"].append(error_msg)
            print(f"[ERROR] {error_msg}")
        
        return results
    
    def run_comprehensive_test(self) -> Dict[str, Any]:
        """运行综合集成测试"""
        print("V4.0 Transformer 主系统集成测试")
        print("=" * 60)
        
        comprehensive_results = {
            "test_start_time": time.time(),
            "data_loading": {},
            "compatibility": {},
            "predictor_test": {},
            "integration_test": {},
            "overall_success": False
        }
        
        try:
            # 1. 加载真实数据
            data = self.load_real_data()
            comprehensive_results["data_loading"] = {
                "success": True,
                "data_size": len(data),
                "data_range": f"{data['期号'].min()} - {data['期号'].max()}"
            }
            
            # 2. 数据兼容性测试
            compatibility_success = self.test_v4_data_compatibility(data)
            comprehensive_results["compatibility"] = {
                "success": compatibility_success
            }
            
            # 3. V4.0预测器测试
            if compatibility_success:
                predictor_results = self.test_v4_predictor_with_real_data(data)
                comprehensive_results["predictor_test"] = predictor_results
            
            # 4. 主系统集成测试
            integration_results = self.test_main_system_integration(data)
            comprehensive_results["integration_test"] = integration_results
            
            # 5. 计算总体成功率
            test_successes = [
                comprehensive_results["data_loading"]["success"],
                comprehensive_results["compatibility"]["success"],
                comprehensive_results["predictor_test"].get("success", False),
                comprehensive_results["integration_test"].get("success", False)
            ]
            
            success_count = sum(test_successes)
            total_tests = len(test_successes)
            success_rate = success_count / total_tests
            
            comprehensive_results["overall_success"] = success_rate >= 0.75  # 75%以上算成功
            comprehensive_results["success_rate"] = success_rate
            comprehensive_results["test_end_time"] = time.time()
            comprehensive_results["total_duration"] = comprehensive_results["test_end_time"] - comprehensive_results["test_start_time"]
            
            # 输出测试摘要
            print("\n" + "=" * 60)
            print("测试结果摘要:")
            print(f"  数据加载: {'OK' if comprehensive_results['data_loading']['success'] else 'FAIL'}")
            print(f"  数据兼容性: {'OK' if comprehensive_results['compatibility']['success'] else 'FAIL'}")
            print(f"  V4.0预测器: {'OK' if comprehensive_results['predictor_test'].get('success', False) else 'FAIL'}")
            print(f"  主系统集成: {'OK' if comprehensive_results['integration_test'].get('success', False) else 'FAIL'}")
            print(f"总体成功率: {success_rate:.1%}")
            print(f"测试耗时: {comprehensive_results['total_duration']:.2f}秒")
            
            if comprehensive_results["overall_success"]:
                print("\n[SUCCESS] V4.0 Transformer集成测试通过！")
            else:
                print("\n[WARN] V4.0 Transformer集成测试部分失败，需要进一步调试")
            
        except Exception as e:
            comprehensive_results["fatal_error"] = str(e)
            print(f"\n[ERROR] 综合测试遇到致命错误: {e}")
            import traceback
            traceback.print_exc()
        
        return comprehensive_results


def main():
    """主测试函数"""
    tester = V4IntegrationTester()
    results = tester.run_comprehensive_test()
    
    # 保存测试结果
    results_path = Path(project_root) / "logs" / "v4_integration_test_results.json"
    results_path.parent.mkdir(exist_ok=True)
    
    # 转换时间戳为可序列化格式
    serializable_results = json.loads(json.dumps(results, default=str))
    
    with open(results_path, 'w', encoding='utf-8') as f:
        json.dump(serializable_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n测试结果已保存到: {results_path}")
    
    return results["overall_success"]


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)