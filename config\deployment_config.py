#!/usr/bin/env python3
"""
部署配置管理
Deployment Configuration Management
"""

import os
from dataclasses import dataclass, field
from typing import Dict, Any, Optional
from pathlib import Path


@dataclass
class DeploymentConfig:
    """部署配置类"""
    
    # 环境配置
    environment: str = "production"  # development, testing, production
    debug_mode: bool = False
    log_level: str = "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
    
    # 性能配置
    max_workers: int = 4
    timeout_seconds: int = 30
    memory_limit_mb: int = 1024
    
    # 数据配置
    data_path: str = "data/raw/dlt_data.csv"
    backup_path: str = "data/backup/"
    cache_enabled: bool = True
    cache_ttl_seconds: int = 3600
    
    # 预测配置
    default_red_kills: int = 13
    default_blue_kills: int = 5
    min_training_periods: int = 50
    max_training_periods: int = 1500
    
    # 系统配置
    enhanced_system_enabled: bool = True
    fallback_system_enabled: bool = True
    performance_monitoring: bool = True
    
    # 输出配置
    output_format: str = "json"  # json, csv, txt
    include_debug_info: bool = False
    unicode_safe: bool = True  # 避免编码问题
    
    @classmethod
    def from_env(cls) -> 'DeploymentConfig':
        """从环境变量创建配置"""
        return cls(
            environment=os.getenv('LOTTERY_ENV', 'production'),
            debug_mode=os.getenv('LOTTERY_DEBUG', 'false').lower() == 'true',
            log_level=os.getenv('LOTTERY_LOG_LEVEL', 'INFO'),
            max_workers=int(os.getenv('LOTTERY_MAX_WORKERS', '4')),
            timeout_seconds=int(os.getenv('LOTTERY_TIMEOUT', '30')),
            data_path=os.getenv('LOTTERY_DATA_PATH', 'data/raw/dlt_data.csv'),
            enhanced_system_enabled=os.getenv('LOTTERY_ENHANCED', 'true').lower() == 'true',
            unicode_safe=os.getenv('LOTTERY_UNICODE_SAFE', 'true').lower() == 'true'
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'environment': self.environment,
            'debug_mode': self.debug_mode,
            'log_level': self.log_level,
            'max_workers': self.max_workers,
            'timeout_seconds': self.timeout_seconds,
            'memory_limit_mb': self.memory_limit_mb,
            'data_path': self.data_path,
            'backup_path': self.backup_path,
            'cache_enabled': self.cache_enabled,
            'cache_ttl_seconds': self.cache_ttl_seconds,
            'default_red_kills': self.default_red_kills,
            'default_blue_kills': self.default_blue_kills,
            'min_training_periods': self.min_training_periods,
            'max_training_periods': self.max_training_periods,
            'enhanced_system_enabled': self.enhanced_system_enabled,
            'fallback_system_enabled': self.fallback_system_enabled,
            'performance_monitoring': self.performance_monitoring,
            'output_format': self.output_format,
            'include_debug_info': self.include_debug_info,
            'unicode_safe': self.unicode_safe
        }
    
    def validate(self) -> bool:
        """验证配置有效性"""
        try:
            # 检查数据文件是否存在
            if not Path(self.data_path).exists():
                print(f"警告: 数据文件不存在 {self.data_path}")
                return False
            
            # 检查配置范围
            if self.max_workers < 1 or self.max_workers > 16:
                print(f"警告: max_workers 超出合理范围 {self.max_workers}")
                return False
            
            if self.timeout_seconds < 5 or self.timeout_seconds > 300:
                print(f"警告: timeout_seconds 超出合理范围 {self.timeout_seconds}")
                return False
            
            if self.min_training_periods < 10 or self.min_training_periods > 1000:
                print(f"警告: min_training_periods 超出合理范围 {self.min_training_periods}")
                return False
            
            return True
            
        except Exception as e:
            print(f"配置验证失败: {e}")
            return False


# 全局配置实例
_deployment_config = None
_config_lock = Lock()


def get_deployment_config() -> DeploymentConfig:
    """获取部署配置单例"""
    global _deployment_config
    if _deployment_config is None:
        with _config_lock:
            if _deployment_config is None:
                _deployment_config = DeploymentConfig.from_env()
    return _deployment_config


def set_deployment_config(config: DeploymentConfig) -> None:
    """设置部署配置"""
    global _deployment_config
    with _config_lock:
        _deployment_config = config


if __name__ == "__main__":
    # 测试配置
    config = get_deployment_config()
    print("部署配置:")
    for key, value in config.to_dict().items():
        print(f"  {key}: {value}")
    
    print(f"\n配置验证: {'通过' if config.validate() else '失败'}")