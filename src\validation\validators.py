"""
数据验证器实现
基于Pydantic的强类型数据验证系统
"""

from typing import Dict, List, Any, Optional
import pandas as pd
from pydantic import BaseModel, Field, validator, ValidationError
from datetime import datetime
import logging

from ..core.interfaces import IDataValidator, ValidationResult


class LotteryDataModel(BaseModel):
    """大乐透数据模型"""
    period_number: str = Field(..., pattern=r'^\d{5}$', description="期号，5位数字")
    red_ball_1: int = Field(..., ge=1, le=35, description="红球1")
    red_ball_2: int = Field(..., ge=1, le=35, description="红球2")
    red_ball_3: int = Field(..., ge=1, le=35, description="红球3")
    red_ball_4: int = Field(..., ge=1, le=35, description="红球4")
    red_ball_5: int = Field(..., ge=1, le=35, description="红球5")
    blue_ball_1: int = Field(..., ge=1, le=12, description="蓝球1")
    blue_ball_2: int = Field(..., ge=1, le=12, description="蓝球2")
    draw_date: str = Field(..., description="开奖日期")
    
    @validator('red_ball_1', 'red_ball_2', 'red_ball_3', 'red_ball_4', 'red_ball_5')
    def validate_red_balls(cls, v, values):
        """验证红球号码不重复且在有效范围内"""
        red_balls = []
        for field_name in ['red_ball_1', 'red_ball_2', 'red_ball_3', 'red_ball_4', 'red_ball_5']:
            if field_name in values:
                red_balls.append(values[field_name])
        red_balls.append(v)
        
        if len(set(red_balls)) != len(red_balls):
            raise ValueError('红球号码不能重复')
        
        return v
    
    @validator('blue_ball_1', 'blue_ball_2')
    def validate_blue_balls(cls, v, values):
        """验证蓝球号码不重复且在有效范围内"""
        blue_balls = []
        for field_name in ['blue_ball_1', 'blue_ball_2']:
            if field_name in values:
                blue_balls.append(values[field_name])
        blue_balls.append(v)
        
        if len(set(blue_balls)) != len(blue_balls):
            raise ValueError('蓝球号码不能重复')
        
        return v
    
    def get_red_balls(self) -> List[int]:
        """获取红球列表"""
        return sorted([self.red_ball_1, self.red_ball_2, self.red_ball_3, self.red_ball_4, self.red_ball_5])
    
    def get_blue_balls(self) -> List[int]:
        """获取蓝球列表"""
        return sorted([self.blue_ball_1, self.blue_ball_2])


class PredictionInputModel(BaseModel):
    """预测输入模型"""
    historical_data: List[LotteryDataModel] = Field(..., min_items=10, description="历史数据")
    target_period: Optional[str] = Field(None, description="目标期号")
    prediction_config: Dict[str, Any] = Field(default_factory=dict, description="预测配置")
    
    @validator('historical_data')
    def validate_historical_data_sequence(cls, v):
        """验证历史数据的连续性"""
        if len(v) < 10:
            raise ValueError('历史数据至少需要10期')
        
        # 验证期号顺序（从新到旧）
        periods = [int(data.period_number) for data in v]
        for i in range(1, len(periods)):
            if periods[i] >= periods[i-1]:
                raise ValueError(f'期号顺序错误: {periods[i-1]} -> {periods[i]}，应该从新到旧排列')
        
        return v


class PredictionOutputModel(BaseModel):
    """预测输出模型"""
    period_number: str = Field(..., description="预测期号")
    red_balls: List[int] = Field(..., min_items=5, max_items=5, description="预测红球")
    blue_balls: List[int] = Field(..., min_items=2, max_items=2, description="预测蓝球")
    confidence: float = Field(..., ge=0.0, le=1.0, description="置信度")
    algorithm_used: str = Field(..., description="使用的算法")
    processing_time: float = Field(..., ge=0.0, description="处理时间（秒）")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    
    @validator('red_balls')
    def validate_red_balls(cls, v):
        """验证预测红球"""
        if len(set(v)) != 5:
            raise ValueError('预测红球不能重复')
        
        for ball in v:
            if not (1 <= ball <= 35):
                raise ValueError(f'预测红球必须在1-35之间，当前值: {ball}')
        
        return sorted(v)
    
    @validator('blue_balls')
    def validate_blue_balls(cls, v):
        """验证预测蓝球"""
        if len(set(v)) != 2:
            raise ValueError('预测蓝球不能重复')
        
        for ball in v:
            if not (1 <= ball <= 12):
                raise ValueError(f'预测蓝球必须在1-12之间，当前值: {ball}')
        
        return sorted(v)


class LotteryDataValidator(IDataValidator):
    """大乐透数据验证器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def validate_lottery_data(self, data: pd.DataFrame) -> ValidationResult:
        """验证彩票数据"""
        errors = []
        warnings = []
        
        try:
            # 检查必要的列
            required_columns = ['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '蓝球1', '蓝球2', '日期']
            missing_columns = [col for col in required_columns if col not in data.columns]
            if missing_columns:
                errors.append(f"缺少必要的列: {missing_columns}")
                return ValidationResult(is_valid=False, errors=errors, warnings=warnings)
            
            # 逐行验证数据
            for index, row in data.iterrows():
                try:
                    # 构造验证数据
                    validation_data = {
                        'period_number': str(row['期号']),
                        'red_ball_1': int(row['红球1']),
                        'red_ball_2': int(row['红球2']),
                        'red_ball_3': int(row['红球3']),
                        'red_ball_4': int(row['红球4']),
                        'red_ball_5': int(row['红球5']),
                        'blue_ball_1': int(row['蓝球1']),
                        'blue_ball_2': int(row['蓝球2']),
                        'draw_date': str(row['日期'])
                    }
                    
                    # 使用Pydantic模型验证
                    LotteryDataModel(**validation_data)
                    
                except ValidationError as e:
                    errors.append(f"第{index}行数据验证失败: {e}")
                except Exception as e:
                    errors.append(f"第{index}行数据处理失败: {e}")
            
            # 检查数据完整性
            if len(data) < 10:
                warnings.append("数据量较少，可能影响预测准确性")
            
            # 检查期号连续性
            periods = data['期号'].astype(str).tolist()
            period_gaps = []
            for i in range(1, len(periods)):
                current_period = int(periods[i-1])
                next_period = int(periods[i])
                if current_period - next_period != 1:
                    period_gaps.append(f"{current_period} -> {next_period}")
            
            if period_gaps:
                warnings.append(f"期号存在间隔: {period_gaps[:5]}")  # 只显示前5个间隔
            
        except Exception as e:
            errors.append(f"数据验证过程中发生错误: {e}")
            self.logger.error(f"数据验证错误: {e}")
        
        is_valid = len(errors) == 0
        return ValidationResult(is_valid=is_valid, errors=errors, warnings=warnings)
    
    def validate_prediction_input(self, data: Dict[str, Any]) -> ValidationResult:
        """验证预测输入"""
        errors = []
        warnings = []
        
        try:
            # 使用Pydantic模型验证
            PredictionInputModel(**data)
            
        except ValidationError as e:
            for error in e.errors():
                field = error.get('loc', ['unknown'])[0]
                message = error.get('msg', 'Unknown error')
                errors.append(f"字段 {field}: {message}")
        
        except Exception as e:
            errors.append(f"预测输入验证失败: {e}")
            self.logger.error(f"预测输入验证错误: {e}")
        
        is_valid = len(errors) == 0
        return ValidationResult(is_valid=is_valid, errors=errors, warnings=warnings)
    
    def validate_prediction_output(self, data: Dict[str, Any]) -> ValidationResult:
        """验证预测输出"""
        errors = []
        warnings = []
        
        try:
            # 使用Pydantic模型验证
            PredictionOutputModel(**data)
            
        except ValidationError as e:
            for error in e.errors():
                field = error.get('loc', ['unknown'])[0]
                message = error.get('msg', 'Unknown error')
                errors.append(f"字段 {field}: {message}")
        
        except Exception as e:
            errors.append(f"预测输出验证失败: {e}")
            self.logger.error(f"预测输出验证错误: {e}")
        
        is_valid = len(errors) == 0
        return ValidationResult(is_valid=is_valid, errors=errors, warnings=warnings)
    
    def validate_dataframe_structure(self, data: pd.DataFrame) -> ValidationResult:
        """验证DataFrame结构"""
        errors = []
        warnings = []
        
        # 检查是否为空
        if data.empty:
            errors.append("数据为空")
            return ValidationResult(is_valid=False, errors=errors, warnings=warnings)
        
        # 检查数据类型
        expected_types = {
            '期号': ['object', 'int64'],
            '红球1': ['int64'],
            '红球2': ['int64'],
            '红球3': ['int64'],
            '红球4': ['int64'],
            '红球5': ['int64'],
            '蓝球1': ['int64'],
            '蓝球2': ['int64'],
            '日期': ['object']
        }
        
        for column, expected_type_list in expected_types.items():
            if column in data.columns:
                actual_type = str(data[column].dtype)
                if actual_type not in expected_type_list:
                    warnings.append(f"列 {column} 的数据类型为 {actual_type}，期望类型: {expected_type_list}")
        
        # 检查缺失值
        missing_data = data.isnull().sum()
        for column, missing_count in missing_data.items():
            if missing_count > 0:
                errors.append(f"列 {column} 有 {missing_count} 个缺失值")
        
        is_valid = len(errors) == 0
        return ValidationResult(is_valid=is_valid, errors=errors, warnings=warnings)