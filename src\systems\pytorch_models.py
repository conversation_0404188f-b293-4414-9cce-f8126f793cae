#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyTorch深度学习模型
包含LSTM、Transformer和多任务网络模型
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from typing import Tuple, Dict, Any


class LSTMModel(nn.Module):
    """LSTM模型用于彩票预测"""
    
    def __init__(self, input_size: int, hidden_size: int, num_layers: int, 
                 dropout: float = 0.2, red_output_size: int = 35, blue_output_size: int = 12):
        super(LSTMModel, self).__init__()
        
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        # LSTM层
        self.lstm = nn.LSTM(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            dropout=dropout if num_layers > 1 else 0,
            batch_first=True
        )
        
        # Dropout层
        self.dropout = nn.Dropout(dropout)
        
        # 输出层
        self.red_output = nn.Linear(hidden_size, red_output_size)
        self.blue_output = nn.Linear(hidden_size, blue_output_size)
        
    def forward(self, x):
        # LSTM前向传播
        lstm_out, (hidden, cell) = self.lstm(x)
        
        # 取最后一个时间步的输出
        last_output = lstm_out[:, -1, :]
        
        # Dropout
        last_output = self.dropout(last_output)
        
        # 分别预测红球和蓝球（输出logits，不使用激活函数）
        red_pred = self.red_output(last_output)
        blue_pred = self.blue_output(last_output)
        
        return {'red': red_pred, 'blue': blue_pred}


class TransformerModel(nn.Module):
    """Transformer模型用于彩票预测"""
    
    def __init__(self, input_size: int, embed_dim: int, num_heads: int, 
                 ff_dim: int, num_layers: int, dropout: float = 0.1,
                 red_output_size: int = 35, blue_output_size: int = 12):
        super(TransformerModel, self).__init__()
        
        self.embed_dim = embed_dim
        
        # 输入嵌入层
        self.input_projection = nn.Linear(input_size, embed_dim)
        
        # 位置编码
        self.pos_encoding = PositionalEncoding(embed_dim, dropout)
        
        # Transformer编码器层
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=embed_dim,
            nhead=num_heads,
            dim_feedforward=ff_dim,
            dropout=dropout,
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers)
        
        # 输出层
        self.red_output = nn.Linear(embed_dim, red_output_size)
        self.blue_output = nn.Linear(embed_dim, blue_output_size)
        
    def forward(self, x):
        # 输入投影
        x = self.input_projection(x)
        
        # 位置编码
        x = self.pos_encoding(x)
        
        # Transformer编码
        transformer_out = self.transformer(x)
        
        # 全局平均池化
        pooled = torch.mean(transformer_out, dim=1)
        
        # 分别预测红球和蓝球（输出logits，不使用激活函数）
        red_pred = self.red_output(pooled)
        blue_pred = self.blue_output(pooled)
        
        return {'red': red_pred, 'blue': blue_pred}


class PositionalEncoding(nn.Module):
    """位置编码"""
    
    def __init__(self, d_model: int, dropout: float = 0.1, max_len: int = 5000):
        super(PositionalEncoding, self).__init__()
        self.dropout = nn.Dropout(p=dropout)
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        self.register_buffer('pe', pe)
        
    def forward(self, x):
        x = x + self.pe[:x.size(1), :].transpose(0, 1)
        return self.dropout(x)


class MultiTaskModel(nn.Module):
    """多任务网络模型"""
    
    def __init__(self, input_size: int, hidden_units: list, dropout: float = 0.2,
                 red_output_size: int = 35, blue_output_size: int = 12):
        super(MultiTaskModel, self).__init__()
        
        # 序列处理层（将3D输入转换为2D）
        self.sequence_processor = nn.LSTM(input_size, hidden_units[0], batch_first=True)
        
        # 共享层
        layers = []
        prev_size = hidden_units[0]
        
        for hidden_size in hidden_units[1:]:
            layers.extend([
                nn.Linear(prev_size, hidden_size),
                nn.ReLU(),
                nn.Dropout(dropout)
            ])
            prev_size = hidden_size
            
        self.shared_layers = nn.Sequential(*layers)
        
        # 任务特定层
        self.red_head = nn.Sequential(
            nn.Linear(prev_size, prev_size // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(prev_size // 2, red_output_size)
        )
        
        self.blue_head = nn.Sequential(
            nn.Linear(prev_size, prev_size // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(prev_size // 2, blue_output_size)
        )
        
    def forward(self, x):
        # 处理序列数据
        lstm_out, (hidden, cell) = self.sequence_processor(x)
        
        # 取最后一个时间步的输出
        last_output = lstm_out[:, -1, :]
        
        # 共享特征提取
        shared_features = self.shared_layers(last_output)
        
        # 任务特定预测（输出logits，不使用激活函数）
        red_pred = self.red_head(shared_features)
        blue_pred = self.blue_head(shared_features)
        
        return {'red': red_pred, 'blue': blue_pred}


class ModelTrainer:
    """模型训练器"""
    
    def __init__(self, model, device, use_mixed_precision=False):
        self.model = model.to(device)
        self.device = device
        self.use_mixed_precision = use_mixed_precision
        
        if use_mixed_precision:
            self.scaler = torch.cuda.amp.GradScaler()
        else:
            self.scaler = None
            
    def train_epoch(self, dataloader, optimizer, criterion):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        
        for batch_idx, (data, targets) in enumerate(dataloader):
            data = data.to(self.device)
            red_targets = targets['red'].to(self.device)
            blue_targets = targets['blue'].to(self.device)
            
            optimizer.zero_grad()
            
            if self.use_mixed_precision and self.scaler:
                with torch.cuda.amp.autocast():
                    outputs = self.model(data)
                    red_loss = criterion(outputs['red'], red_targets)
                    blue_loss = criterion(outputs['blue'], blue_targets)
                    loss = red_loss + blue_loss
                
                self.scaler.scale(loss).backward()
                self.scaler.step(optimizer)
                self.scaler.update()
            else:
                outputs = self.model(data)
                red_loss = criterion(outputs['red'], red_targets)
                blue_loss = criterion(outputs['blue'], blue_targets)
                loss = red_loss + blue_loss
                
                loss.backward()
                optimizer.step()
            
            total_loss += loss.item()
            
        return total_loss / len(dataloader)
    
    def evaluate(self, dataloader, criterion):
        """评估模型"""
        self.model.eval()
        total_loss = 0.0
        
        with torch.no_grad():
            for data, targets in dataloader:
                data = data.to(self.device)
                red_targets = targets['red'].to(self.device)
                blue_targets = targets['blue'].to(self.device)
                
                outputs = self.model(data)
                red_loss = criterion(outputs['red'], red_targets)
                blue_loss = criterion(outputs['blue'], blue_targets)
                loss = red_loss + blue_loss
                
                total_loss += loss.item()
                
        return total_loss / len(dataloader)
    
    def predict(self, data):
        """预测"""
        self.model.eval()
        with torch.no_grad():
            data = data.to(self.device)
            outputs = self.model(data)
            return outputs


def create_model(model_type: str, config: Dict[str, Any], input_size: int) -> nn.Module:
    """创建模型工厂函数"""
    if model_type == 'lstm':
        return LSTMModel(
            input_size=input_size,
            hidden_size=config.get('units', 128),
            num_layers=config.get('layers', 2),
            dropout=config.get('dropout', 0.2)
        )
    elif model_type == 'transformer':
        return TransformerModel(
            input_size=input_size,
            embed_dim=config.get('embed_dim', 64),
            num_heads=config.get('num_heads', 8),
            ff_dim=config.get('ff_dim', 256),
            num_layers=config.get('num_layers', 4),
            dropout=config.get('dropout', 0.1)
        )
    elif model_type == 'multi_task':
        return MultiTaskModel(
            input_size=input_size,
            hidden_units=config.get('hidden_units', [256, 128, 64]),
            dropout=config.get('dropout', 0.2)
        )
    else:
        raise ValueError(f"未知的模型类型: {model_type}")
