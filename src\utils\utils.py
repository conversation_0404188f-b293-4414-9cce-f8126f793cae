"""
工具函数模块
提供数据读取、格式转换、比值计算等基础功能
"""

import pandas as pd
import numpy as np
from typing import List, Tuple, Dict, Any


def load_data(file_path: str = 'data/raw/dlt_data.csv') -> pd.DataFrame:
    """
    加载大乐透历史数据
    
    Args:
        file_path: CSV文件路径
        
    Returns:
        DataFrame: 包含历史开奖数据的DataFrame
    """
    df = pd.read_csv(file_path)
    # 确保数据按期号从旧到新排序
    df = df.sort_values('期号', ascending=True).reset_index(drop=True)
    return df


def parse_numbers(row: pd.Series) -> Tuple[List[int], List[int]]:
    """
    解析一行数据中的红球和蓝球号码
    
    Args:
        row: DataFrame的一行数据
        
    Returns:
        Tuple[List[int], List[int]]: (红球号码列表, 蓝球号码列表)
    """
    red_balls = [int(row[f'红球{i}']) for i in range(1, 6)]
    blue_balls = [int(row[f'蓝球{i}']) for i in range(1, 3)]
    return red_balls, blue_balls


def calculate_odd_even_ratio(numbers: List[int]) -> Tuple[int, int]:
    """
    计算奇偶比
    
    Args:
        numbers: 号码列表
        
    Returns:
        Tuple[int, int]: (奇数个数, 偶数个数)
    """
    odd_count = sum(1 for num in numbers if num % 2 == 1)
    even_count = len(numbers) - odd_count
    return odd_count, even_count


def calculate_size_ratio_red(numbers: List[int]) -> Tuple[int, int]:
    """
    计算红球大小比（小号01-18，大号19-35）- 使用统一标准
    返回格式：(大号个数, 小号个数) - 符合大数:小数显示格式

    Args:
        numbers: 红球号码列表

    Returns:
        Tuple[int, int]: (大号个数, 小号个数)
    """
    small_count = sum(1 for num in numbers if 1 <= num <= 18)
    big_count = len(numbers) - small_count
    return big_count, small_count


def calculate_size_ratio_blue(numbers: List[int]) -> Tuple[int, int]:
    """
    计算蓝球大小比（小号01-06，大号07-12）- 使用统一标准
    返回格式：(大号个数, 小号个数) - 符合大数:小数显示格式

    Args:
        numbers: 蓝球号码列表（大乐透有2个蓝球）

    Returns:
        Tuple[int, int]: (大号个数, 小号个数)
    """
    if not numbers:
        return 0, 0

    small_count = sum(1 for num in numbers if 1 <= num <= 7)
    big_count = sum(1 for num in numbers if 7 <= num <= 12)

    return big_count, small_count


def ratio_to_state(ratio: Tuple[int, int]) -> str:
    """
    将比值转换为状态字符串
    
    Args:
        ratio: (数量1, 数量2)
        
    Returns:
        str: 状态字符串，如"3:2"
    """
    return f"{ratio[0]}:{ratio[1]}"


def state_to_ratio(state: str) -> Tuple[int, int]:
    """
    将状态字符串转换为比值
    
    Args:
        state: 状态字符串，如"3:2"
        
    Returns:
        Tuple[int, int]: (数量1, 数量2)
    """
    parts = state.split(':')
    return int(parts[0]), int(parts[1])


def get_all_red_states() -> List[str]:
    """
    获取红球所有可能的状态（5个球的比值状态）
    
    Returns:
        List[str]: 所有可能的状态列表
    """
    return ["0:5", "1:4", "2:3", "3:2", "4:1", "5:0"]


def get_all_blue_states() -> List[str]:
    """
    获取蓝球所有可能的状态（2个球的大小比状态）
    大乐透有2个蓝球，小号1-6，大号7-12

    Returns:
        List[str]: 所有可能的状态列表
    """
    return ["2:0", "1:1", "0:2"]


def format_numbers(numbers: List[int]) -> str:
    """
    格式化号码为两位数字符串
    
    Args:
        numbers: 号码列表
        
    Returns:
        str: 格式化后的号码字符串，如"01,03,07"
    """
    return ','.join([f"{num:02d}" for num in sorted(numbers)])


def check_hit_2_plus_1(predicted: Tuple[List[int], List[int]],
                      actual: Tuple[List[int], List[int]]) -> bool:
    """
    检查是否命中2+1（至少2个红球+1个蓝球）

    Args:
        predicted: (预测红球, 预测蓝球)
        actual: (实际红球, 实际蓝球)

    Returns:
        bool: 是否命中2+1
    """
    pred_red, pred_blue = predicted
    actual_red, actual_blue = actual

    red_hits = len(set(pred_red) & set(actual_red))
    blue_hits = len(set(pred_blue) & set(actual_blue))

    return red_hits >= 2 and blue_hits >= 1


def calculate_success_rate(predictions: List[bool]) -> float:
    """
    计算成功率
    
    Args:
        predictions: 预测结果列表（True表示成功）
        
    Returns:
        float: 成功率（0-1之间）
    """
    if not predictions:
        return 0.0
    return sum(predictions) / len(predictions)


def validate_data_format(df: pd.DataFrame) -> bool:
    """
    验证数据格式是否正确

    Args:
        df: 数据DataFrame

    Returns:
        bool: 格式是否正确
    """
    required_columns = ['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '蓝球1', '蓝球2']
    return all(col in df.columns for col in required_columns)


def get_data_summary(df: pd.DataFrame) -> Dict[str, Any]:
    """
    获取数据摘要信息

    Args:
        df: 数据DataFrame

    Returns:
        Dict: 数据摘要
    """
    if df.empty:
        return {"total_periods": 0, "date_range": None}

    summary = {
        "total_periods": len(df),
        "period_range": (df['期号'].min(), df['期号'].max()),
        "data_quality": validate_data_format(df)
    }

    return summary


def safe_load_data(file_path: str = 'data/raw/dlt_data.csv') -> pd.DataFrame:
    """
    安全加载数据，包含错误处理

    Args:
        file_path: CSV文件路径

    Returns:
        DataFrame: 包含历史开奖数据的DataFrame

    Raises:
        FileNotFoundError: 文件不存在
        ValueError: 数据格式错误
    """
    try:
        df = load_data(file_path)

        if not validate_data_format(df):
            raise ValueError("数据格式不正确，缺少必要的列")

        return df

    except FileNotFoundError:
        raise FileNotFoundError(f"数据文件不存在: {file_path}")
    except Exception as e:
        raise ValueError(f"数据加载失败: {str(e)}")


def calculate_hit_rate_detailed(predictions: List[Tuple], actuals: List[Tuple]) -> Dict[str, Any]:
    """
    计算详细的命中率统计

    Args:
        predictions: 预测结果列表 [(红球, 蓝球), ...]
        actuals: 实际结果列表 [(红球, 蓝球), ...]

    Returns:
        Dict: 详细命中率统计
    """
    if len(predictions) != len(actuals):
        raise ValueError("预测结果和实际结果数量不匹配")

    stats = {
        "total_periods": len(predictions),
        "red_hits": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0},
        "blue_hits": {"0": 0, "1": 0, "2": 0},
        "2_plus_1_hits": 0,
        "hit_rate_2_plus_1": 0.0
    }

    for pred, actual in zip(predictions, actuals):
        pred_red, pred_blue = pred
        actual_red, actual_blue = actual

        # 计算红球命中数
        red_hit_count = len(set(pred_red) & set(actual_red))
        stats["red_hits"][str(red_hit_count)] += 1

        # 计算蓝球命中数
        blue_hit_count = len(set(pred_blue) & set(actual_blue))
        stats["blue_hits"][str(blue_hit_count)] += 1

        # 计算2+1命中
        if red_hit_count >= 2 and blue_hit_count >= 1:
            stats["2_plus_1_hits"] += 1

    # 计算2+1命中率
    stats["hit_rate_2_plus_1"] = stats["2_plus_1_hits"] / stats["total_periods"] if stats["total_periods"] > 0 else 0.0

    return stats
