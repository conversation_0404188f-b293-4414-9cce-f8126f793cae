# 建议的开发命令

## Windows系统命令
由于项目运行在Windows系统上，使用以下命令：

### 基本文件操作
- `dir` - 列出目录内容
- `cd <directory>` - 切换目录
- `type <file>` - 查看文件内容
- `copy <source> <dest>` - 复制文件
- `del <file>` - 删除文件
- `mkdir <directory>` - 创建目录

### Python相关命令
- `python --version` - 检查Python版本
- `pip list` - 查看已安装包
- `pip install -r requirements.txt` - 安装依赖
- `pip install -e .` - 开发模式安装

### 运行项目
- `python main_entry.py` - 运行主程序
- `python -m src.apps.main` - 直接运行主模块
- `python -m pytest tests/` - 运行测试

### 代码质量检查
- `python -m black src/` - 代码格式化
- `python -m flake8 src/` - 代码检查
- `python -m mypy src/` - 类型检查
- `python -m pytest tests/ --cov=src` - 测试覆盖率

### Git操作
- `git status` - 查看状态
- `git add .` - 添加所有更改
- `git commit -m "message"` - 提交更改
- `git push` - 推送到远程

### 项目特定命令
- `python scripts/config_consistency_checker.py` - 配置一致性检查
- `python scripts/organize_config_files.py` - 整理配置文件
- `python src/tools/optimize_predictor_parameters_unified.py` - 参数优化