"""统一奇偶预测器模块

这个模块提供了一个统一的、可配置的奇偶预测器，
整合了项目中所有奇偶预测器的功能，避免代码重复。
"""

import pandas as pd
from typing import List, Dict, Any, Optional, Tuple
from enum import Enum

from ..core.base import StandardBasePredictor
from ..core.interfaces import PredictionType, BallType, PredictionResult as StandardPredictionResult, ValidationResult
from ..core.project_config import project_config


class OddEvenAlgorithm(Enum):
    """奇偶预测算法类型"""
    ENHANCED = "enhanced"      # 增强算法（周期+马尔科夫+反连续+频率）
    OPTIMIZED = "optimized"    # 优化算法（马尔科夫+频率+趋势）
    ADVANCED = "advanced"      # 高级算法（反连续+周期+状态热度）
    BASIC = "basic"            # 基础算法（仅频率）


class UnifiedOddEvenPredictor(StandardBasePredictor):
    """统一奇偶预测器
    
    整合了所有奇偶预测器的功能，通过配置参数选择不同的算法策略。
    支持多种预测算法：增强、优化、高级和基础算法。
    """
    
    def __init__(self, algorithm: OddEvenAlgorithm = OddEvenAlgorithm.ENHANCED, **kwargs):
        """初始化统一奇偶预测器
        
        Args:
            algorithm: 预测算法类型
            **kwargs: 其他配置参数
        """
        super().__init__(
            name=f"统一奇偶预测器-{algorithm.value}",
            prediction_type=PredictionType.ODD_EVEN_RATIO,
            version="2.0"
        )
        
        self.algorithm = algorithm
        self.config = kwargs
        
        # 基础状态权重（基于历史频率分析）
        self.base_state_weights = {
            '3:2': 0.369,  # 历史频率36.9%
            '2:3': 0.315,  # 历史频率31.5%
            '4:1': 0.151,  # 历史频率15.1%
            '1:4': 0.123,  # 历史频率12.3%
            '5:0': 0.025,  # 历史频率2.5%
            '0:5': 0.017   # 历史频率1.7%
        }
        
        # 状态转移概率矩阵
        self.transition_matrix = {
            '3:2': {'3:2': 0.360, '2:3': 0.315, '4:1': 0.159, '1:4': 0.127, '5:0': 0.022, '0:5': 0.018},
            '2:3': {'3:2': 0.373, '2:3': 0.305, '4:1': 0.167, '1:4': 0.127, '5:0': 0.015, '0:5': 0.013},
            '4:1': {'3:2': 0.388, '2:3': 0.326, '1:4': 0.110, '4:1': 0.101, '5:0': 0.044, '0:5': 0.031},
            '1:4': {'3:2': 0.348, '2:3': 0.315, '4:1': 0.163, '1:4': 0.136, '5:0': 0.027, '0:5': 0.011},
            '5:0': {'2:3': 0.378, '3:2': 0.378, '5:0': 0.081, '4:1': 0.081, '1:4': 0.054, '0:5': 0.027},
            '0:5': {'3:2': 0.423, '2:3': 0.346, '4:1': 0.154, '1:4': 0.077, '5:0': 0.000, '0:5': 0.000}
        }
        
        # 初始化算法特定参数
        self._init_algorithm_params()
        
        # 训练数据
        self.recent_states = []
        self.state_heat = {}  # 状态热度
        self.pattern_scores = {}  # 模式得分
        
        self.logger.info(f"初始化统一奇偶预测器，算法: {algorithm.value}")
    
    def _init_algorithm_params(self):
        """初始化算法特定参数"""
        if self.algorithm == OddEvenAlgorithm.ENHANCED:
            # 增强算法：周期+马尔科夫+反连续+频率
            self.weights = {
                "cycle": self.config.get("cycle_weight", 0.4),
                "markov": self.config.get("markov_weight", 0.3),
                "anti": self.config.get("anti_weight", 0.2),
                "frequency": self.config.get("frequency_weight", 0.1)
            }
            self.cycle_lengths = self.config.get("cycle_lengths", [2, 3, 5, 7, 10])
            
        elif self.algorithm == OddEvenAlgorithm.OPTIMIZED:
            # 优化算法：马尔科夫+频率+趋势
            self.weights = {
                "markov": self.config.get("markov_weight", 0.5),
                "frequency": self.config.get("frequency_weight", 0.3),
                "trend": self.config.get("trend_weight", 0.2)
            }
            # 近期趋势偏向
            self.trend_bias = {
                '2:3': 1.2, '3:2': 1.0, '4:1': 0.8,
                '1:4': 0.8, '5:0': 0.6, '0:5': 0.6
            }
            
        elif self.algorithm == OddEvenAlgorithm.ADVANCED:
            # 高级算法：反连续+周期+状态热度
            self.weights = {
                "anti": self.config.get("anti_weight", 0.7),
                "cycle": self.config.get("cycle_weight", 0.2),
                "heat": self.config.get("heat_weight", 0.1)
            }
            self.cycle_patterns = {2: 0.3, 3: 0.4, 5: 0.2, 7: 0.1}
            self.heat_decay = self.config.get("heat_decay", 0.8)
            
        elif self.algorithm == OddEvenAlgorithm.BASIC:
            # 基础算法：仅频率
            self.weights = {"frequency": 1.0}
    
    def train(self, data: pd.DataFrame, **kwargs) -> bool:
        """训练预测器
        
        Args:
            data: 历史数据
            **kwargs: 其他参数
            
        Returns:
            bool: 训练是否成功
        """
        try:
            self.logger.info(f"开始训练统一奇偶预测器，算法: {self.algorithm.value}")
            
            # 提取奇偶状态序列
            state_sequence = self._extract_odd_even_states(data)
            
            if len(state_sequence) < 10:
                self.logger.warning("训练数据不足，至少需要10期数据")
                return False
            
            # 保存训练数据
            max_history = self.config.get("max_history", 50)
            self.recent_states = state_sequence[-max_history:]
            
            # 初始化状态热度
            if self.algorithm == OddEvenAlgorithm.ADVANCED:
                self._init_state_heat()
            
            # 分析模式
            self._analyze_patterns()
            
            self.is_trained = True
            self.logger.info(f"训练完成，使用 {len(self.recent_states)} 期历史数据")
            return True
            
        except Exception as e:
            self.logger.error(f"训练失败: {e}")
            return False
    
    def predict(self, data: pd.DataFrame, target_index: int, **kwargs) -> StandardPredictionResult:
        """执行奇偶比预测
        
        Args:
            data: 历史数据
            target_index: 目标期数索引
            **kwargs: 其他参数
            
        Returns:
            StandardPredictionResult: 预测结果
        """
        try:
            # 验证输入
            validation = self.validate_input(data, target_index)
            if not validation.is_valid:
                raise ValueError(f"输入验证失败: {validation.errors}")
            
            # 获取当前状态
            current_states = self._extract_odd_even_states(data[:target_index])
            if not current_states:
                raise ValueError("无法提取当前奇偶状态")
            
            current_state = current_states[-1]
            
            # 根据算法执行预测
            if self.algorithm == OddEvenAlgorithm.ENHANCED:
                prediction, confidence = self._predict_enhanced(current_states, current_state)
            elif self.algorithm == OddEvenAlgorithm.OPTIMIZED:
                prediction, confidence = self._predict_optimized(current_states, current_state)
            elif self.algorithm == OddEvenAlgorithm.ADVANCED:
                prediction, confidence = self._predict_advanced(current_states, current_state)
            else:  # BASIC
                prediction, confidence = self._predict_basic(current_state)
            
            # 创建预测结果
            result = StandardPredictionResult(
                prediction_type=self.prediction_type,
                ball_type=BallType.RED,
                value=prediction,
                confidence=confidence,
                metadata={
                    "algorithm": self.algorithm.value,
                    "current_state": current_state,
                    "model_version": self.version
                }
            )
            
            self.logger.info(f"预测完成: {prediction} (置信度: {confidence:.3f})")
            return result
            
        except Exception as e:
            self.logger.error(f"预测失败: {e}")
            return StandardPredictionResult(
                prediction_type=self.prediction_type,
                ball_type=BallType.RED,
                value=None,
                confidence=0.0,
                metadata={"error": str(e)}
            )
    
    def _extract_odd_even_states(self, data: pd.DataFrame) -> List[str]:
        """从数据中提取奇偶状态序列"""
        states = []
        
        for _, row in data.iterrows():
            # 提取红球号码
            red_balls = []
            for i in range(1, 7):  # 假设红球列名为 red_1 到 red_6
                col_name = f"red_{i}"
                if col_name in row:
                    red_balls.append(int(row[col_name]))
            
            if len(red_balls) == 6:
                # 计算奇偶比
                odd_count = sum(1 for ball in red_balls if ball % 2 == 1)
                even_count = 6 - odd_count
                state = f"{odd_count}:{even_count}"
                states.append(state)
        
        return states
    
    def _predict_enhanced(self, states: List[str], current_state: str) -> Tuple[str, float]:
        """增强算法预测"""
        scores = {state: 0.0 for state in self.base_state_weights.keys()}
        
        # 周期预测
        cycle_scores = self._calculate_cycle_scores(states)
        
        # 马尔科夫预测
        markov_scores = self.transition_matrix.get(current_state, self.base_state_weights)
        
        # 反连续预测
        anti_scores = self._calculate_anti_consecutive_scores(current_state)
        
        # 频率预测
        frequency_scores = self.base_state_weights
        
        # 加权合并
        for state in scores.keys():
            scores[state] = (
                self.weights["cycle"] * cycle_scores.get(state, 0) +
                self.weights["markov"] * markov_scores.get(state, 0) +
                self.weights["anti"] * anti_scores.get(state, 0) +
                self.weights["frequency"] * frequency_scores.get(state, 0)
            )
        
        # 选择最高分数的状态
        best_state = max(scores.keys(), key=lambda k: scores[k])
        confidence = scores[best_state]
        
        return best_state, confidence
    
    def _predict_optimized(self, states: List[str], current_state: str) -> Tuple[str, float]:
        """优化算法预测"""
        scores = {state: 0.0 for state in self.base_state_weights.keys()}
        
        # 马尔科夫预测
        markov_scores = self.transition_matrix.get(current_state, self.base_state_weights)
        
        # 频率预测
        frequency_scores = self.base_state_weights
        
        # 趋势预测（应用趋势偏向）
        trend_scores = {state: freq * self.trend_bias.get(state, 1.0) 
                       for state, freq in self.base_state_weights.items()}
        
        # 加权合并
        for state in scores.keys():
            scores[state] = (
                self.weights["markov"] * markov_scores.get(state, 0) +
                self.weights["frequency"] * frequency_scores.get(state, 0) +
                self.weights["trend"] * trend_scores.get(state, 0)
            )
        
        best_state = max(scores.keys(), key=lambda k: scores[k])
        confidence = scores[best_state]
        
        return best_state, confidence
    
    def _predict_advanced(self, states: List[str], current_state: str) -> Tuple[str, float]:
        """高级算法预测"""
        scores = {state: 0.0 for state in self.base_state_weights.keys()}
        
        # 反连续预测
        anti_scores = self._calculate_anti_consecutive_scores(current_state)
        
        # 周期预测
        cycle_scores = self._calculate_advanced_cycle_scores(states)
        
        # 状态热度预测
        heat_scores = self._calculate_heat_scores()
        
        # 加权合并
        for state in scores.keys():
            scores[state] = (
                self.weights["anti"] * anti_scores.get(state, 0) +
                self.weights["cycle"] * cycle_scores.get(state, 0) +
                self.weights["heat"] * heat_scores.get(state, 0)
            )
        
        best_state = max(scores.keys(), key=lambda k: scores[k])
        confidence = scores[best_state]
        
        return best_state, confidence
    
    def _predict_basic(self, current_state: str) -> Tuple[str, float]:
        """基础算法预测（仅基于频率）"""
        best_state = max(self.base_state_weights.keys(), 
                        key=lambda k: self.base_state_weights[k])
        confidence = self.base_state_weights[best_state]
        
        return best_state, confidence
    
    def _calculate_cycle_scores(self, states: List[str]) -> Dict[str, float]:
        """计算周期得分"""
        scores = {state: 0.0 for state in self.base_state_weights.keys()}
        
        for cycle_len in self.cycle_lengths:
            if len(states) >= cycle_len:
                # 检查周期模式
                pattern = states[-cycle_len:]
                if len(set(pattern)) < len(pattern):  # 有重复，可能存在周期
                    for state in self.base_state_weights.keys():
                        if state in pattern:
                            scores[state] += 1.0 / cycle_len
        
        # 归一化
        total = sum(scores.values())
        if total > 0:
            scores = {k: v / total for k, v in scores.items()}
        
        return scores
    
    def _calculate_anti_consecutive_scores(self, current_state: str) -> Dict[str, float]:
        """计算反连续得分"""
        scores = {state: 0.0 for state in self.base_state_weights.keys()}
        
        # 避免连续相同状态
        for state in scores.keys():
            if state == current_state:
                scores[state] = 0.1  # 降低相同状态的概率
            else:
                scores[state] = 1.0
        
        # 归一化
        total = sum(scores.values())
        if total > 0:
            scores = {k: v / total for k, v in scores.items()}
        
        return scores
    
    def _calculate_advanced_cycle_scores(self, states: List[str]) -> Dict[str, float]:
        """计算高级周期得分"""
        scores = {state: 0.0 for state in self.base_state_weights.keys()}
        
        for cycle_len, weight in self.cycle_patterns.items():
            if len(states) >= cycle_len:
                expected_state = states[-cycle_len] if cycle_len <= len(states) else None
                if expected_state and expected_state in scores:
                    scores[expected_state] += weight
        
        return scores
    
    def _calculate_heat_scores(self) -> Dict[str, float]:
        """计算状态热度得分"""
        return self.state_heat.copy()
    
    def _init_state_heat(self):
        """初始化状态热度"""
        self.state_heat = {state: 0.0 for state in self.base_state_weights.keys()}
        
        # 基于最近状态更新热度
        for i, state in enumerate(reversed(self.recent_states)):
            if state in self.state_heat:
                decay_factor = self.heat_decay ** i
                self.state_heat[state] += decay_factor
    
    def _analyze_patterns(self):
        """分析历史模式"""
        if len(self.recent_states) < 5:
            return
        
        # 分析状态转移模式
        transitions = {}
        for i in range(len(self.recent_states) - 1):
            current = self.recent_states[i]
            next_state = self.recent_states[i + 1]
            key = f"{current}->{next_state}"
            transitions[key] = transitions.get(key, 0) + 1
        
        self.pattern_scores = transitions
        self.logger.debug(f"分析到 {len(transitions)} 种转移模式")
    
    def get_algorithm_info(self) -> Dict[str, Any]:
        """获取算法信息"""
        info = self.get_model_info()
        info.update({
            "algorithm": self.algorithm.value,
            "weights": getattr(self, 'weights', {}),
            "recent_states_count": len(self.recent_states),
            "pattern_count": len(self.pattern_scores)
        })
        return info
    
    def supports_incremental_training(self) -> bool:
        """支持增量训练"""
        return True
    
    def update_with_new_data(self, new_states: List[str]):
        """使用新数据进行增量更新"""
        if not self.is_trained:
            self.logger.warning("模型尚未训练，无法进行增量更新")
            return
        
        # 添加新状态
        self.recent_states.extend(new_states)
        
        # 保持历史数据在合理范围内
        max_history = self.config.get("max_history", 50)
        if len(self.recent_states) > max_history:
            self.recent_states = self.recent_states[-max_history:]
        
        # 重新分析模式
        self._analyze_patterns()
        
        # 更新状态热度（如果使用高级算法）
        if self.algorithm == OddEvenAlgorithm.ADVANCED:
            self._init_state_heat()
        
        self.logger.info(f"增量更新完成，新增 {len(new_states)} 期数据")