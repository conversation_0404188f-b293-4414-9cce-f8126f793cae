"""
自动参数调优系统
基于历史数据自动找到最优参数配置
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Callable
import json
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import warnings
warnings.filterwarnings('ignore')

from ..core.interfaces import IPredictor, PredictionResult
from .enhanced_kill_system import EnhancedKillSystem
from .optimized_markov import OptimizedMarkovPredictor
from .enhanced_bayesian import EnhancedBayesianPredictor
from .intelligent_ensemble import IntelligentEnsemble


class HistoricalDataAnalyzer:
    """历史数据分析器"""
    
    def __init__(self, data: pd.DataFrame):
        self.data = data
        self.analysis_results = {}
        
    def analyze_data_characteristics(self) -> Dict[str, Any]:
        """分析数据特征"""
        print("[数据] 分析历史数据特征...")
        
        characteristics = {
            'total_periods': len(self.data),
            'date_range': self._get_date_range(),
            'red_ball_stats': self._analyze_red_balls(),
            'blue_ball_stats': self._analyze_blue_balls(),
            'pattern_analysis': self._analyze_patterns(),
            'volatility_analysis': self._analyze_volatility()
        }
        
        self.analysis_results = characteristics
        return characteristics
    
    def _get_date_range(self) -> Dict[str, str]:
        """获取日期范围"""
        if '期号' in self.data.columns:
            periods = self.data['期号'].astype(str)
            return {
                'start_period': periods.iloc[-1],  # 最早期号
                'end_period': periods.iloc[0],     # 最新期号
                'total_periods': len(periods)
            }
        return {'start_period': 'unknown', 'end_period': 'unknown', 'total_periods': len(self.data)}
    
    def _analyze_red_balls(self) -> Dict[str, Any]:
        """分析红球特征"""
        red_columns = ['红球1', '红球2', '红球3', '红球4', '红球5']
        all_red_numbers = []
        
        for _, row in self.data.iterrows():
            for col in red_columns:
                if col in row and pd.notna(row[col]):
                    all_red_numbers.append(int(row[col]))
        
        if not all_red_numbers:
            return {}
        
        # 频率分析
        from collections import Counter
        frequency = Counter(all_red_numbers)
        
        # 奇偶分析
        odd_count = sum(1 for num in all_red_numbers if num % 2 == 1)
        even_count = len(all_red_numbers) - odd_count
        
        # 大小分析 (>=18为大号)
        large_count = sum(1 for num in all_red_numbers if num >= 18)
        small_count = len(all_red_numbers) - large_count
        
        return {
            'frequency_distribution': dict(frequency),
            'most_frequent': frequency.most_common(5),
            'least_frequent': frequency.most_common()[-5:],
            'odd_even_ratio': f"{odd_count}:{even_count}",
            'large_small_ratio': f"{large_count}:{small_count}",
            'average_frequency': len(all_red_numbers) / 35,
            'std_frequency': np.std(list(frequency.values()))
        }
    
    def _analyze_blue_balls(self) -> Dict[str, Any]:
        """分析蓝球特征"""
        blue_columns = ['蓝球1', '蓝球2']
        all_blue_numbers = []
        
        for _, row in self.data.iterrows():
            for col in blue_columns:
                if col in row and pd.notna(row[col]):
                    all_blue_numbers.append(int(row[col]))
        
        if not all_blue_numbers:
            return {}
        
        from collections import Counter
        frequency = Counter(all_blue_numbers)
        
        # 奇偶分析
        odd_count = sum(1 for num in all_blue_numbers if num % 2 == 1)
        even_count = len(all_blue_numbers) - odd_count
        
        # 大小分析 (>=7为大号)
        large_count = sum(1 for num in all_blue_numbers if num >= 7)
        small_count = len(all_blue_numbers) - large_count
        
        return {
            'frequency_distribution': dict(frequency),
            'most_frequent': frequency.most_common(3),
            'least_frequent': frequency.most_common()[-3:],
            'odd_even_ratio': f"{odd_count}:{even_count}",
            'large_small_ratio': f"{large_count}:{small_count}",
            'average_frequency': len(all_blue_numbers) / 12,
            'std_frequency': np.std(list(frequency.values()))
        }
    
    def _analyze_patterns(self) -> Dict[str, Any]:
        """分析号码模式"""
        patterns = {
            'consecutive_patterns': self._analyze_consecutive_patterns(),
            'gap_patterns': self._analyze_gap_patterns(),
            'repeat_patterns': self._analyze_repeat_patterns()
        }
        return patterns
    
    def _analyze_consecutive_patterns(self) -> Dict[str, float]:
        """分析连号模式"""
        consecutive_counts = []
        red_columns = ['红球1', '红球2', '红球3', '红球4', '红球5']
        
        for _, row in self.data.iterrows():
            red_numbers = []
            for col in red_columns:
                if col in row and pd.notna(row[col]):
                    red_numbers.append(int(row[col]))
            
            if len(red_numbers) == 5:
                sorted_nums = sorted(red_numbers)
                consecutive = 0
                for i in range(len(sorted_nums) - 1):
                    if sorted_nums[i + 1] - sorted_nums[i] == 1:
                        consecutive += 1
                consecutive_counts.append(consecutive)
        
        return {
            'average_consecutive': np.mean(consecutive_counts) if consecutive_counts else 0,
            'max_consecutive': max(consecutive_counts) if consecutive_counts else 0,
            'consecutive_frequency': {i: consecutive_counts.count(i) for i in range(5)}
        }
    
    def _analyze_gap_patterns(self) -> Dict[str, float]:
        """分析间隔模式"""
        gaps = []
        red_columns = ['红球1', '红球2', '红球3', '红球4', '红球5']
        
        for _, row in self.data.iterrows():
            red_numbers = []
            for col in red_columns:
                if col in row and pd.notna(row[col]):
                    red_numbers.append(int(row[col]))
            
            if len(red_numbers) == 5:
                sorted_nums = sorted(red_numbers)
                period_gaps = [sorted_nums[i + 1] - sorted_nums[i] for i in range(len(sorted_nums) - 1)]
                gaps.extend(period_gaps)
        
        return {
            'average_gap': np.mean(gaps) if gaps else 0,
            'gap_std': np.std(gaps) if gaps else 0,
            'min_gap': min(gaps) if gaps else 0,
            'max_gap': max(gaps) if gaps else 0
        }
    
    def _analyze_repeat_patterns(self) -> Dict[str, Any]:
        """分析重复模式"""
        repeat_analysis = {'period_repeats': [], 'number_stability': {}}
        
        red_columns = ['红球1', '红球2', '红球3', '红球4', '红球5']
        previous_numbers = None
        
        for _, row in self.data.iterrows():
            current_numbers = set()
            for col in red_columns:
                if col in row and pd.notna(row[col]):
                    current_numbers.add(int(row[col]))
            
            if previous_numbers is not None and len(current_numbers) == 5:
                repeats = len(current_numbers & previous_numbers)
                repeat_analysis['period_repeats'].append(repeats)
            
            previous_numbers = current_numbers
        
        return {
            'average_period_repeats': np.mean(repeat_analysis['period_repeats']) if repeat_analysis['period_repeats'] else 0,
            'repeat_distribution': {i: repeat_analysis['period_repeats'].count(i) for i in range(6)}
        }
    
    def _analyze_volatility(self) -> Dict[str, float]:
        """分析波动性"""
        red_sums = []
        red_columns = ['红球1', '红球2', '红球3', '红球4', '红球5']
        
        for _, row in self.data.iterrows():
            red_numbers = []
            for col in red_columns:
                if col in row and pd.notna(row[col]):
                    red_numbers.append(int(row[col]))
            
            if len(red_numbers) == 5:
                red_sums.append(sum(red_numbers))
        
        return {
            'sum_average': np.mean(red_sums) if red_sums else 0,
            'sum_std': np.std(red_sums) if red_sums else 0,
            'sum_min': min(red_sums) if red_sums else 0,
            'sum_max': max(red_sums) if red_sums else 0,
            'volatility_coefficient': np.std(red_sums) / np.mean(red_sums) if red_sums and np.mean(red_sums) > 0 else 0
        }
    
    def recommend_parameters(self) -> Dict[str, Dict[str, Any]]:
        """基于数据特征推荐参数"""
        if not self.analysis_results:
            self.analyze_data_characteristics()
        
        recommendations = {}
        
        # 马尔科夫参数推荐
        recommendations['markov'] = self._recommend_markov_params()
        
        # 贝叶斯参数推荐
        recommendations['bayesian'] = self._recommend_bayesian_params()
        
        # 集成系统参数推荐
        recommendations['ensemble'] = self._recommend_ensemble_params()
        
        return recommendations
    
    def _recommend_markov_params(self) -> Dict[str, Any]:
        """推荐马尔科夫参数"""
        volatility = self.analysis_results.get('volatility_analysis', {}).get('volatility_coefficient', 0.5)
        total_periods = self.analysis_results.get('total_periods', 100)
        
        # 基于波动性和数据量推荐参数
        if volatility > 0.3:  # 高波动性
            red_order = 1  # 使用1阶，避免过拟合
            smoothing_factor = 0.05  # 较大的平滑因子
        else:  # 低波动性
            red_order = 2 if total_periods > 200 else 1  # 数据足够时使用2阶
            smoothing_factor = 0.01  # 较小的平滑因子
        
        return {
            'red_order': red_order,
            'blue_order': 1,  # 蓝球数据较少，使用1阶
            'smoothing_factor': smoothing_factor,
            'reasoning': f'基于波动性系数{volatility:.3f}和数据量{total_periods}推荐'
        }
    
    def _recommend_bayesian_params(self) -> Dict[str, Any]:
        """推荐贝叶斯参数"""
        total_periods = self.analysis_results.get('total_periods', 100)
        red_stats = self.analysis_results.get('red_ball_stats', {})
        std_frequency = red_stats.get('std_frequency', 1.0)
        
        # 基于数据量和频率分布推荐窗口大小
        if total_periods > 300:
            window_size = 150
        elif total_periods > 150:
            window_size = 100
        else:
            window_size = min(80, total_periods // 2)
        
        # 基于频率标准差推荐学习率
        if std_frequency > 2.0:  # 频率分布不均匀
            learning_rate = 0.15  # 较高的学习率
        else:
            learning_rate = 0.1   # 标准学习率
        
        return {
            'window_size': window_size,
            'learning_rate': learning_rate,
            'reasoning': f'基于数据量{total_periods}和频率标准差{std_frequency:.2f}推荐'
        }
    
    def _recommend_ensemble_params(self) -> Dict[str, Any]:
        """推荐集成系统参数"""
        total_periods = self.analysis_results.get('total_periods', 100)
        
        # 基于数据量推荐性能窗口
        if total_periods > 200:
            performance_window = 50
            adaptation_rate = 0.1
        else:
            performance_window = 30
            adaptation_rate = 0.15  # 数据少时更快适应
        
        return {
            'performance_window': performance_window,
            'adaptation_rate': adaptation_rate,
            'selection_strategy': 'weighted_ensemble',
            'reasoning': f'基于数据量{total_periods}推荐'
        }


class AutoParameterTuner:
    """自动参数调优器"""
    
    def __init__(self, data: pd.DataFrame):
        self.data = data
        self.analyzer = HistoricalDataAnalyzer(data)
        self.optimal_params = {}
        self.tuning_history = []
        
    def auto_tune_all_algorithms(self) -> Dict[str, Dict[str, Any]]:
        """自动调优所有算法参数"""
        print("[精准] 开始自动参数调优...")
        
        # 1. 分析历史数据特征
        print("[数据] 分析历史数据特征...")
        data_characteristics = self.analyzer.analyze_data_characteristics()
        
        # 2. 获取推荐参数
        print("[提示] 生成参数推荐...")
        recommended_params = self.analyzer.recommend_parameters()
        
        # 3. 精细调优
        print("[工具] 执行精细参数调优...")
        tuned_params = {}
        
        # 调优马尔科夫参数
        tuned_params['markov'] = self._fine_tune_markov(recommended_params['markov'])
        
        # 调优贝叶斯参数
        tuned_params['bayesian'] = self._fine_tune_bayesian(recommended_params['bayesian'])
        
        # 调优集成系统参数
        tuned_params['ensemble'] = self._fine_tune_ensemble(recommended_params['ensemble'])
        
        # 4. 验证最优参数
        print("[成功] 验证最优参数...")
        validation_results = self._validate_parameters(tuned_params)
        
        # 5. 保存结果
        self.optimal_params = tuned_params
        
        tuning_result = {
            'data_characteristics': data_characteristics,
            'recommended_params': recommended_params,
            'tuned_params': tuned_params,
            'validation_results': validation_results,
            'tuning_timestamp': datetime.now().isoformat()
        }
        
        self.tuning_history.append(tuning_result)
        
        print("[完成] 自动参数调优完成！")
        return tuning_result

    def _fine_tune_markov(self, base_params: Dict[str, Any]) -> Dict[str, Any]:
        """精细调优马尔科夫参数"""
        print("[工具] 精细调优马尔科夫参数...")

        # 基于推荐参数进行小范围调优
        base_order = base_params['red_order']
        base_smoothing = base_params['smoothing_factor']

        # 测试参数组合
        test_combinations = [
            {'red_order': base_order, 'blue_order': 1, 'smoothing_factor': base_smoothing},
            {'red_order': base_order, 'blue_order': 1, 'smoothing_factor': base_smoothing * 0.5},
            {'red_order': base_order, 'blue_order': 1, 'smoothing_factor': base_smoothing * 2.0},
        ]

        # 如果推荐1阶，也测试2阶
        if base_order == 1 and len(self.data) > 150:
            test_combinations.append({'red_order': 2, 'blue_order': 1, 'smoothing_factor': base_smoothing})

        best_params = base_params.copy()
        best_score = 0.0

        for params in test_combinations:
            try:
                score = self._evaluate_markov_params(params)
                if score > best_score:
                    best_score = score
                    best_params = params
                    best_params['reasoning'] = f'通过精细调优获得，评分: {score:.4f}'
            except Exception as e:
                print(f"[警告] 马尔科夫参数测试失败 {params}: {e}")
                continue

        print(f"[成功] 马尔科夫最优参数: {best_params}")
        return best_params

    def _fine_tune_bayesian(self, base_params: Dict[str, Any]) -> Dict[str, Any]:
        """精细调优贝叶斯参数"""
        print("[工具] 精细调优贝叶斯参数...")

        base_window = base_params['window_size']
        base_lr = base_params['learning_rate']

        # 测试参数组合
        test_combinations = [
            {'window_size': base_window, 'learning_rate': base_lr},
            {'window_size': int(base_window * 0.8), 'learning_rate': base_lr},
            {'window_size': int(base_window * 1.2), 'learning_rate': base_lr},
            {'window_size': base_window, 'learning_rate': base_lr * 0.8},
            {'window_size': base_window, 'learning_rate': base_lr * 1.2},
        ]

        best_params = base_params.copy()
        best_score = 0.0

        for params in test_combinations:
            try:
                score = self._evaluate_bayesian_params(params)
                if score > best_score:
                    best_score = score
                    best_params = params
                    best_params['reasoning'] = f'通过精细调优获得，评分: {score:.4f}'
            except Exception as e:
                print(f"[警告] 贝叶斯参数测试失败 {params}: {e}")
                continue

        print(f"[成功] 贝叶斯最优参数: {best_params}")
        return best_params

    def _fine_tune_ensemble(self, base_params: Dict[str, Any]) -> Dict[str, Any]:
        """精细调优集成系统参数"""
        print("[工具] 精细调优集成系统参数...")

        base_window = base_params['performance_window']
        base_rate = base_params['adaptation_rate']

        # 测试参数组合
        test_combinations = [
            {'performance_window': base_window, 'adaptation_rate': base_rate, 'selection_strategy': 'weighted_ensemble'},
            {'performance_window': int(base_window * 0.8), 'adaptation_rate': base_rate, 'selection_strategy': 'weighted_ensemble'},
            {'performance_window': int(base_window * 1.2), 'adaptation_rate': base_rate, 'selection_strategy': 'weighted_ensemble'},
            {'performance_window': base_window, 'adaptation_rate': base_rate * 0.8, 'selection_strategy': 'weighted_ensemble'},
            {'performance_window': base_window, 'adaptation_rate': base_rate, 'selection_strategy': 'top_performers'},
        ]

        best_params = base_params.copy()
        best_score = 0.0

        for params in test_combinations:
            try:
                score = self._evaluate_ensemble_params(params)
                if score > best_score:
                    best_score = score
                    best_params = params
                    best_params['reasoning'] = f'通过精细调优获得，评分: {score:.4f}'
            except Exception as e:
                print(f"[警告] 集成系统参数测试失败 {params}: {e}")
                continue

        print(f"[成功] 集成系统最优参数: {best_params}")
        return best_params

    def _evaluate_markov_params(self, params: Dict[str, Any]) -> float:
        """评估马尔科夫参数"""
        try:
            predictor = OptimizedMarkovPredictor(**{k: v for k, v in params.items() if k != 'reasoning'})
            return self._run_quick_backtest(predictor, periods=10)
        except Exception as e:
            print(f"[警告] 马尔科夫参数评估失败: {e}")
            return 0.0

    def _evaluate_bayesian_params(self, params: Dict[str, Any]) -> float:
        """评估贝叶斯参数"""
        try:
            predictor = EnhancedBayesianPredictor(**{k: v for k, v in params.items() if k != 'reasoning'})
            return self._run_quick_backtest(predictor, periods=10)
        except Exception as e:
            print(f"[警告] 贝叶斯参数评估失败: {e}")
            return 0.0

    def _evaluate_ensemble_params(self, params: Dict[str, Any]) -> float:
        """评估集成系统参数"""
        try:
            # 使用当前最优的子算法参数
            config = {
                'initial_weights': {'enhanced_kill': 0.35, 'markov': 0.30, 'bayesian': 0.35},
                **{k: v for k, v in params.items() if k != 'reasoning'}
            }
            predictor = IntelligentEnsemble(self.data, config)
            return self._run_quick_backtest(predictor, periods=8)
        except Exception as e:
            print(f"[警告] 集成系统参数评估失败: {e}")
            return 0.0

    def _run_quick_backtest(self, predictor: IPredictor, periods: int = 10) -> float:
        """快速回测评估"""
        if len(self.data) < periods + 10:
            return 0.0

        # 训练预测器
        train_data = self.data.iloc[periods:]
        if hasattr(predictor, 'train'):
            predictor.train(train_data)
        elif hasattr(predictor, 'initialize'):
            predictor.initialize(train_data)

        scores = []
        red_columns = ['红球1', '红球2', '红球3', '红球4', '红球5']

        for i in range(min(periods, len(self.data) - 10)):
            try:
                # 预测
                result = predictor.predict(self.data, i, prediction_type='numbers', ball_type='red')

                # 获取实际结果
                actual_row = self.data.iloc[i]
                actual_numbers = []
                for col in red_columns:
                    if col in actual_row and pd.notna(actual_row[col]):
                        actual_numbers.append(int(actual_row[col]))

                if len(actual_numbers) == 5 and result.predicted_value:
                    # 计算命中率
                    predicted = set(result.predicted_value)
                    actual = set(actual_numbers)
                    hit_rate = len(predicted & actual) / len(actual)

                    # 结合置信度
                    score = 0.7 * hit_rate + 0.3 * result.confidence
                    scores.append(score)

            except Exception as e:
                continue

        return np.mean(scores) if scores else 0.0

    def _validate_parameters(self, tuned_params: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """验证最优参数"""
        print("🔍 验证最优参数...")

        validation_results = {}

        # 验证马尔科夫参数
        if 'markov' in tuned_params:
            markov_score = self._evaluate_markov_params(tuned_params['markov'])
            validation_results['markov'] = {
                'score': markov_score,
                'params': tuned_params['markov'],
                'status': 'good' if markov_score > 0.3 else 'needs_improvement'
            }

        # 验证贝叶斯参数
        if 'bayesian' in tuned_params:
            bayesian_score = self._evaluate_bayesian_params(tuned_params['bayesian'])
            validation_results['bayesian'] = {
                'score': bayesian_score,
                'params': tuned_params['bayesian'],
                'status': 'good' if bayesian_score > 0.3 else 'needs_improvement'
            }

        # 验证集成系统参数
        if 'ensemble' in tuned_params:
            ensemble_score = self._evaluate_ensemble_params(tuned_params['ensemble'])
            validation_results['ensemble'] = {
                'score': ensemble_score,
                'params': tuned_params['ensemble'],
                'status': 'good' if ensemble_score > 0.3 else 'needs_improvement'
            }

        return validation_results

    def get_optimal_parameters(self) -> Dict[str, Dict[str, Any]]:
        """获取最优参数"""
        return self.optimal_params.copy()

    def save_tuning_results(self, filepath: str):
        """保存调优结果"""
        results = {
            'optimal_parameters': self.optimal_params,
            'tuning_history': self.tuning_history,
            'data_info': {
                'total_periods': len(self.data),
                'columns': list(self.data.columns)
            },
            'saved_at': datetime.now().isoformat()
        }

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        print(f"[成功] 参数调优结果已保存到: {filepath}")

    def load_tuning_results(self, filepath: str):
        """加载调优结果"""
        with open(filepath, 'r', encoding='utf-8') as f:
            results = json.load(f)

        self.optimal_params = results.get('optimal_parameters', {})
        self.tuning_history = results.get('tuning_history', [])

        print(f"[成功] 参数调优结果已从 {filepath} 加载")
        print(f"[数据] 加载了 {len(self.tuning_history)} 条调优记录")

    def print_optimization_summary(self):
        """打印优化摘要"""
        if not self.optimal_params:
            print("[失败] 暂无优化参数")
            return

        print("\n" + "="*60)
        print("[数据] 参数优化摘要")
        print("="*60)

        for algorithm, params in self.optimal_params.items():
            print(f"\n[工具] {algorithm.upper()} 算法最优参数:")
            for key, value in params.items():
                if key != 'reasoning':
                    print(f"  {key}: {value}")
            if 'reasoning' in params:
                print(f"  推理: {params['reasoning']}")

        if self.tuning_history:
            latest_validation = self.tuning_history[-1].get('validation_results', {})
            print(f"\n[成功] 验证结果:")
            for alg, result in latest_validation.items():
                status_emoji = "[成功]" if result['status'] == 'good' else "[警告]"
                print(f"  {status_emoji} {alg}: 评分 {result['score']:.4f} ({result['status']})")

        print("="*60)
