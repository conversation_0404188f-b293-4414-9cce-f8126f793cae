# 代码风格和约定

## Python编程规范
- **PEP 8**: 遵循Python官方代码风格指南
- **Python 3.10+**: 使用现代Python语法特性
- **类型提示**: 使用Type Hints进行类型检查
- **文档字符串**: 详细的docstring和注释

## 命名约定
- **类名**: PascalCase (如 `LotteryAnalyzer`)
- **函数/变量名**: snake_case (如 `calculate_odd_even_ratio`)
- **常量**: UPPER_CASE (如 `DEFAULT_CONFIG`)
- **私有成员**: 下划线前缀 (如 `_private_method`)

## 项目结构原则
- **src布局**: 所有源代码在src目录下
- **测试分离**: 测试代码在独立的tests目录
- **配置外部化**: 配置文件独立管理
- **模块化设计**: 高内聚、低耦合的模块设计

## 设计模式
- **工厂模式**: 用于创建预测器实例
- **适配器模式**: 统一不同预测器接口
- **策略模式**: 支持多种算法切换
- **观察者模式**: 用于监控和日志记录

## 错误处理
- **异常处理**: 完善的try-catch机制
- **日志记录**: 结构化日志记录
- **参数验证**: 输入参数严格验证
- **优雅降级**: 算法失败时的备选方案