"""
统一预测器
整合所有版本的预测算法，通过配置选择不同的预测策略
遵循DRY原则，避免代码重复
"""

from typing import List, Tuple, Dict, Any, Optional
from dataclasses import dataclass
from collections import defaultdict
import numpy as np

# 导入统一配置
from config.unified_config import get_predictor_config, get_model_config

# 导入基础工具
from src.utils.unified_utils import calculate_all_ratios_unified
from src.utils.utils import get_all_red_states, get_all_blue_states


@dataclass
class PredictionResult:
    """预测结果数据类"""
    predicted_state: str
    confidence: float
    probabilities: Dict[str, float]
    algorithm_used: str
    sub_predictions: Dict[str, Tuple[str, float]] = None


class UnifiedOddEvenPredictor:
    """统一奇偶比预测器"""
    
    def __init__(self, algorithm: str = None):
        """
        初始化统一预测器
        
        Args:
            algorithm: 算法类型 ('enhanced', 'optimized', 'advanced')
        """
        self.config = get_predictor_config()
        self.algorithm = algorithm or self.config.odd_even_algorithm
        
        # 基础状态权重（基于历史频率）
        self.base_state_weights = {
            '3:2': 0.369,  # 历史频率36.9%
            '2:3': 0.315,  # 历史频率31.5%
            '4:1': 0.151,  # 历史频率15.1%
            '1:4': 0.123,  # 历史频率12.3%
            '5:0': 0.025,  # 历史频率2.5%
            '0:5': 0.017   # 历史频率1.7%
        }
        
        # 算法特定参数
        self._init_algorithm_params()
        
        # 训练状态
        self.is_trained = False
        self.recent_states = []
        self.state_heat = {}
        self.pattern_scores = {}
        
    def _init_algorithm_params(self):
        """初始化算法特定参数"""
        if self.algorithm == "enhanced":
            self.weights = {
                "cycle": 0.4,
                "markov": 0.3,
                "anti": 0.2,
                "frequency": 0.1
            }
            self.cycle_lengths = [2, 3, 5, 7, 10]
            
        elif self.algorithm == "optimized":
            self.weights = {
                "markov": 0.5,
                "frequency": 0.3,
                "trend": 0.2
            }
            
        elif self.algorithm == "advanced":
            self.weights = {
                "anti_consecutive": 0.4,
                "pattern": 0.3,
                "heat": 0.2,
                "frequency": 0.1
            }
            self.cycle_patterns = {2: 0.3, 3: 0.4, 5: 0.2, 7: 0.1}
            self.heat_decay = 0.8
            
        else:
            # 默认使用enhanced算法
            self.algorithm = "enhanced"
            self._init_algorithm_params()
    
    def train(self, state_sequence: List[str]):
        """
        训练预测器
        
        Args:
            state_sequence: 状态序列（从新到旧）
        """
        self.recent_states = state_sequence[:30]  # 保存最近30期状态
        
        if self.algorithm == "enhanced":
            self._train_enhanced()
        elif self.algorithm == "optimized":
            self._train_optimized()
        elif self.algorithm == "advanced":
            self._train_advanced()
            
        self.is_trained = True
    
    def _train_enhanced(self):
        """训练增强算法"""
        # 计算周期模式得分
        self.pattern_scores = {}
        for cycle_len in self.cycle_lengths:
            if len(self.recent_states) > cycle_len * 2:
                matches = 0
                total = 0
                for i in range(cycle_len, len(self.recent_states) - cycle_len):
                    if self.recent_states[i] == self.recent_states[i + cycle_len]:
                        matches += 1
                    total += 1
                if total > 0:
                    self.pattern_scores[cycle_len] = matches / total
    
    def _train_optimized(self):
        """训练优化算法"""
        # 计算近期趋势偏置
        self.recent_trend_bias = {}
        if len(self.recent_states) >= 5:
            recent_5 = self.recent_states[:5]
            state_count = {}
            for state in recent_5:
                state_count[state] = state_count.get(state, 0) + 1
            
            for state in get_all_red_states():
                recent_freq = state_count.get(state, 0) / len(recent_5)
                if recent_freq > 0.4:
                    self.recent_trend_bias[state] = 0.7  # 降低权重
                else:
                    self.recent_trend_bias[state] = 1.2  # 提高权重
    
    def _train_advanced(self):
        """训练高级算法"""
        # 计算状态热度
        self._calculate_state_heat()
        # 分析模式
        self._analyze_patterns()
    
    def _calculate_state_heat(self):
        """计算状态热度"""
        self.state_heat = {}
        for state in get_all_red_states():
            heat = 0
            for i, recent_state in enumerate(self.recent_states[:10]):
                if recent_state == state:
                    # 越近期的状态热度越高
                    heat += (10 - i) / 10
            self.state_heat[state] = heat / 10 if len(self.recent_states) > 0 else 0
    
    def _analyze_patterns(self):
        """分析周期模式"""
        self.pattern_scores = {}
        for cycle_len in self.cycle_patterns.keys():
            if len(self.recent_states) > cycle_len * 2:
                matches = 0
                total = 0
                for i in range(cycle_len, len(self.recent_states) - cycle_len):
                    if self.recent_states[i] == self.recent_states[i + cycle_len]:
                        matches += 1
                    total += 1
                if total > 0:
                    self.pattern_scores[cycle_len] = matches / total
    
    def predict(self, current_state: str = None) -> PredictionResult:
        """
        预测下一期的奇偶比状态
        
        Args:
            current_state: 当前状态
            
        Returns:
            PredictionResult: 预测结果
        """
        if not self.is_trained:
            return self._predict_by_frequency()
        
        if self.algorithm == "enhanced":
            return self._predict_enhanced(current_state)
        elif self.algorithm == "optimized":
            return self._predict_optimized(current_state)
        elif self.algorithm == "advanced":
            return self._predict_advanced(current_state)
        else:
            return self._predict_by_frequency()
    
    def _predict_enhanced(self, current_state: str) -> PredictionResult:
        """增强算法预测"""
        # 多策略融合预测
        cycle_pred = self._predict_by_cycle()
        markov_pred = self._predict_by_markov(current_state)
        anti_pred = self._predict_by_anti_consecutive(current_state)
        frequency_pred = self._predict_by_frequency()
        
        # 融合预测结果
        final_probs = {}
        all_states = get_all_red_states()
        
        for state in all_states:
            prob = (
                cycle_pred.probabilities.get(state, 0) * self.weights["cycle"] +
                markov_pred.probabilities.get(state, 0) * self.weights["markov"] +
                anti_pred.probabilities.get(state, 0) * self.weights["anti"] +
                frequency_pred.probabilities.get(state, 0) * self.weights["frequency"]
            )
            final_probs[state] = prob
        
        # 归一化
        total_prob = sum(final_probs.values())
        if total_prob > 0:
            final_probs = {k: v/total_prob for k, v in final_probs.items()}
        
        best_state = max(final_probs.items(), key=lambda x: x[1])
        confidence = self._calculate_confidence(final_probs, best_state[1], current_state)
        
        return PredictionResult(
            predicted_state=best_state[0],
            confidence=confidence,
            probabilities=final_probs,
            algorithm_used="enhanced",
            sub_predictions={
                "cycle": (cycle_pred.predicted_state, cycle_pred.confidence),
                "markov": (markov_pred.predicted_state, markov_pred.confidence),
                "anti": (anti_pred.predicted_state, anti_pred.confidence),
                "frequency": (frequency_pred.predicted_state, frequency_pred.confidence)
            }
        )
    
    def _predict_optimized(self, current_state: str) -> PredictionResult:
        """优化算法预测"""
        # 多策略融合预测
        markov_pred = self._predict_by_markov(current_state)
        frequency_pred = self._predict_by_frequency()
        trend_pred = self._predict_by_trend()
        
        # 融合预测结果
        final_probs = {}
        all_states = get_all_red_states()
        
        for state in all_states:
            prob = (
                markov_pred.probabilities.get(state, 0) * self.weights["markov"] +
                frequency_pred.probabilities.get(state, 0) * self.weights["frequency"] +
                trend_pred.probabilities.get(state, 0) * self.weights["trend"]
            )
            
            # 应用近期趋势偏置
            prob *= self.recent_trend_bias.get(state, 1.0)
            final_probs[state] = prob
        
        # 归一化
        total_prob = sum(final_probs.values())
        if total_prob > 0:
            final_probs = {k: v/total_prob for k, v in final_probs.items()}
        
        best_state = max(final_probs.items(), key=lambda x: x[1])
        confidence = self._calculate_confidence(final_probs, best_state[1], current_state)
        
        return PredictionResult(
            predicted_state=best_state[0],
            confidence=confidence,
            probabilities=final_probs,
            algorithm_used="optimized",
            sub_predictions={
                "markov": (markov_pred.predicted_state, markov_pred.confidence),
                "frequency": (frequency_pred.predicted_state, frequency_pred.confidence),
                "trend": (trend_pred.predicted_state, trend_pred.confidence)
            }
        )
    
    def _predict_advanced(self, current_state: str) -> PredictionResult:
        """高级算法预测"""
        # 多策略融合预测
        anti_consecutive_pred = self._predict_by_anti_consecutive(current_state)
        pattern_pred = self._predict_by_pattern()
        heat_pred = self._predict_by_heat()
        frequency_pred = self._predict_by_frequency()
        
        # 融合预测结果
        final_probs = {}
        all_states = get_all_red_states()
        
        for state in all_states:
            prob = (
                anti_consecutive_pred.probabilities.get(state, 0) * self.weights["anti_consecutive"] +
                pattern_pred.probabilities.get(state, 0) * self.weights["pattern"] +
                heat_pred.probabilities.get(state, 0) * self.weights["heat"] +
                frequency_pred.probabilities.get(state, 0) * self.weights["frequency"]
            )
            final_probs[state] = prob

        # 归一化
        total_prob = sum(final_probs.values())
        if total_prob > 0:
            final_probs = {k: v/total_prob for k, v in final_probs.items()}

        best_state = max(final_probs.items(), key=lambda x: x[1])
        confidence = self._calculate_confidence(final_probs, best_state[1], current_state)

        return PredictionResult(
            predicted_state=best_state[0],
            confidence=confidence,
            probabilities=final_probs,
            algorithm_used="advanced",
            sub_predictions={
                "anti_consecutive": (anti_consecutive_pred.predicted_state, anti_consecutive_pred.confidence),
                "pattern": (pattern_pred.predicted_state, pattern_pred.confidence),
                "heat": (heat_pred.predicted_state, heat_pred.confidence),
                "frequency": (frequency_pred.predicted_state, frequency_pred.confidence)
            }
        )

    def _predict_by_cycle(self) -> PredictionResult:
        """基于周期模式的预测"""
        probs = {state: 0 for state in get_all_red_states()}

        if len(self.recent_states) < 5:
            return self._predict_by_frequency()

        # 检查不同周期长度
        cycle_votes = defaultdict(float)

        for cycle_len in self.cycle_lengths:
            if len(self.recent_states) >= cycle_len:
                cycle_state = self.recent_states[-cycle_len]
                confidence = self._calculate_cycle_confidence(cycle_len)
                cycle_votes[cycle_state] += confidence

        # 如果有周期预测
        if cycle_votes:
            total_votes = sum(cycle_votes.values())
            for state, votes in cycle_votes.items():
                probs[state] = votes / total_votes
        else:
            return self._predict_by_frequency()

        best_state = max(probs.items(), key=lambda x: x[1])
        confidence = best_state[1]

        return PredictionResult(
            predicted_state=best_state[0],
            confidence=confidence,
            probabilities=probs,
            algorithm_used="cycle"
        )

    def _calculate_cycle_confidence(self, cycle_len: int) -> float:
        """计算周期置信度"""
        if cycle_len not in self.pattern_scores:
            return 0.1
        return self.pattern_scores[cycle_len]

    def _predict_by_markov(self, current_state: str) -> PredictionResult:
        """基于马尔可夫链的预测"""
        if not current_state or len(self.recent_states) < 3:
            return self._predict_by_frequency()

        # 计算状态转移概率
        transition_counts = defaultdict(lambda: defaultdict(int))

        for i in range(len(self.recent_states) - 1):
            from_state = self.recent_states[i + 1]
            to_state = self.recent_states[i]
            transition_counts[from_state][to_state] += 1

        # 获取当前状态的转移概率
        if current_state in transition_counts:
            total_transitions = sum(transition_counts[current_state].values())
            probs = {}
            for state in get_all_red_states():
                count = transition_counts[current_state].get(state, 0)
                probs[state] = count / total_transitions if total_transitions > 0 else self.base_state_weights[state]
        else:
            probs = self.base_state_weights.copy()

        best_state = max(probs.items(), key=lambda x: x[1])
        confidence = best_state[1]

        return PredictionResult(
            predicted_state=best_state[0],
            confidence=confidence,
            probabilities=probs,
            algorithm_used="markov"
        )

    def _predict_by_anti_consecutive(self, current_state: str) -> PredictionResult:
        """基于反连续性的预测"""
        probs = self.base_state_weights.copy()

        if current_state:
            # 降低当前状态的概率
            probs[current_state] *= 0.3

            # 如果最近2期相同，进一步降低
            if len(self.recent_states) >= 2 and self.recent_states[-1] == self.recent_states[-2]:
                probs[current_state] *= 0.1

        # 归一化
        total = sum(probs.values())
        if total > 0:
            probs = {k: v/total for k, v in probs.items()}

        best_state = max(probs.items(), key=lambda x: x[1])
        confidence = best_state[1]

        return PredictionResult(
            predicted_state=best_state[0],
            confidence=confidence,
            probabilities=probs,
            algorithm_used="anti_consecutive"
        )

    def _predict_by_frequency(self) -> PredictionResult:
        """基于历史频率的预测"""
        probs = self.base_state_weights.copy()
        best_state = max(probs.items(), key=lambda x: x[1])
        confidence = best_state[1]

        return PredictionResult(
            predicted_state=best_state[0],
            confidence=confidence,
            probabilities=probs,
            algorithm_used="frequency"
        )

    def _predict_by_trend(self) -> PredictionResult:
        """基于近期趋势预测"""
        if len(self.recent_states) < 5:
            return self._predict_by_frequency()

        # 分析最近5期的状态分布
        recent_5 = self.recent_states[:5]
        state_count = {}
        for state in recent_5:
            state_count[state] = state_count.get(state, 0) + 1

        # 基于反向逻辑：如果某状态最近出现较多，下期可能转向其他状态
        all_states = get_all_red_states()
        probs = {}

        for state in all_states:
            recent_freq = state_count.get(state, 0) / len(recent_5)
            # 反向权重：最近出现多的状态，下期概率降低
            if recent_freq > 0.4:  # 如果最近出现频率超过40%
                probs[state] = self.base_state_weights[state] * 0.7  # 降低权重
            else:
                probs[state] = self.base_state_weights[state] * 1.2  # 提高权重

        # 归一化
        total = sum(probs.values())
        if total > 0:
            probs = {k: v/total for k, v in probs.items()}

        best_state = max(probs.items(), key=lambda x: x[1])
        confidence = best_state[1]

        return PredictionResult(
            predicted_state=best_state[0],
            confidence=confidence,
            probabilities=probs,
            algorithm_used="trend"
        )

    def _predict_by_pattern(self) -> PredictionResult:
        """基于周期性模式预测"""
        probs = {state: 0 for state in get_all_red_states()}

        for cycle_len, cycle_weight in self.cycle_patterns.items():
            if len(self.recent_states) > cycle_len:
                pattern_state = self.recent_states[cycle_len]
                pattern_score = self.pattern_scores.get(cycle_len, 0)
                probs[pattern_state] += cycle_weight * pattern_score

        # 如果没有明显模式，使用频率分布
        if sum(probs.values()) == 0:
            probs = self.base_state_weights.copy()
        else:
            # 归一化
            total = sum(probs.values())
            probs = {k: v/total for k, v in probs.items()}

        best_state = max(probs.items(), key=lambda x: x[1])
        confidence = best_state[1]

        return PredictionResult(
            predicted_state=best_state[0],
            confidence=confidence,
            probabilities=probs,
            algorithm_used="pattern"
        )

    def _predict_by_heat(self) -> PredictionResult:
        """基于状态热度的反向预测"""
        probs = {}

        # 反向热度：热度高的状态，下期概率降低
        for state in get_all_red_states():
            heat = self.state_heat.get(state, 0)
            # 反向权重：热度越高，下期概率越低
            probs[state] = self.base_state_weights[state] * (1 - heat * self.heat_decay)

        # 归一化
        total = sum(probs.values())
        if total > 0:
            probs = {k: v/total for k, v in probs.items()}

        best_state = max(probs.items(), key=lambda x: x[1])
        confidence = best_state[1]

        return PredictionResult(
            predicted_state=best_state[0],
            confidence=confidence,
            probabilities=probs,
            algorithm_used="heat"
        )

    def _calculate_confidence(self, probs: Dict[str, float], max_prob: float, current_state: str) -> float:
        """计算置信度"""
        base_confidence = max_prob

        # 概率分布的集中度加成
        entropy = -sum(p * np.log(p + 1e-10) for p in probs.values())
        max_entropy = np.log(len(probs))
        concentration_bonus = (max_entropy - entropy) / max_entropy * 0.2

        return min(base_confidence + concentration_bonus, 1.0)

    def _calculate_advanced_confidence(self, probs: Dict[str, float], max_prob: float, current_state: str) -> float:
        """计算高级置信度"""
        base_confidence = max_prob

        # 反连续性加成
        predicted_state = max(probs.items(), key=lambda x: x[1])[0]
        anti_consecutive_bonus = 0
        if current_state and predicted_state != current_state:
            anti_consecutive_bonus = 0.15  # 反连续性加成

        # 模式一致性加成
        pattern_bonus = 0
        for cycle_len in [2, 3, 5]:
            if len(self.recent_states) > cycle_len:
                if self.recent_states[cycle_len] == predicted_state:
                    pattern_score = self.pattern_scores.get(cycle_len, 0)
                    pattern_bonus += pattern_score * 0.1

        return min(base_confidence + anti_consecutive_bonus + pattern_bonus, 1.0)

    def predict_top_k(self, current_state: str = None, k: int = 2) -> List[Tuple[str, float]]:
        """
        预测Top-K状态

        Args:
            current_state: 当前状态
            k: 返回前k个预测

        Returns:
            List[Tuple[str, float]]: Top-K预测结果
        """
        result = self.predict(current_state)
        sorted_probs = sorted(result.probabilities.items(), key=lambda x: x[1], reverse=True)
        return sorted_probs[:k]

    def get_prediction_details(self, current_state: str = None) -> Dict[str, Any]:
        """
        获取详细的预测信息

        Args:
            current_state: 当前状态

        Returns:
            Dict[str, Any]: 详细预测信息
        """
        result = self.predict(current_state)

        return {
            "algorithm": self.algorithm,
            "prediction": result.predicted_state,
            "confidence": result.confidence,
            "probabilities": result.probabilities,
            "sub_predictions": result.sub_predictions,
            "current_state": current_state,
            "recent_states": self.recent_states[:10],
            "weights": self.weights,
            "is_trained": self.is_trained
        }


class UnifiedSizeRatioPredictor:
    """统一大小比预测器"""

    def __init__(self, ball_type: str = "red", algorithm: str = None):
        """
        初始化统一大小比预测器

        Args:
            ball_type: 球类型 ('red', 'blue')
            algorithm: 算法类型 ('enhanced', 'optimized', 'advanced')
        """
        self.config = get_predictor_config()
        self.ball_type = ball_type
        self.algorithm = algorithm or self.config.size_ratio_algorithm

        # 基础状态权重（基于历史频率）
        if ball_type == "red":
            self.base_state_weights = {
                '3:2': 0.35,   # 红球大小比历史频率
                '2:3': 0.30,
                '4:1': 0.15,
                '1:4': 0.12,
                '5:0': 0.05,
                '0:5': 0.03
            }
        else:  # blue
            self.base_state_weights = {
                '1:1': 0.45,   # 蓝球大小比历史频率
                '2:0': 0.25,
                '0:2': 0.20,
                '1:0': 0.06,
                '0:1': 0.04
            }

        # 算法特定参数
        self._init_algorithm_params()

        # 训练状态
        self.is_trained = False
        self.recent_states = []
        self.state_heat = {}
        self.pattern_scores = {}

    def _init_algorithm_params(self):
        """初始化算法特定参数"""
        if self.algorithm == "enhanced":
            self.weights = {
                "markov": 0.5,
                "frequency": 0.3,
                "trend": 0.2
            }

        elif self.algorithm == "optimized":
            self.weights = {
                "markov": 0.6,
                "frequency": 0.4
            }

        elif self.algorithm == "advanced":
            self.weights = {
                "markov": 0.4,
                "pattern": 0.3,
                "heat": 0.2,
                "frequency": 0.1
            }
            self.cycle_patterns = {2: 0.4, 3: 0.3, 5: 0.2, 7: 0.1}
            self.heat_decay = 0.7

        else:
            # 默认使用enhanced算法
            self.algorithm = "enhanced"
            self._init_algorithm_params()

    def train(self, state_sequence: List[str]):
        """
        训练预测器

        Args:
            state_sequence: 状态序列（从新到旧）
        """
        self.recent_states = state_sequence[:30]  # 保存最近30期状态

        if self.algorithm == "enhanced":
            self._train_enhanced()
        elif self.algorithm == "optimized":
            self._train_optimized()
        elif self.algorithm == "advanced":
            self._train_advanced()

        self.is_trained = True

    def _train_enhanced(self):
        """训练增强算法"""
        # 计算近期趋势
        if len(self.recent_states) >= 5:
            recent_5 = self.recent_states[:5]
            state_count = {}
            for state in recent_5:
                state_count[state] = state_count.get(state, 0) + 1

            self.recent_trend_bias = {}
            for state in self.base_state_weights.keys():
                recent_freq = state_count.get(state, 0) / len(recent_5)
                if recent_freq > 0.4:
                    self.recent_trend_bias[state] = 0.8  # 降低权重
                else:
                    self.recent_trend_bias[state] = 1.1  # 提高权重

    def _train_optimized(self):
        """训练优化算法"""
        # 简化的训练，主要依赖马尔可夫链
        pass

    def _train_advanced(self):
        """训练高级算法"""
        # 计算状态热度
        self._calculate_state_heat()
        # 分析模式
        self._analyze_patterns()

    def _calculate_state_heat(self):
        """计算状态热度"""
        self.state_heat = {}
        for state in self.base_state_weights.keys():
            heat = 0
            for i, recent_state in enumerate(self.recent_states[:10]):
                if recent_state == state:
                    # 越近期的状态热度越高
                    heat += (10 - i) / 10
            self.state_heat[state] = heat / 10 if len(self.recent_states) > 0 else 0

    def _analyze_patterns(self):
        """分析周期模式"""
        self.pattern_scores = {}
        for cycle_len in self.cycle_patterns.keys():
            if len(self.recent_states) > cycle_len * 2:
                matches = 0
                total = 0
                for i in range(cycle_len, len(self.recent_states) - cycle_len):
                    if self.recent_states[i] == self.recent_states[i + cycle_len]:
                        matches += 1
                    total += 1
                if total > 0:
                    self.pattern_scores[cycle_len] = matches / total

    def predict(self, current_state: str = None) -> PredictionResult:
        """
        预测下一期的大小比状态

        Args:
            current_state: 当前状态

        Returns:
            PredictionResult: 预测结果
        """
        if not self.is_trained:
            return self._predict_by_frequency()

        if self.algorithm == "enhanced":
            return self._predict_enhanced(current_state)
        elif self.algorithm == "optimized":
            return self._predict_optimized(current_state)
        elif self.algorithm == "advanced":
            return self._predict_advanced(current_state)
        else:
            return self._predict_by_frequency()

    def _predict_enhanced(self, current_state: str) -> PredictionResult:
        """增强算法预测"""
        # 多策略融合预测
        markov_pred = self._predict_by_markov(current_state)
        frequency_pred = self._predict_by_frequency()
        trend_pred = self._predict_by_trend()

        # 融合预测结果
        final_probs = {}
        all_states = list(self.base_state_weights.keys())

        for state in all_states:
            prob = (
                markov_pred.probabilities.get(state, 0) * self.weights["markov"] +
                frequency_pred.probabilities.get(state, 0) * self.weights["frequency"] +
                trend_pred.probabilities.get(state, 0) * self.weights["trend"]
            )
            final_probs[state] = prob

        # 归一化
        total_prob = sum(final_probs.values())
        if total_prob > 0:
            final_probs = {k: v/total_prob for k, v in final_probs.items()}

        best_state = max(final_probs.items(), key=lambda x: x[1])
        confidence = self._calculate_confidence(final_probs, best_state[1], current_state)

        return PredictionResult(
            predicted_state=best_state[0],
            confidence=confidence,
            probabilities=final_probs,
            algorithm_used=f"enhanced_{self.ball_type}",
            sub_predictions={
                "markov": (markov_pred.predicted_state, markov_pred.confidence),
                "frequency": (frequency_pred.predicted_state, frequency_pred.confidence),
                "trend": (trend_pred.predicted_state, trend_pred.confidence)
            }
        )

    def _predict_optimized(self, current_state: str) -> PredictionResult:
        """优化算法预测"""
        # 简化的双策略融合
        markov_pred = self._predict_by_markov(current_state)
        frequency_pred = self._predict_by_frequency()

        # 融合预测结果
        final_probs = {}
        all_states = list(self.base_state_weights.keys())

        for state in all_states:
            prob = (
                markov_pred.probabilities.get(state, 0) * self.weights["markov"] +
                frequency_pred.probabilities.get(state, 0) * self.weights["frequency"]
            )
            final_probs[state] = prob

        # 归一化
        total_prob = sum(final_probs.values())
        if total_prob > 0:
            final_probs = {k: v/total_prob for k, v in final_probs.items()}

        best_state = max(final_probs.items(), key=lambda x: x[1])
        confidence = self._calculate_confidence(final_probs, best_state[1], current_state)

        return PredictionResult(
            predicted_state=best_state[0],
            confidence=confidence,
            probabilities=final_probs,
            algorithm_used=f"optimized_{self.ball_type}",
            sub_predictions={
                "markov": (markov_pred.predicted_state, markov_pred.confidence),
                "frequency": (frequency_pred.predicted_state, frequency_pred.confidence)
            }
        )

    def _predict_advanced(self, current_state: str) -> PredictionResult:
        """高级算法预测"""
        # 多策略融合预测
        markov_pred = self._predict_by_markov(current_state)
        pattern_pred = self._predict_by_pattern()
        heat_pred = self._predict_by_heat()
        frequency_pred = self._predict_by_frequency()

        # 融合预测结果
        final_probs = {}
        all_states = list(self.base_state_weights.keys())

        for state in all_states:
            prob = (
                markov_pred.probabilities.get(state, 0) * self.weights["markov"] +
                pattern_pred.probabilities.get(state, 0) * self.weights["pattern"] +
                heat_pred.probabilities.get(state, 0) * self.weights["heat"] +
                frequency_pred.probabilities.get(state, 0) * self.weights["frequency"]
            )
            final_probs[state] = prob

        # 归一化
        total_prob = sum(final_probs.values())
        if total_prob > 0:
            final_probs = {k: v/total_prob for k, v in final_probs.items()}

        best_state = max(final_probs.items(), key=lambda x: x[1])
        confidence = self._calculate_confidence(final_probs, best_state[1], current_state)

        return PredictionResult(
            predicted_state=best_state[0],
            confidence=confidence,
            probabilities=final_probs,
            algorithm_used=f"advanced_{self.ball_type}",
            sub_predictions={
                "markov": (markov_pred.predicted_state, markov_pred.confidence),
                "pattern": (pattern_pred.predicted_state, pattern_pred.confidence),
                "heat": (heat_pred.predicted_state, heat_pred.confidence),
                "frequency": (frequency_pred.predicted_state, frequency_pred.confidence)
            }
        )

    def _predict_by_markov(self, current_state: str) -> PredictionResult:
        """基于马尔可夫链的预测"""
        if not current_state or len(self.recent_states) < 3:
            return self._predict_by_frequency()

        # 计算状态转移概率
        transition_counts = defaultdict(lambda: defaultdict(int))

        for i in range(len(self.recent_states) - 1):
            from_state = self.recent_states[i + 1]
            to_state = self.recent_states[i]
            transition_counts[from_state][to_state] += 1

        # 获取当前状态的转移概率
        if current_state in transition_counts:
            total_transitions = sum(transition_counts[current_state].values())
            probs = {}
            for state in self.base_state_weights.keys():
                count = transition_counts[current_state].get(state, 0)
                probs[state] = count / total_transitions if total_transitions > 0 else self.base_state_weights[state]
        else:
            probs = self.base_state_weights.copy()

        best_state = max(probs.items(), key=lambda x: x[1])
        confidence = best_state[1]

        return PredictionResult(
            predicted_state=best_state[0],
            confidence=confidence,
            probabilities=probs,
            algorithm_used=f"markov_{self.ball_type}"
        )

    def _predict_by_frequency(self) -> PredictionResult:
        """基于历史频率的预测"""
        probs = self.base_state_weights.copy()
        best_state = max(probs.items(), key=lambda x: x[1])
        confidence = best_state[1]

        return PredictionResult(
            predicted_state=best_state[0],
            confidence=confidence,
            probabilities=probs,
            algorithm_used=f"frequency_{self.ball_type}"
        )

    def _predict_by_trend(self) -> PredictionResult:
        """基于近期趋势预测"""
        if len(self.recent_states) < 5:
            return self._predict_by_frequency()

        # 应用近期趋势偏置
        probs = {}
        for state in self.base_state_weights.keys():
            bias = getattr(self, 'recent_trend_bias', {}).get(state, 1.0)
            probs[state] = self.base_state_weights[state] * bias

        # 归一化
        total = sum(probs.values())
        if total > 0:
            probs = {k: v/total for k, v in probs.items()}

        best_state = max(probs.items(), key=lambda x: x[1])
        confidence = best_state[1]

        return PredictionResult(
            predicted_state=best_state[0],
            confidence=confidence,
            probabilities=probs,
            algorithm_used=f"trend_{self.ball_type}"
        )

    def _predict_by_pattern(self) -> PredictionResult:
        """基于周期性模式预测"""
        probs = {state: 0 for state in self.base_state_weights.keys()}

        for cycle_len, cycle_weight in self.cycle_patterns.items():
            if len(self.recent_states) > cycle_len:
                pattern_state = self.recent_states[cycle_len]
                pattern_score = self.pattern_scores.get(cycle_len, 0)
                if pattern_state in probs:
                    probs[pattern_state] += cycle_weight * pattern_score

        # 如果没有明显模式，使用频率分布
        if sum(probs.values()) == 0:
            probs = self.base_state_weights.copy()
        else:
            # 归一化
            total = sum(probs.values())
            probs = {k: v/total for k, v in probs.items()}

        best_state = max(probs.items(), key=lambda x: x[1])
        confidence = best_state[1]

        return PredictionResult(
            predicted_state=best_state[0],
            confidence=confidence,
            probabilities=probs,
            algorithm_used=f"pattern_{self.ball_type}"
        )

    def _predict_by_heat(self) -> PredictionResult:
        """基于状态热度的反向预测"""
        probs = {}

        # 反向热度：热度高的状态，下期概率降低
        for state in self.base_state_weights.keys():
            heat = self.state_heat.get(state, 0)
            # 反向权重：热度越高，下期概率越低
            probs[state] = self.base_state_weights[state] * (1 - heat * self.heat_decay)

        # 归一化
        total = sum(probs.values())
        if total > 0:
            probs = {k: v/total for k, v in probs.items()}

        best_state = max(probs.items(), key=lambda x: x[1])
        confidence = best_state[1]

        return PredictionResult(
            predicted_state=best_state[0],
            confidence=confidence,
            probabilities=probs,
            algorithm_used=f"heat_{self.ball_type}"
        )

    def _calculate_confidence(self, probs: Dict[str, float], max_prob: float, current_state: str) -> float:
        """计算置信度"""
        base_confidence = max_prob

        # 概率分布的集中度加成
        entropy = -sum(p * np.log(p + 1e-10) for p in probs.values())
        max_entropy = np.log(len(probs))
        concentration_bonus = (max_entropy - entropy) / max_entropy * 0.15

        return min(base_confidence + concentration_bonus, 1.0)

    def predict_top_k(self, current_state: str = None, k: int = 2) -> List[Tuple[str, float]]:
        """
        预测Top-K状态

        Args:
            current_state: 当前状态
            k: 返回前k个预测

        Returns:
            List[Tuple[str, float]]: Top-K预测结果
        """
        result = self.predict(current_state)
        sorted_probs = sorted(result.probabilities.items(), key=lambda x: x[1], reverse=True)
        return sorted_probs[:k]

    def get_prediction_details(self, current_state: str = None) -> Dict[str, Any]:
        """
        获取详细的预测信息

        Args:
            current_state: 当前状态

        Returns:
            Dict[str, Any]: 详细预测信息
        """
        result = self.predict(current_state)

        return {
            "ball_type": self.ball_type,
            "algorithm": self.algorithm,
            "prediction": result.predicted_state,
            "confidence": result.confidence,
            "probabilities": result.probabilities,
            "sub_predictions": result.sub_predictions,
            "current_state": current_state,
            "recent_states": self.recent_states[:10],
            "weights": self.weights,
            "is_trained": self.is_trained
        }
