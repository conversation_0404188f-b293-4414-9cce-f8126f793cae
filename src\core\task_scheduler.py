"""统一任务调度系统

提供定时任务、异步任务和任务队列管理功能。
"""

import time
import threading
import asyncio
import uuid
from typing import Dict, List, Optional, Any, Callable, Union, Coroutine
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, Future
from queue import Queue, PriorityQueue
from functools import wraps
import json
from pathlib import Path

from .exceptions import LotteryPredictorException, ErrorCode
from .logging_manager import get_logger, log_performance


class TaskStatus(Enum):
    """任务状态"""

    PENDING = "pending"  # 等待中
    RUNNING = "running"  # 运行中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"  # 失败
    CANCELLED = "cancelled"  # 已取消
    RETRYING = "retrying"  # 重试中


class TaskPriority(Enum):
    """任务优先级"""

    LOW = 3
    NORMAL = 2
    HIGH = 1
    CRITICAL = 0


class ScheduleType(Enum):
    """调度类型"""

    ONCE = "once"  # 一次性
    INTERVAL = "interval"  # 间隔执行
    CRON = "cron"  # Cron表达式
    DAILY = "daily"  # 每日
    WEEKLY = "weekly"  # 每周
    MONTHLY = "monthly"  # 每月


@dataclass
class TaskResult:
    """任务结果"""

    task_id: str
    status: TaskStatus
    result: Any = None
    error: Optional[str] = None
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    duration: Optional[float] = None
    retry_count: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)

    @property
    def is_success(self) -> bool:
        """是否成功"""
        return self.status == TaskStatus.COMPLETED

    @property
    def is_failed(self) -> bool:
        """是否失败"""
        return self.status == TaskStatus.FAILED

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "task_id": self.task_id,
            "status": self.status.value,
            "result": self.result,
            "error": self.error,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "duration": self.duration,
            "retry_count": self.retry_count,
            "metadata": self.metadata,
        }


@dataclass
class Task:
    """任务定义"""

    id: str
    name: str
    func: Callable
    args: tuple = field(default_factory=tuple)
    kwargs: Dict[str, Any] = field(default_factory=dict)
    priority: TaskPriority = TaskPriority.NORMAL
    max_retries: int = 0
    retry_delay: float = 1.0
    timeout: Optional[float] = None
    created_at: float = field(default_factory=time.time)
    scheduled_at: Optional[float] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

    # 调度相关
    schedule_type: Optional[ScheduleType] = None
    schedule_config: Dict[str, Any] = field(default_factory=dict)

    # 状态
    status: TaskStatus = TaskStatus.PENDING
    result: Optional[TaskResult] = None

    def __lt__(self, other):
        """用于优先级队列排序"""
        if not isinstance(other, Task):
            return NotImplemented

        # 首先按优先级排序
        if self.priority.value != other.priority.value:
            return self.priority.value < other.priority.value

        # 然后按调度时间排序
        self_time = self.scheduled_at or self.created_at
        other_time = other.scheduled_at or other.created_at
        return self_time < other_time

    def should_run_now(self) -> bool:
        """检查是否应该现在运行"""
        if self.scheduled_at is None:
            return True
        return time.time() >= self.scheduled_at

    def calculate_next_run_time(self) -> Optional[float]:
        """计算下次运行时间"""
        if self.schedule_type == ScheduleType.ONCE:
            return None

        current_time = time.time()

        if self.schedule_type == ScheduleType.INTERVAL:
            interval = self.schedule_config.get("interval", 60)
            return current_time + interval

        elif self.schedule_type == ScheduleType.DAILY:
            # 每日在指定时间运行
            hour = self.schedule_config.get("hour", 0)
            minute = self.schedule_config.get("minute", 0)

            now = datetime.fromtimestamp(current_time)
            next_run = now.replace(hour=hour, minute=minute, second=0, microsecond=0)

            if next_run <= now:
                next_run += timedelta(days=1)

            return next_run.timestamp()

        elif self.schedule_type == ScheduleType.WEEKLY:
            # 每周在指定时间运行
            weekday = self.schedule_config.get("weekday", 0)  # 0=Monday
            hour = self.schedule_config.get("hour", 0)
            minute = self.schedule_config.get("minute", 0)

            now = datetime.fromtimestamp(current_time)
            days_ahead = weekday - now.weekday()

            if days_ahead <= 0:  # 目标日期已过或是今天
                days_ahead += 7

            next_run = now + timedelta(days=days_ahead)
            next_run = next_run.replace(
                hour=hour, minute=minute, second=0, microsecond=0
            )

            return next_run.timestamp()

        return None


class TaskQueue:
    """任务队列"""

    def __init__(self, maxsize: int = 0):
        self._queue = PriorityQueue(maxsize=maxsize)
        self._lock = threading.RLock()

    def put(self, task: Task, block: bool = True, timeout: Optional[float] = None):
        """添加任务到队列"""
        with self._lock:
            self._queue.put(task, block=block, timeout=timeout)

    def get(self, block: bool = True, timeout: Optional[float] = None) -> Task:
        """从队列获取任务"""
        return self._queue.get(block=block, timeout=timeout)

    def empty(self) -> bool:
        """检查队列是否为空"""
        return self._queue.empty()

    def qsize(self) -> int:
        """获取队列大小"""
        return self._queue.qsize()

    def task_done(self):
        """标记任务完成"""
        self._queue.task_done()


class TaskExecutor:
    """任务执行器"""

    def __init__(self, max_workers: int = 4):
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.logger = get_logger("TaskExecutor")
        self._running_tasks: Dict[str, Future] = {}
        self._lock = threading.RLock()

    def submit_task(self, task: Task) -> Future:
        """提交任务执行"""
        with self._lock:
            future = self.executor.submit(self._execute_task, task)
            self._running_tasks[task.id] = future
            return future

    def _execute_task(self, task: Task) -> TaskResult:
        """执行任务"""
        start_time = time.time()
        task.status = TaskStatus.RUNNING

        result = TaskResult(
            task_id=task.id, status=TaskStatus.RUNNING, start_time=start_time
        )

        try:
            self.logger.info(f"开始执行任务: {task.name} ({task.id})")

            # 执行任务函数
            if asyncio.iscoroutinefunction(task.func):
                # 异步函数
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    task_result = loop.run_until_complete(
                        asyncio.wait_for(
                            task.func(*task.args, **task.kwargs), timeout=task.timeout
                        )
                    )
                finally:
                    loop.close()
            else:
                # 同步函数
                task_result = task.func(*task.args, **task.kwargs)

            # 任务成功完成
            result.status = TaskStatus.COMPLETED
            result.result = task_result
            task.status = TaskStatus.COMPLETED

            self.logger.info(f"任务执行成功: {task.name} ({task.id})")

        except Exception as e:
            # 任务执行失败
            error_msg = str(e)
            result.status = TaskStatus.FAILED
            result.error = error_msg
            task.status = TaskStatus.FAILED

            self.logger.error(f"任务执行失败: {task.name} ({task.id}) - {error_msg}")

        finally:
            end_time = time.time()
            result.end_time = end_time
            result.duration = end_time - start_time
            task.result = result

            # 从运行任务列表中移除
            with self._lock:
                self._running_tasks.pop(task.id, None)

        return result

    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        with self._lock:
            future = self._running_tasks.get(task_id)
            if future:
                return future.cancel()
            return False

    def get_running_tasks(self) -> List[str]:
        """获取正在运行的任务ID列表"""
        with self._lock:
            return list(self._running_tasks.keys())

    def shutdown(self, wait: bool = True):
        """关闭执行器"""
        self.executor.shutdown(wait=wait)


class CronParser:
    """简单的Cron表达式解析器"""

    @staticmethod
    def parse(cron_expr: str) -> Dict[str, Any]:
        """解析Cron表达式

        格式: minute hour day month weekday
        支持: * 数字 范围(1-5) 列表(1,3,5) 步长(*/2)
        """
        parts = cron_expr.strip().split()
        if len(parts) != 5:
            raise ValueError(f"无效的Cron表达式: {cron_expr}")

        fields = ["minute", "hour", "day", "month", "weekday"]
        parsed = {}

        for i, (field, value) in enumerate(zip(fields, parts)):
            parsed[field] = CronParser._parse_field(value, field)

        return parsed

    @staticmethod
    def _parse_field(value: str, field: str) -> List[int]:
        """解析单个字段"""
        if value == "*":
            return CronParser._get_field_range(field)

        result = []

        # 处理逗号分隔的列表
        for part in value.split(","):
            if "/" in part:
                # 步长
                range_part, step = part.split("/", 1)
                step = int(step)

                if range_part == "*":
                    base_range = CronParser._get_field_range(field)
                else:
                    base_range = CronParser._parse_range(range_part, field)

                result.extend(base_range[::step])

            elif "-" in part:
                # 范围
                result.extend(CronParser._parse_range(part, field))

            else:
                # 单个值
                result.append(int(part))

        return sorted(list(set(result)))

    @staticmethod
    def _parse_range(range_str: str, field: str) -> List[int]:
        """解析范围"""
        start, end = map(int, range_str.split("-", 1))
        return list(range(start, end + 1))

    @staticmethod
    def _get_field_range(field: str) -> List[int]:
        """获取字段的完整范围"""
        ranges = {
            "minute": list(range(0, 60)),
            "hour": list(range(0, 24)),
            "day": list(range(1, 32)),
            "month": list(range(1, 13)),
            "weekday": list(range(0, 7)),  # 0=Sunday
        }
        return ranges.get(field, [])

    @staticmethod
    def next_run_time(
        cron_config: Dict[str, Any], from_time: Optional[float] = None
    ) -> float:
        """计算下次运行时间"""
        if from_time is None:
            from_time = time.time()

        current = datetime.fromtimestamp(from_time)

        # 从下一分钟开始查找
        next_time = current.replace(second=0, microsecond=0) + timedelta(minutes=1)

        # 最多查找一年
        max_time = next_time + timedelta(days=366)

        while next_time < max_time:
            if CronParser._matches_cron(next_time, cron_config):
                return next_time.timestamp()
            next_time += timedelta(minutes=1)

        raise ValueError("无法找到下次运行时间")

    @staticmethod
    def _matches_cron(dt: datetime, cron_config: Dict[str, Any]) -> bool:
        """检查时间是否匹配Cron配置"""
        return (
            dt.minute in cron_config["minute"]
            and dt.hour in cron_config["hour"]
            and dt.day in cron_config["day"]
            and dt.month in cron_config["month"]
            and dt.weekday() in cron_config["weekday"]
        )


class TaskScheduler:
    """任务调度器"""

    def __init__(self, max_workers: int = 4, max_queue_size: int = 1000):
        self.logger = get_logger("TaskScheduler")

        # 任务队列和执行器
        self.task_queue = TaskQueue(maxsize=max_queue_size)
        self.executor = TaskExecutor(max_workers=max_workers)

        # 任务存储
        self.tasks: Dict[str, Task] = {}
        self.scheduled_tasks: Dict[str, Task] = {}  # 定时任务
        self.task_results: Dict[str, TaskResult] = {}

        # 调度线程
        self._scheduler_thread = None
        self._worker_threads: List[threading.Thread] = []
        self._stop_event = threading.Event()

        # 锁
        self._lock = threading.RLock()

        # 启动调度器
        self.start()

    def start(self):
        """启动调度器"""
        if self._scheduler_thread and self._scheduler_thread.is_alive():
            return

        self._stop_event.clear()

        # 启动调度线程
        self._scheduler_thread = threading.Thread(
            target=self._scheduler_worker, daemon=True
        )
        self._scheduler_thread.start()

        # 启动工作线程
        for i in range(2):  # 2个工作线程处理队列任务
            worker = threading.Thread(target=self._queue_worker, daemon=True)
            worker.start()
            self._worker_threads.append(worker)

        self.logger.info("任务调度器已启动")

    def stop(self, wait: bool = True):
        """停止调度器"""
        self._stop_event.set()

        if wait:
            # 等待线程结束
            if self._scheduler_thread and self._scheduler_thread.is_alive():
                self._scheduler_thread.join(timeout=5)

            for worker in self._worker_threads:
                if worker.is_alive():
                    worker.join(timeout=5)

        # 关闭执行器
        self.executor.shutdown(wait=wait)

        self.logger.info("任务调度器已停止")

    def add_task(
        self,
        name: str,
        func: Callable,
        args: tuple = (),
        kwargs: Optional[Dict[str, Any]] = None,
        priority: TaskPriority = TaskPriority.NORMAL,
        max_retries: int = 0,
        retry_delay: float = 1.0,
        timeout: Optional[float] = None,
        scheduled_at: Optional[float] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> str:
        """添加一次性任务"""
        task_id = str(uuid.uuid4())

        task = Task(
            id=task_id,
            name=name,
            func=func,
            args=args,
            kwargs=kwargs or {},
            priority=priority,
            max_retries=max_retries,
            retry_delay=retry_delay,
            timeout=timeout,
            scheduled_at=scheduled_at,
            metadata=metadata or {},
            schedule_type=ScheduleType.ONCE,
        )

        with self._lock:
            self.tasks[task_id] = task

            if scheduled_at is None or scheduled_at <= time.time():
                # 立即执行
                self.task_queue.put(task)
            else:
                # 定时执行
                self.scheduled_tasks[task_id] = task

        self.logger.info(f"添加任务: {name} ({task_id})")
        return task_id

    def add_scheduled_task(
        self,
        name: str,
        func: Callable,
        schedule_type: ScheduleType,
        schedule_config: Dict[str, Any],
        args: tuple = (),
        kwargs: Optional[Dict[str, Any]] = None,
        priority: TaskPriority = TaskPriority.NORMAL,
        max_retries: int = 0,
        retry_delay: float = 1.0,
        timeout: Optional[float] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> str:
        """添加定时任务"""
        task_id = str(uuid.uuid4())

        # 计算首次运行时间
        if schedule_type == ScheduleType.CRON:
            cron_expr = schedule_config.get("cron")
            if not cron_expr:
                raise ValueError("Cron任务需要提供cron表达式")

            cron_config = CronParser.parse(cron_expr)
            next_run = CronParser.next_run_time(cron_config)
            schedule_config["parsed_cron"] = cron_config
        else:
            next_run = time.time()

        task = Task(
            id=task_id,
            name=name,
            func=func,
            args=args,
            kwargs=kwargs or {},
            priority=priority,
            max_retries=max_retries,
            retry_delay=retry_delay,
            timeout=timeout,
            scheduled_at=next_run,
            metadata=metadata or {},
            schedule_type=schedule_type,
            schedule_config=schedule_config,
        )

        with self._lock:
            self.tasks[task_id] = task
            self.scheduled_tasks[task_id] = task

        self.logger.info(f"添加定时任务: {name} ({task_id}) - {schedule_type.value}")
        return task_id

    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        with self._lock:
            task = self.tasks.get(task_id)
            if not task:
                return False

            # 尝试取消正在运行的任务
            if task.status == TaskStatus.RUNNING:
                cancelled = self.executor.cancel_task(task_id)
                if cancelled:
                    task.status = TaskStatus.CANCELLED
            else:
                task.status = TaskStatus.CANCELLED

            # 从调度任务中移除
            self.scheduled_tasks.pop(task_id, None)

            self.logger.info(f"取消任务: {task.name} ({task_id})")
            return True

    def get_task(self, task_id: str) -> Optional[Task]:
        """获取任务"""
        with self._lock:
            return self.tasks.get(task_id)

    def get_task_result(self, task_id: str) -> Optional[TaskResult]:
        """获取任务结果"""
        with self._lock:
            task = self.tasks.get(task_id)
            return task.result if task else None

    def get_tasks(self, status: Optional[TaskStatus] = None) -> List[Task]:
        """获取任务列表"""
        with self._lock:
            tasks = list(self.tasks.values())
            if status:
                tasks = [t for t in tasks if t.status == status]
            return tasks

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            total_tasks = len(self.tasks)
            status_counts = {}

            for task in self.tasks.values():
                status = task.status.value
                status_counts[status] = status_counts.get(status, 0) + 1

            return {
                "total_tasks": total_tasks,
                "scheduled_tasks": len(self.scheduled_tasks),
                "queue_size": self.task_queue.qsize(),
                "running_tasks": len(self.executor.get_running_tasks()),
                "status_counts": status_counts,
                "worker_count": self.executor.max_workers,
            }

    def _scheduler_worker(self):
        """调度器工作线程"""
        while not self._stop_event.wait(1):  # 每秒检查一次
            try:
                self._check_scheduled_tasks()
            except Exception as e:
                self.logger.error(f"调度器工作线程错误: {e}")

    def _check_scheduled_tasks(self):
        """检查定时任务"""
        current_time = time.time()
        tasks_to_run = []

        with self._lock:
            for task_id, task in list(self.scheduled_tasks.items()):
                if task.should_run_now():
                    tasks_to_run.append(task)

                    # 计算下次运行时间
                    next_run_time = self._calculate_next_run_time(task)

                    if next_run_time:
                        # 创建新的任务实例用于下次运行
                        new_task_id = str(uuid.uuid4())
                        new_task = Task(
                            id=new_task_id,
                            name=task.name,
                            func=task.func,
                            args=task.args,
                            kwargs=task.kwargs,
                            priority=task.priority,
                            max_retries=task.max_retries,
                            retry_delay=task.retry_delay,
                            timeout=task.timeout,
                            scheduled_at=next_run_time,
                            metadata=task.metadata.copy(),
                            schedule_type=task.schedule_type,
                            schedule_config=task.schedule_config.copy(),
                        )

                        self.tasks[new_task_id] = new_task
                        self.scheduled_tasks[new_task_id] = new_task

                    # 移除当前任务
                    del self.scheduled_tasks[task_id]

        # 将到期任务加入执行队列
        for task in tasks_to_run:
            self.task_queue.put(task)

    def _calculate_next_run_time(self, task: Task) -> Optional[float]:
        """计算下次运行时间"""
        if task.schedule_type == ScheduleType.CRON:
            cron_config = task.schedule_config.get("parsed_cron")
            if cron_config:
                return CronParser.next_run_time(cron_config)
        else:
            return task.calculate_next_run_time()

        return None

    def _queue_worker(self):
        """队列工作线程"""
        while not self._stop_event.is_set():
            try:
                # 从队列获取任务
                task = self.task_queue.get(timeout=1)

                # 提交任务执行
                future = self.executor.submit_task(task)

                # 标记队列任务完成
                self.task_queue.task_done()

            except Exception:
                # 超时或其他错误，继续循环
                continue

    def clear_completed_tasks(self, older_than: Optional[float] = None):
        """清理已完成的任务"""
        if older_than is None:
            older_than = time.time() - 86400  # 默认清理1天前的任务

        with self._lock:
            tasks_to_remove = []

            for task_id, task in self.tasks.items():
                if (
                    task.status
                    in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]
                    and task.created_at < older_than
                ):
                    tasks_to_remove.append(task_id)

            for task_id in tasks_to_remove:
                del self.tasks[task_id]
                self.task_results.pop(task_id, None)

            self.logger.info(f"清理已完成任务: {len(tasks_to_remove)} 个")

    def export_tasks(self, file_path: str):
        """导出任务信息"""
        try:
            tasks_data = {
                "timestamp": time.time(),
                "tasks": [],
                "statistics": self.get_statistics(),
            }

            with self._lock:
                for task in self.tasks.values():
                    task_data = {
                        "id": task.id,
                        "name": task.name,
                        "status": task.status.value,
                        "priority": task.priority.value,
                        "created_at": task.created_at,
                        "scheduled_at": task.scheduled_at,
                        "schedule_type": (
                            task.schedule_type.value if task.schedule_type else None
                        ),
                        "schedule_config": task.schedule_config,
                        "metadata": task.metadata,
                    }

                    if task.result:
                        task_data["result"] = task.result.to_dict()

                    tasks_data["tasks"].append(task_data)

            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(tasks_data, f, indent=2, ensure_ascii=False)

            self.logger.info(f"任务信息已导出到: {file_path}")

        except Exception as e:
            self.logger.error(f"导出任务信息失败: {e}")
            raise LotteryPredictorException(
                f"导出任务信息失败: {e}", ErrorCode.FILE_ACCESS_ERROR
            )


def scheduled_task(
    schedule_type: ScheduleType,
    schedule_config: Dict[str, Any],
    name: Optional[str] = None,
    priority: TaskPriority = TaskPriority.NORMAL,
    max_retries: int = 0,
    retry_delay: float = 1.0,
    timeout: Optional[float] = None,
):
    """定时任务装饰器

    Args:
        schedule_type: 调度类型
        schedule_config: 调度配置
        name: 任务名称
        priority: 优先级
        max_retries: 最大重试次数
        retry_delay: 重试延迟
        timeout: 超时时间

    Returns:
        Callable: 装饰器函数
    """

    def decorator(func: Callable) -> Callable:
        task_name = name or func.__name__

        # 自动注册任务
        get_task_scheduler().add_scheduled_task(
            name=task_name,
            func=func,
            schedule_type=schedule_type,
            schedule_config=schedule_config,
            priority=priority,
            max_retries=max_retries,
            retry_delay=retry_delay,
            timeout=timeout,
        )

        return func

    return decorator


def async_task(
    name: Optional[str] = None,
    priority: TaskPriority = TaskPriority.NORMAL,
    max_retries: int = 0,
    retry_delay: float = 1.0,
    timeout: Optional[float] = None,
):
    """异步任务装饰器

    Args:
        name: 任务名称
        priority: 优先级
        max_retries: 最大重试次数
        retry_delay: 重试延迟
        timeout: 超时时间

    Returns:
        Callable: 装饰器函数
    """

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            task_name = name or func.__name__

            return get_task_scheduler().add_task(
                name=task_name,
                func=func,
                args=args,
                kwargs=kwargs,
                priority=priority,
                max_retries=max_retries,
                retry_delay=retry_delay,
                timeout=timeout,
            )

        return wrapper

    return decorator


# 全局任务调度器实例
_task_scheduler = None


def get_task_scheduler() -> TaskScheduler:
    """获取任务调度器实例

    Returns:
        TaskScheduler: 任务调度器实例
    """
    global _task_scheduler
    if _task_scheduler is None:
        _task_scheduler = TaskScheduler()
    return _task_scheduler


def add_task(
    name: str,
    func: Callable,
    args: tuple = (),
    kwargs: Optional[Dict[str, Any]] = None,
    priority: TaskPriority = TaskPriority.NORMAL,
    scheduled_at: Optional[float] = None,
) -> str:
    """添加任务的便捷函数

    Args:
        name: 任务名称
        func: 任务函数
        args: 位置参数
        kwargs: 关键字参数
        priority: 优先级
        scheduled_at: 调度时间

    Returns:
        str: 任务ID
    """
    return get_task_scheduler().add_task(
        name=name,
        func=func,
        args=args,
        kwargs=kwargs,
        priority=priority,
        scheduled_at=scheduled_at,
    )


def get_task_status(task_id: str) -> Optional[TaskStatus]:
    """获取任务状态的便捷函数

    Args:
        task_id: 任务ID

    Returns:
        Optional[TaskStatus]: 任务状态
    """
    task = get_task_scheduler().get_task(task_id)
    return task.status if task else None


# 导出的公共接口
__all__ = [
    "TaskStatus",
    "TaskPriority",
    "ScheduleType",
    "TaskResult",
    "Task",
    "TaskQueue",
    "TaskExecutor",
    "CronParser",
    "TaskScheduler",
    "scheduled_task",
    "async_task",
    "get_task_scheduler",
    "add_task",
    "get_task_status",
]
