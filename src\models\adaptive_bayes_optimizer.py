"""
自适应贝叶斯优化器
根据上一期预测是否命中来动态调整贝叶斯参数
"""

import json
from typing import Dict, List, Tuple
from collections import deque


class AdaptiveBayesOptimizer:
    """
    自适应贝叶斯优化器
    根据预测反馈动态调整参数
    """
    
    def __init__(self):
        self.name = "自适应贝叶斯优化器"
        
        # 初始权重配置
        self.base_weights = {
            'markov_chain': 0.35,
            'frequency_analysis': 0.25, 
            'trend_following': 0.20,
            'pattern_recognition': 0.20
        }
        
        # 初始置信度配置
        self.base_confidences = {
            'markov_chain': 0.8,
            'frequency_analysis': 0.7,
            'trend_following': 0.6,
            'pattern_recognition': 0.5
        }
        
        # 当前动态权重和置信度
        self.current_weights = self.base_weights.copy()
        self.current_confidences = self.base_confidences.copy()
        
        # 预测历史记录
        self.prediction_history = deque(maxlen=20)  # 保存最近20期
        
        # 各证据源的表现统计
        self.evidence_performance = {
            'markov_chain': {'hits': 0, 'total': 0, 'accuracy': 0.0},
            'frequency_analysis': {'hits': 0, 'total': 0, 'accuracy': 0.0},
            'trend_following': {'hits': 0, 'total': 0, 'accuracy': 0.0},
            'pattern_recognition': {'hits': 0, 'total': 0, 'accuracy': 0.0}
        }
        
        # 自适应参数
        self.learning_rate = 0.1  # 学习率
        self.momentum = 0.9       # 动量因子
        self.min_weight = 0.05    # 最小权重
        self.max_weight = 0.6     # 最大权重
        self.min_confidence = 0.3 # 最小置信度
        self.max_confidence = 0.95# 最大置信度
        
        # 权重调整历史（用于动量）
        self.weight_momentum = {key: 0.0 for key in self.base_weights.keys()}
        self.confidence_momentum = {key: 0.0 for key in self.base_confidences.keys()}
    
    def record_prediction_result(self, period: str, predicted_state: str, actual_state: str, 
                               evidence_predictions: Dict[str, str], bayes_probability: float):
        """
        记录预测结果并更新参数
        
        Args:
            period: 期号
            predicted_state: 预测状态
            actual_state: 实际状态
            evidence_predictions: 各证据源的预测结果
            bayes_probability: 贝叶斯概率
        """
        # 记录整体预测结果
        is_hit = predicted_state == actual_state
        
        prediction_record = {
            'period': period,
            'predicted_state': predicted_state,
            'actual_state': actual_state,
            'is_hit': is_hit,
            'bayes_probability': bayes_probability,
            'evidence_predictions': evidence_predictions,
            'weights_used': self.current_weights.copy(),
            'confidences_used': self.current_confidences.copy()
        }
        
        self.prediction_history.append(prediction_record)
        
        # 更新各证据源的表现统计
        self._update_evidence_performance(evidence_predictions, actual_state)
        
        # 根据结果调整参数
        self._adaptive_adjustment(is_hit, evidence_predictions, actual_state)
    
    def _update_evidence_performance(self, evidence_predictions: Dict[str, str], actual_state: str):
        """更新各证据源的表现统计"""
        for evidence_name, predicted_state in evidence_predictions.items():
            if evidence_name in self.evidence_performance:
                self.evidence_performance[evidence_name]['total'] += 1
                if predicted_state == actual_state:
                    self.evidence_performance[evidence_name]['hits'] += 1
                
                # 计算准确率
                total = self.evidence_performance[evidence_name]['total']
                hits = self.evidence_performance[evidence_name]['hits']
                self.evidence_performance[evidence_name]['accuracy'] = hits / total if total > 0 else 0.0
    
    def _adaptive_adjustment(self, is_hit: bool, evidence_predictions: Dict[str, str], actual_state: str):
        """
        根据预测结果进行自适应调整
        
        策略：
        1. 如果命中：增强表现好的证据源权重和置信度
        2. 如果未命中：降低表现差的证据源权重，提升表现好的
        3. 使用动量机制避免过度调整
        """
        # 计算各证据源在本次预测中的表现
        evidence_scores = {}
        for evidence_name, predicted_state in evidence_predictions.items():
            evidence_scores[evidence_name] = 1.0 if predicted_state == actual_state else 0.0
        
        # 调整权重
        self._adjust_weights(is_hit, evidence_scores)
        
        # 调整置信度
        self._adjust_confidences(is_hit, evidence_scores)
        
        # 应用约束
        self._apply_constraints()
    
    def _adjust_weights(self, is_hit: bool, evidence_scores: Dict[str, float]):
        """调整证据源权重"""
        for evidence_name in self.current_weights.keys():
            if evidence_name in evidence_scores:
                evidence_score = evidence_scores[evidence_name]
                historical_accuracy = self.evidence_performance[evidence_name]['accuracy']
                
                # 计算调整量
                if is_hit:
                    # 整体命中时，奖励表现好的证据源
                    adjustment = self.learning_rate * evidence_score * 0.5
                else:
                    # 整体未命中时，根据历史表现调整
                    if historical_accuracy > 0.4:  # 历史表现好的证据源
                        adjustment = self.learning_rate * (evidence_score - 0.5) * 0.3
                    else:  # 历史表现差的证据源
                        adjustment = -self.learning_rate * (1 - evidence_score) * 0.2
                
                # 应用动量
                self.weight_momentum[evidence_name] = (
                    self.momentum * self.weight_momentum[evidence_name] + 
                    (1 - self.momentum) * adjustment
                )
                
                # 更新权重
                self.current_weights[evidence_name] += self.weight_momentum[evidence_name]
        
        # 重新归一化权重
        total_weight = sum(self.current_weights.values())
        if total_weight > 0:
            for key in self.current_weights.keys():
                self.current_weights[key] /= total_weight
    
    def _adjust_confidences(self, is_hit: bool, evidence_scores: Dict[str, float]):
        """调整证据源置信度"""
        for evidence_name in self.current_confidences.keys():
            if evidence_name in evidence_scores:
                evidence_score = evidence_scores[evidence_name]
                historical_accuracy = self.evidence_performance[evidence_name]['accuracy']
                
                # 计算调整量
                if is_hit:
                    # 整体命中时，提升表现好的证据源置信度
                    adjustment = self.learning_rate * evidence_score * 0.1
                else:
                    # 整体未命中时，降低表现差的证据源置信度
                    adjustment = -self.learning_rate * (1 - evidence_score) * 0.05
                
                # 结合历史表现
                if historical_accuracy > 0.5:
                    adjustment *= 1.2  # 历史表现好的证据源调整幅度更大
                else:
                    adjustment *= 0.8  # 历史表现差的证据源调整幅度更小
                
                # 应用动量
                self.confidence_momentum[evidence_name] = (
                    self.momentum * self.confidence_momentum[evidence_name] + 
                    (1 - self.momentum) * adjustment
                )
                
                # 更新置信度
                self.current_confidences[evidence_name] += self.confidence_momentum[evidence_name]
    
    def _apply_constraints(self):
        """应用权重和置信度约束"""
        # 权重约束
        for key in self.current_weights.keys():
            self.current_weights[key] = max(self.min_weight, 
                                          min(self.max_weight, self.current_weights[key]))
        
        # 重新归一化权重
        total_weight = sum(self.current_weights.values())
        if total_weight > 0:
            for key in self.current_weights.keys():
                self.current_weights[key] /= total_weight
        
        # 置信度约束
        for key in self.current_confidences.keys():
            self.current_confidences[key] = max(self.min_confidence, 
                                              min(self.max_confidence, self.current_confidences[key]))
    
    def get_current_parameters(self) -> Tuple[List[float], List[float]]:
        """获取当前的权重和置信度参数"""
        weights = [
            self.current_weights['markov_chain'],
            self.current_weights['frequency_analysis'],
            self.current_weights['trend_following'],
            self.current_weights['pattern_recognition']
        ]
        
        confidences = [
            self.current_confidences['markov_chain'],
            self.current_confidences['frequency_analysis'],
            self.current_confidences['trend_following'],
            self.current_confidences['pattern_recognition']
        ]
        
        return weights, confidences
    
    def get_performance_summary(self) -> Dict:
        """获取性能总结"""
        if not self.prediction_history:
            return {"error": "无预测历史"}
        
        # 计算整体表现
        total_predictions = len(self.prediction_history)
        total_hits = sum(1 for record in self.prediction_history if record['is_hit'])
        overall_accuracy = total_hits / total_predictions
        
        # 计算最近表现
        recent_records = list(self.prediction_history)[-10:]  # 最近10期
        recent_hits = sum(1 for record in recent_records if record['is_hit'])
        recent_accuracy = recent_hits / len(recent_records) if recent_records else 0
        
        return {
            'total_predictions': total_predictions,
            'overall_accuracy': overall_accuracy,
            'recent_accuracy': recent_accuracy,
            'evidence_performance': self.evidence_performance.copy(),
            'current_weights': self.current_weights.copy(),
            'current_confidences': self.current_confidences.copy()
        }
    
    def save_state(self, filepath: str):
        """保存优化器状态"""
        state = {
            'current_weights': self.current_weights,
            'current_confidences': self.current_confidences,
            'evidence_performance': self.evidence_performance,
            'prediction_history': list(self.prediction_history),
            'weight_momentum': self.weight_momentum,
            'confidence_momentum': self.confidence_momentum
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(state, f, ensure_ascii=False, indent=2)
    
    def load_state(self, filepath: str):
        """加载优化器状态"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                state = json.load(f)
            
            self.current_weights = state.get('current_weights', self.base_weights.copy())
            self.current_confidences = state.get('current_confidences', self.base_confidences.copy())
            self.evidence_performance = state.get('evidence_performance', {})
            self.weight_momentum = state.get('weight_momentum', {key: 0.0 for key in self.base_weights.keys()})
            self.confidence_momentum = state.get('confidence_momentum', {key: 0.0 for key in self.base_confidences.keys()})
            
            # 恢复预测历史
            history_data = state.get('prediction_history', [])
            self.prediction_history = deque(history_data, maxlen=20)
            
        except FileNotFoundError:
            pass  # 使用默认配置
        except Exception as e:
            print(f"加载自适应状态失败: {e}")
