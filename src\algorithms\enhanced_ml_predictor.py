"""
增强ML比值预测器
使用更强的算法和优化的特征工程
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
import warnings
warnings.filterwarnings('ignore')

try:
    import xgboost as xgb
    HAS_XGBOOST = True
except ImportError:
    HAS_XGBOOST = False

try:
    import lightgbm as lgb
    HAS_LIGHTGBM = True
except ImportError:
    HAS_LIGHTGBM = False

from sklearn.ensemble import RandomForestClassifier, VotingClassifier
from sklearn.model_selection import cross_val_score, StratifiedKFold
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.metrics import accuracy_score, classification_report
from .enhanced_feature_engineering import EnhancedFeatureEngineering

class EnhancedMLPredictor:
    """增强ML比值预测器"""
    
    def __init__(self, model_type: str = 'xgboost', lookback_periods: int = 30):
        """
        初始化增强ML预测器
        
        Args:
            model_type: 模型类型 ('xgboost', 'lightgbm', 'random_forest', 'ensemble')
            lookback_periods: 回看期数
        """
        self.model_type = model_type
        self.lookback_periods = lookback_periods
        self.feature_engineer = EnhancedFeatureEngineering(lookback_periods)
        
        # 模型字典
        self.models = {}
        self.label_encoders = {}
        self.feature_scalers = {}
        self.feature_importance = {}
        self.is_trained = False
        
        # 初始化标签编码器
        self._init_label_encoders()
        
    def _init_label_encoders(self):
        """初始化标签编码器"""
        self.label_encoders = {
            'red_odd_even': LabelEncoder(),
            'red_size': LabelEncoder(),
            'blue_size': LabelEncoder()
        }
        
        # 预定义可能的比值
        ratio_values = {
            'red_odd_even': ['0:5', '1:4', '2:3', '3:2', '4:1', '5:0'],
            'red_size': ['0:5', '1:4', '2:3', '3:2', '4:1', '5:0'],
            'blue_size': ['0:2', '1:1', '2:0']
        }
        
        for target, encoder in self.label_encoders.items():
            encoder.fit(ratio_values[target])
    
    def _create_model(self, target: str) -> Any:
        """
        创建模型
        
        Args:
            target: 目标变量名
            
        Returns:
            模型实例
        """
        if self.model_type == 'xgboost' and HAS_XGBOOST:
            return xgb.XGBClassifier(
                n_estimators=200,
                max_depth=6,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42,
                eval_metric='mlogloss'
            )
        elif self.model_type == 'lightgbm' and HAS_LIGHTGBM:
            return lgb.LGBMClassifier(
                n_estimators=200,
                max_depth=6,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42,
                verbose=-1
            )
        elif self.model_type == 'ensemble':
            models = []
            
            # 随机森林
            rf = RandomForestClassifier(
                n_estimators=100,
                max_depth=10,
                random_state=42
            )
            models.append(('rf', rf))
            
            # XGBoost (如果可用)
            if HAS_XGBOOST:
                xgb_model = xgb.XGBClassifier(
                    n_estimators=100,
                    max_depth=6,
                    learning_rate=0.1,
                    random_state=42,
                    eval_metric='mlogloss'
                )
                models.append(('xgb', xgb_model))
            
            # LightGBM (如果可用)
            if HAS_LIGHTGBM:
                lgb_model = lgb.LGBMClassifier(
                    n_estimators=100,
                    max_depth=6,
                    learning_rate=0.1,
                    random_state=42,
                    verbose=-1
                )
                models.append(('lgb', lgb_model))
            
            return VotingClassifier(estimators=models, voting='soft')
        else:
            # 默认使用随机森林
            return RandomForestClassifier(
                n_estimators=200,
                max_depth=10,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42
            )
    
    def train(self, data: pd.DataFrame, min_history: int = 50) -> Dict[str, Any]:
        """
        训练模型
        
        Args:
            data: 训练数据
            min_history: 最小历史数据量
            
        Returns:
            训练结果
        """
        print(f"[循环] 开始训练增强ML比值预测器 (模型类型: {self.model_type})")
        
        if len(data) < min_history:
            raise ValueError(f"训练数据不足，需要至少 {min_history} 期数据")
        
        # 特征工程
        enhanced_data = self.feature_engineer.create_enhanced_features(data)
        feature_names = self.feature_engineer.get_feature_names(enhanced_data)
        
        print(f"[数据] 训练数据规模: {len(enhanced_data)} 样本, {len(feature_names)} 特征")
        
        # 准备训练数据
        X = enhanced_data[feature_names].fillna(0)
        
        # 标准化特征
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        self.feature_scalers['scaler'] = scaler
        
        # 训练结果
        training_results = {}
        
        # 为每个目标变量训练模型
        targets = {
            'red_odd_even': 'red_odd_even_ratio',
            'red_size': 'red_size_ratio',
            'blue_size': 'blue_size_ratio'
        }
        
        for target_name, target_col in targets.items():
            print(f"[精准] 训练 {target_name} 预测模型...")
            
            # 准备目标变量
            y = enhanced_data[target_col].fillna('2:3')  # 默认值
            y_encoded = self.label_encoders[target_name].transform(y)
            
            # 创建模型
            model = self._create_model(target_name)
            
            # 交叉验证
            cv_scores = cross_val_score(
                model, X_scaled, y_encoded, 
                cv=StratifiedKFold(n_splits=5, shuffle=True, random_state=42),
                scoring='accuracy'
            )
            
            # 训练最终模型
            model.fit(X_scaled, y_encoded)
            
            # 训练集准确率
            train_pred = model.predict(X_scaled)
            train_accuracy = accuracy_score(y_encoded, train_pred)
            
            # 保存模型
            self.models[target_name] = model
            
            # 特征重要性
            if hasattr(model, 'feature_importances_'):
                importance = model.feature_importances_
            elif hasattr(model, 'estimators_') and hasattr(model.estimators_[0], 'feature_importances_'):
                # 对于集成模型，取平均重要性
                importance = np.mean([est.feature_importances_ for name, est in model.estimators_], axis=0)
            else:
                importance = np.zeros(len(feature_names))
            
            self.feature_importance[target_name] = dict(zip(feature_names, importance))
            
            # 记录结果
            training_results[target_name] = {
                'cv_mean': cv_scores.mean(),
                'cv_std': cv_scores.std(),
                'train_accuracy': train_accuracy,
                'feature_importance': self.feature_importance[target_name]
            }
            
            print(f"[成功] {target_name} - CV准确率: {cv_scores.mean():.3f} (±{cv_scores.std():.3f})")
        
        self.is_trained = True
        self.feature_names = feature_names
        
        return training_results
    
    def predict_ratios(self, data: pd.DataFrame, target_index: int) -> Dict[str, Any]:
        """
        预测比值
        
        Args:
            data: 历史数据
            target_index: 目标预测位置
            
        Returns:
            预测结果
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        # 特征工程
        enhanced_data = self.feature_engineer.create_enhanced_features(data)
        
        # 获取预测样本
        if target_index >= len(enhanced_data):
            # 使用最后一行数据
            X = enhanced_data[self.feature_names].iloc[-1:].fillna(0)
        else:
            X = enhanced_data[self.feature_names].iloc[target_index:target_index+1].fillna(0)
        
        # 标准化
        X_scaled = self.feature_scalers['scaler'].transform(X)
        
        # 预测
        predictions = {}
        confidences = {}
        
        for target_name, model in self.models.items():
            # 预测概率
            pred_proba = model.predict_proba(X_scaled)[0]
            
            # 获取最高概率的类别
            pred_class_idx = np.argmax(pred_proba)
            confidence = pred_proba[pred_class_idx]
            
            # 解码预测结果
            pred_ratio = self.label_encoders[target_name].inverse_transform([pred_class_idx])[0]
            
            predictions[target_name] = pred_ratio
            confidences[target_name] = confidence
        
        return {
            'predictions': predictions,
            'confidences': confidences,
            'features_used': len(self.feature_names),
            'model_type': self.model_type
        }
    
    def get_feature_importance(self, target: str, top_k: int = 10) -> Dict[str, float]:
        """
        获取特征重要性
        
        Args:
            target: 目标变量
            top_k: 返回前k个重要特征
            
        Returns:
            特征重要性字典
        """
        if target not in self.feature_importance:
            return {}
        
        importance = self.feature_importance[target]
        sorted_importance = sorted(importance.items(), key=lambda x: x[1], reverse=True)
        
        return dict(sorted_importance[:top_k])
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            'model_type': self.model_type,
            'is_trained': self.is_trained,
            'feature_count': len(self.feature_names) if hasattr(self, 'feature_names') else 0,
            'has_xgboost': HAS_XGBOOST,
            'has_lightgbm': HAS_LIGHTGBM,
            'targets': list(self.models.keys()) if self.is_trained else []
        }
