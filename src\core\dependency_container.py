"""
依赖注入容器
管理系统中所有组件的依赖关系，实现控制反转
"""

from typing import Dict, Type, Any, Optional, Callable
from abc import ABC, abstractmethod
import inspect
from functools import wraps

from .interfaces import IPredictor, IKillNumberPredictor


class DependencyContainer:
    """依赖注入容器"""

    def __init__(self):
        self._services: Dict[str, Any] = {}
        self._factories: Dict[str, Callable] = {}
        self._singletons: Dict[str, Any] = {}
        self._interfaces: Dict[Type, str] = {}

    def register_singleton(
        self, interface: Type, implementation: Type, name: str = None
    ) -> None:
        """
        注册单例服务

        Args:
            interface: 接口类型
            implementation: 实现类型
            name: 服务名称，默认使用实现类名
        """
        service_name = name or implementation.__name__
        self._services[service_name] = implementation
        self._interfaces[interface] = service_name

    def register_transient(
        self, interface: Type, factory: Callable, name: str = None
    ) -> None:
        """
        注册瞬态服务（每次调用都创建新实例）

        Args:
            interface: 接口类型
            factory: 工厂函数
            name: 服务名称
        """
        service_name = name or factory.__name__
        self._factories[service_name] = factory
        self._interfaces[interface] = service_name

    def register_instance(
        self, interface: Type, instance: Any, name: str = None
    ) -> None:
        """
        注册实例

        Args:
            interface: 接口类型
            instance: 实例对象
            name: 服务名称
        """
        service_name = name or instance.__class__.__name__
        self._singletons[service_name] = instance
        self._interfaces[interface] = service_name

    def resolve(self, interface: Type, name: str = None) -> Any:
        """
        解析服务

        Args:
            interface: 接口类型
            name: 服务名称

        Returns:
            服务实例
        """
        service_name = name or self._interfaces.get(interface)

        if not service_name:
            raise ValueError(f"未找到接口 {interface} 的注册服务")

        # 检查单例缓存
        if service_name in self._singletons:
            return self._singletons[service_name]

        # 检查工厂方法
        if service_name in self._factories:
            return self._factories[service_name]()

        # 检查服务类型
        if service_name in self._services:
            service_class = self._services[service_name]
            instance = self._create_instance(service_class)

            # 缓存单例
            self._singletons[service_name] = instance
            return instance

        raise ValueError(f"未找到服务: {service_name}")

    def resolve_by_name(self, name: str) -> Any:
        """
        通过名称解析服务

        Args:
            name: 服务名称

        Returns:
            服务实例
        """
        # 检查单例缓存
        if name in self._singletons:
            return self._singletons[name]

        # 检查工厂方法
        if name in self._factories:
            return self._factories[name]()

        # 检查服务类型
        if name in self._services:
            service_class = self._services[name]
            instance = self._create_instance(service_class)

            # 缓存单例
            self._singletons[name] = instance
            return instance

        raise ValueError(f"未找到服务: {name}")

    def _create_instance(self, service_class: Type) -> Any:
        """
        创建服务实例，支持构造函数依赖注入

        Args:
            service_class: 服务类型

        Returns:
            服务实例
        """
        # 获取构造函数签名
        signature = inspect.signature(service_class.__init__)
        parameters = signature.parameters

        # 准备构造函数参数
        kwargs = {}
        for param_name, param in parameters.items():
            if param_name == "self":
                continue

            # 尝试通过类型注解解析依赖
            if param.annotation != inspect.Parameter.empty:
                try:
                    dependency = self.resolve(param.annotation)
                    kwargs[param_name] = dependency
                except ValueError:
                    # 如果无法解析依赖，使用默认值
                    if param.default != inspect.Parameter.empty:
                        kwargs[param_name] = param.default

        return service_class(**kwargs)

    def get_registered_services(self) -> Dict[str, str]:
        """获取已注册的服务列表"""
        services = {}

        for name, service_class in self._services.items():
            services[name] = f"Singleton: {service_class.__name__}"

        for name, factory in self._factories.items():
            services[name] = f"Transient: {factory.__name__}"

        for name, instance in self._singletons.items():
            services[name] = f"Instance: {instance.__class__.__name__}"

        return services

    def clear(self) -> None:
        """清空容器"""
        self._services.clear()
        self._factories.clear()
        self._singletons.clear()
        self._interfaces.clear()


# 全局容器实例
_container = DependencyContainer()


def get_container() -> DependencyContainer:
    """获取全局依赖容器"""
    return _container


def inject(interface: Type = None, name: str = None):
    """
    依赖注入装饰器

    Args:
        interface: 接口类型
        name: 服务名称
    """

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 获取函数签名
            signature = inspect.signature(func)
            parameters = signature.parameters

            # 注入依赖
            for param_name, param in parameters.items():
                if param_name not in kwargs:
                    param_interface = interface or param.annotation
                    if param_interface != inspect.Parameter.empty:
                        try:
                            dependency = _container.resolve(param_interface, name)
                            kwargs[param_name] = dependency
                        except ValueError:
                            pass

            return func(*args, **kwargs)

        return wrapper

    return decorator


class ServiceLocator:
    """服务定位器模式实现"""

    @staticmethod
    def get_predictor(predictor_type: str) -> IPredictor:
        """
        获取预测器

        Args:
            predictor_type: 预测器类型

        Returns:
            预测器实例
        """
        return _container.resolve_by_name(predictor_type)

    @staticmethod
    def get_kill_predictor() -> IKillNumberPredictor:
        """获取杀号预测器"""
        return _container.resolve(IKillNumberPredictor)

    @staticmethod
    def get_number_predictor() -> IPredictor:
        """获取号码预测器"""
        return _container.resolve(IPredictor)

    @staticmethod
    def get_probability_predictor() -> IPredictor:
        """获取概率预测器"""
        return _container.resolve(IPredictor)
