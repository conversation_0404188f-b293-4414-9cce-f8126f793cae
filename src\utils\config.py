#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单配置类，用于深度学习选择器
"""

import os
from pathlib import Path


class Config:
    """简单配置类"""
    
    def __init__(self):
        """初始化配置"""
        # 深度学习相关配置
        self.dl_epochs = int(os.getenv('DL_EPOCHS', '20'))
        self.dl_batch_size = int(os.getenv('DL_BATCH_SIZE', '32'))
        self.dl_learning_rate = float(os.getenv('DL_LEARNING_RATE', '0.001'))
        
        # GPU配置
        self.use_gpu = os.getenv('USE_GPU', 'true').lower() == 'true'
        self.use_mixed_precision = os.getenv('USE_MIXED_PRECISION', 'true').lower() == 'true'
        
        # 数据配置
        self.sequence_length = int(os.getenv('SEQUENCE_LENGTH', '20'))
        self.train_test_split = float(os.getenv('TRAIN_TEST_SPLIT', '0.8'))
        
        # 数据分割比例
        self.train_ratio = float(os.getenv('TRAIN_RATIO', '0.7'))
        self.validation_ratio = float(os.getenv('VALIDATION_RATIO', '0.2'))
        self.val_ratio = self.validation_ratio  # 别名
        self.test_ratio = float(os.getenv('TEST_RATIO', '0.1'))
        
        # 模型保存路径
        self.model_save_dir = Path(os.getenv('MODEL_SAVE_DIR', 'models/deep_learning'))
        self.model_save_dir.mkdir(parents=True, exist_ok=True)
        
        # 日志配置
        self.log_level = os.getenv('LOG_LEVEL', 'INFO')
        
        # 彩票相关配置
        self.red_ball_count = 5
        self.blue_ball_count = 2
        self.red_ball_range = (1, 35)
        self.blue_ball_range = (1, 12)
        
        # 特征工程配置
        self.feature_window = int(os.getenv('FEATURE_WINDOW', '10'))
        self.target_window = int(os.getenv('TARGET_WINDOW', '1'))
        
        # 模型配置
        self.early_stopping_patience = int(os.getenv('EARLY_STOPPING_PATIENCE', '10'))
        self.min_delta = float(os.getenv('MIN_DELTA', '0.001'))
        
    def get(self, key, default=None):
        """获取配置值"""
        return getattr(self, key, default)
    
    def set(self, key, value):
        """设置配置值"""
        setattr(self, key, value)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'dl_epochs': self.dl_epochs,
            'dl_batch_size': self.dl_batch_size,
            'dl_learning_rate': self.dl_learning_rate,
            'use_gpu': self.use_gpu,
            'use_mixed_precision': self.use_mixed_precision,
            'sequence_length': self.sequence_length,
            'train_test_split': self.train_test_split,
            'model_save_dir': str(self.model_save_dir),
            'log_level': self.log_level,
            'red_ball_count': self.red_ball_count,
            'blue_ball_count': self.blue_ball_count,
            'red_ball_range': self.red_ball_range,
            'blue_ball_range': self.blue_ball_range
        }
    
    def __repr__(self):
        return f"Config({self.to_dict()})"


# 创建默认配置实例
default_config = Config()