#!/usr/bin/env python3
"""
性能优化测试脚本
测试增强的多样性杀号算法和自适应参数优化
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def main():
    """主测试函数"""
    try:
        print("[启动] 启动性能优化测试...")
        print("=" * 60)
        
        # 导入系统
        from src.apps.advanced_probabilistic_system import AdvancedProbabilisticSystem
        
        # 创建系统实例
        system = AdvancedProbabilisticSystem()
        
        # 加载数据
        print("[数据] 加载数据...")
        if not system.load_data():
            print("[失败] 数据加载失败")
            return 1
        
        print(f"[成功] 数据加载成功，共 {len(system.data)} 期数据")
        
        # 运行性能优化
        print("\n[工具] 开始性能优化...")
        optimization_result = system.run_performance_optimization(optimization_periods=30)
        
        if not optimization_result['success']:
            print(f"[失败] 性能优化失败: {optimization_result.get('error')}")
            return 1
        
        # 测试优化后的杀号效果
        print("\n[精准] 测试优化后的杀号效果...")
        test_periods = ['25070', '25069', '25068', '25067', '25066']
        
        for period in test_periods:
            print(f"\n测试期号: {period}")
            result = system.predict_kills_by_period(period, red_target_count=13, blue_target_count=5)
            
            if result['success']:
                print(f"   红球杀号: {sorted(result['red_kills'])}")
                print(f"   蓝球杀号: {result['blue_kills']}")
                print(f"   使用增强系统: {result.get('enhanced_system_used', False)}")
            else:
                print(f"   [失败] 预测失败: {result.get('error')}")
        
        # 显示多样性分析
        print("\n[提升] 多样性分析...")
        analyze_diversity(system, test_periods)
        
        # 性能对比测试
        print("\n⚖️  性能对比测试...")
        compare_performance(system)
        
        print("\n[成功] 性能优化测试完成!")
        return 0
        
    except Exception as e:
        print(f"[失败] 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

def analyze_diversity(system, test_periods):
    """分析杀号多样性"""
    try:
        all_red_kills = []
        all_blue_kills = []
        
        for period in test_periods:
            result = system.predict_kills_by_period(period, red_target_count=13, blue_target_count=5)
            if result['success']:
                all_red_kills.extend(result['red_kills'])
                all_blue_kills.extend(result['blue_kills'])
        
        # 分析红球多样性
        red_zones = [0, 0, 0]  # 小中大
        for ball in all_red_kills:
            if 1 <= ball <= 12:
                red_zones[0] += 1
            elif 13 <= ball <= 24:
                red_zones[1] += 1
            elif 25 <= ball <= 35:
                red_zones[2] += 1
        
        # 分析蓝球多样性
        blue_zones = [0, 0, 0]  # 小中大
        for ball in all_blue_kills:
            if 1 <= ball <= 4:
                blue_zones[0] += 1
            elif 5 <= ball <= 8:
                blue_zones[1] += 1
            elif 9 <= ball <= 12:
                blue_zones[2] += 1
        
        print(f"   红球区间分布: 小区(1-12): {red_zones[0]}, 中区(13-24): {red_zones[1]}, 大区(25-35): {red_zones[2]}")
        print(f"   蓝球区间分布: 小区(1-4): {blue_zones[0]}, 中区(5-8): {blue_zones[1]}, 大区(9-12): {blue_zones[2]}")
        
        # 计算重复率
        from collections import Counter
        red_counter = Counter(all_red_kills)
        blue_counter = Counter(all_blue_kills)
        
        red_repeats = sum(1 for count in red_counter.values() if count > 1)
        blue_repeats = sum(1 for count in blue_counter.values() if count > 1)
        
        print(f"   红球重复数字: {red_repeats}/{len(set(all_red_kills))} ({red_repeats/len(set(all_red_kills))*100:.1f}%)")
        print(f"   蓝球重复数字: {blue_repeats}/{len(set(all_blue_kills))} ({blue_repeats/len(set(all_blue_kills))*100:.1f}%)")
        
    except Exception as e:
        print(f"   [失败] 多样性分析失败: {e}")

def compare_performance(system):
    """性能对比测试"""
    try:
        # 测试原始系统 vs 增强系统
        test_period = '25070'
        
        # 准备数据
        train_data, period_data = system._prepare_data_for_period(test_period)
        if train_data is None:
            print("   [失败] 无法准备测试数据")
            return
        
        system.data = train_data
        system.initialize_system()
        
        # 原始系统预测
        print("   原始系统预测:")
        original_red = system.predict_red_kills(period_data, 13)
        original_blue = system.predict_blue_kills(period_data, 5)
        print(f"     红球: {sorted(original_red)}")
        print(f"     蓝球: {original_blue}")
        
        # 增强系统预测
        print("   增强系统预测:")
        enhanced_data = []
        for _, row in train_data.iterrows():
            enhanced_data.append([
                row['期号'],
                row['红球1'], row['红球2'], row['红球3'], row['红球4'], row['红球5'],
                row['蓝球1'], row['蓝球2']
            ])
        
        enhanced_red = system.enhanced_diversity_system.predict_enhanced_kills(
            enhanced_data, int(test_period), 13, 'red'
        )
        enhanced_blue = system.enhanced_diversity_system.predict_enhanced_kills(
            enhanced_data, int(test_period), 5, 'blue'
        )
        print(f"     红球: {sorted(enhanced_red)}")
        print(f"     蓝球: {enhanced_blue}")
        
        # 分析差异
        red_diff = set(enhanced_red) - set(original_red)
        blue_diff = set(enhanced_blue) - set(original_blue)
        
        print(f"   差异分析:")
        print(f"     红球差异: {len(red_diff)}/{len(enhanced_red)} ({len(red_diff)/len(enhanced_red)*100:.1f}%)")
        print(f"     蓝球差异: {len(blue_diff)}/{len(enhanced_blue)} ({len(blue_diff)/len(enhanced_blue)*100:.1f}%)")
        
    except Exception as e:
        print(f"   [失败] 性能对比失败: {e}")

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)