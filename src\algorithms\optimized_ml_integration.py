"""
优化的ML比值预测集成模块
使用增强特征工程和更强的算法
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
import os
import warnings
warnings.filterwarnings('ignore')

from .enhanced_ml_predictor import EnhancedMLPredictor
from .time_series_validator import TimeSeriesValidator

class OptimizedMLIntegration:
    """优化的ML比值预测集成器"""
    
    def __init__(self, model_type: str = 'xgboost', lookback_periods: int = 30):
        """
        初始化优化ML比值预测集成器
        
        Args:
            model_type: 模型类型 ('xgboost', 'lightgbm', 'random_forest', 'ensemble')
            lookback_periods: 回看期数
        """
        self.model_type = model_type
        self.lookback_periods = lookback_periods
        self.predictor = EnhancedMLPredictor(model_type, lookback_periods)
        self.validator = TimeSeriesValidator()
        self.is_trained = False
        self.performance_metrics = {}
        self.feature_importance = {}
        self.prediction_confidence_threshold = 0.6  # 置信度阈值
        self.ml_weight = 0.7  # ML预测的权重
        
    def train_and_validate(self, data: pd.DataFrame, validation_periods: int = 10) -> Dict[str, Any]:
        """
        训练并验证模型
        
        Args:
            data: 训练数据
            validation_periods: 验证期数
            
        Returns:
            训练和验证结果
        """
        print(f"🤖 开始训练和验证优化ML比值预测器...")
        
        # 进行时间序列验证
        validation_results = self.validator.validate_time_series_split(
            data, 
            self.predictor, 
            validation_periods=validation_periods,
            min_train_periods=50
        )
        
        # 使用全部数据训练最终模型
        print(f"[循环] 使用全部数据训练最终模型...")
        final_performance = self.predictor.train(data, min_history=30)
        
        self.is_trained = True
        self.performance_metrics = {
            'validation_results': validation_results,
            'final_training_performance': final_performance
        }
        
        # 获取特征重要性
        self._extract_feature_importance()
        
        # 打印性能摘要
        self._print_performance_summary()
        
        return self.performance_metrics
    
    def predict_next_period_ratios(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        预测下一期的比值
        
        Args:
            data: 历史数据
            
        Returns:
            预测结果
        """
        if not self.is_trained:
            print("[警告] 模型尚未训练，正在使用当前数据进行训练...")
            self.predictor.train(data, min_history=30)
            self.is_trained = True
        
        # 预测下一期
        prediction_result = self.predictor.predict_ratios(data, len(data))
        
        # 计算整体置信度
        confidences = prediction_result['confidences']
        avg_confidence = np.mean(list(confidences.values()))
        
        # 根据置信度调整预测
        adjusted_predictions = self._adjust_predictions_by_confidence(
            prediction_result['predictions'], 
            confidences
        )
        
        # 格式化输出
        formatted_result = {
            'red_odd_even_ratio': adjusted_predictions['red_odd_even'],
            'red_size_ratio': adjusted_predictions['red_size'],
            'blue_size_ratio': adjusted_predictions['blue_size'],
            'confidences': confidences,
            'average_confidence': avg_confidence,
            'model_type': self.model_type,
            'features_used': prediction_result['features_used'],
            'ml_weight': self.ml_weight,
            'high_confidence': avg_confidence >= self.prediction_confidence_threshold
        }
        
        return formatted_result
    
    def _adjust_predictions_by_confidence(self, predictions: Dict[str, str], confidences: Dict[str, float]) -> Dict[str, str]:
        """
        根据置信度调整预测结果
        
        Args:
            predictions: 原始预测结果
            confidences: 置信度
            
        Returns:
            调整后的预测结果
        """
        adjusted = predictions.copy()
        
        # 如果置信度太低，使用历史频率最高的比值
        fallback_ratios = {
            'red_odd_even': '3:2',  # 历史上最常见的红球奇偶比
            'red_size': '2:3',      # 历史上最常见的红球大小比
            'blue_size': '1:1'      # 历史上最常见的蓝球大小比
        }
        
        for target, confidence in confidences.items():
            if confidence < self.prediction_confidence_threshold:
                print(f"[警告] {target} 置信度较低 ({confidence:.3f})，使用备选预测")
                adjusted[target] = fallback_ratios[target]
        
        return adjusted
    
    def _extract_feature_importance(self):
        """提取特征重要性"""
        if hasattr(self.predictor, 'feature_importance'):
            self.feature_importance = self.predictor.feature_importance
    
    def get_feature_importance_summary(self, top_k: int = 10) -> Dict[str, Dict[str, float]]:
        """
        获取特征重要性摘要
        
        Args:
            top_k: 返回前k个重要特征
            
        Returns:
            特征重要性摘要
        """
        summary = {}
        for target in ['red_odd_even', 'red_size', 'blue_size']:
            summary[target] = self.predictor.get_feature_importance(target, top_k)
        return summary
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        if not self.performance_metrics:
            return {}
        
        validation_results = self.performance_metrics.get('validation_results', {})
        
        # 计算平均准确率
        accuracies = []
        for result in validation_results.get('results', []):
            for target in ['red_odd_even', 'red_size', 'blue_size']:
                if target in result.get('accuracies', {}):
                    accuracies.append(result['accuracies'][target])
        
        avg_accuracy = np.mean(accuracies) if accuracies else 0
        
        return {
            'model_type': self.model_type,
            'average_accuracy': avg_accuracy,
            'validation_periods': len(validation_results.get('results', [])),
            'feature_count': self.predictor.get_model_info().get('feature_count', 0),
            'is_trained': self.is_trained,
            'ml_weight': self.ml_weight,
            'confidence_threshold': self.prediction_confidence_threshold
        }
    
    def _print_performance_summary(self):
        """打印性能摘要"""
        if not self.performance_metrics:
            return
        
        validation_results = self.performance_metrics.get('validation_results', {})
        final_performance = self.performance_metrics.get('final_training_performance', {})
        
        print("\n" + "="*60)
        print("[精准] 优化ML比值预测器性能摘要")
        print("="*60)
        
        # 验证性能
        if validation_results:
            results = validation_results.get('results', [])
            if results:
                print(f"[数据] 验证性能 (模型: {self.model_type}):")
                
                # 计算各目标的平均准确率
                target_accuracies = {'red_odd_even': [], 'red_size': [], 'blue_size': []}
                
                for result in results:
                    for target, acc_list in target_accuracies.items():
                        if target in result.get('accuracies', {}):
                            acc_list.append(result['accuracies'][target])
                
                for target, acc_list in target_accuracies.items():
                    if acc_list:
                        avg_acc = np.mean(acc_list)
                        target_name = {
                            'red_odd_even': '红球奇偶比',
                            'red_size': '红球大小比',
                            'blue_size': '蓝球大小比'
                        }[target]
                        print(f"  {target_name}准确率: {avg_acc:.1%}")
                
                # 总体准确率
                all_accuracies = []
                for acc_list in target_accuracies.values():
                    all_accuracies.extend(acc_list)
                
                if all_accuracies:
                    overall_acc = np.mean(all_accuracies)
                    print(f"  平均准确率: {overall_acc:.1%}")
        
        # 最终训练性能
        if final_performance:
            print(f"\n[循环] 最终训练性能:")
            for target, metrics in final_performance.items():
                cv_mean = metrics.get('cv_mean', 0)
                train_acc = metrics.get('train_accuracy', 0)
                target_name = {
                    'red_odd_even': '红球奇偶比',
                    'red_size': '红球大小比',
                    'blue_size': '蓝球大小比'
                }.get(target, target)
                print(f"  {target_name}: CV={cv_mean:.3f}, 训练={train_acc:.3f}")
        
        # 模型信息
        model_info = self.predictor.get_model_info()
        print(f"\n[提升] 模型配置:")
        print(f"  模型类型: {model_info['model_type']}")
        print(f"  特征数量: {model_info['feature_count']}")
        print(f"  ML权重: {self.ml_weight}")
        print(f"  置信度阈值: {self.prediction_confidence_threshold}")
        
        # 总体评估
        performance = self.get_performance_summary()
        avg_acc = performance.get('average_accuracy', 0)
        
        if avg_acc >= 0.4:
            status = "🟢 表现良好"
        elif avg_acc >= 0.3:
            status = "🟡 需要改进"
        else:
            status = "🔴 需要优化"
        
        print(f"\n[提升] 总体评估: {status} (平均准确率: {avg_acc:.1%})")
        print("="*60)
    
    def set_ml_weight(self, weight: float):
        """设置ML预测权重"""
        self.ml_weight = max(0.0, min(1.0, weight))
        print(f"[工具] ML预测权重已设置为: {self.ml_weight}")
    
    def set_confidence_threshold(self, threshold: float):
        """设置置信度阈值"""
        self.prediction_confidence_threshold = max(0.0, min(1.0, threshold))
        print(f"[工具] 置信度阈值已设置为: {self.prediction_confidence_threshold}")
    
    def is_high_confidence_prediction(self, prediction_result: Dict[str, Any]) -> bool:
        """判断是否为高置信度预测"""
        return prediction_result.get('high_confidence', False)
    
    def get_prediction_explanation(self, prediction_result: Dict[str, Any]) -> str:
        """获取预测解释"""
        avg_conf = prediction_result.get('average_confidence', 0)
        model_type = prediction_result.get('model_type', 'unknown')
        features_used = prediction_result.get('features_used', 0)
        
        explanation = f"使用{model_type}模型，基于{features_used}个特征进行预测，"
        explanation += f"平均置信度{avg_conf:.1%}"
        
        if avg_conf >= self.prediction_confidence_threshold:
            explanation += "，预测可信度较高"
        else:
            explanation += "，预测可信度较低，已使用备选策略"
        
        return explanation
