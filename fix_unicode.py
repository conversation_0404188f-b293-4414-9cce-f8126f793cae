#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面修复项目中的Unicode字符编码问题
解决'gbk' codec编码错误
"""

import os
import re
import glob
from pathlib import Path

def fix_unicode_in_file(filepath):
    """修复文件中的Unicode字符"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
    except UnicodeDecodeError:
        # 尝试其他编码
        try:
            with open(filepath, 'r', encoding='gbk') as f:
                content = f.read()
        except UnicodeDecodeError:
            print(f"跳过无法读取的文件: {filepath}")
            return False

    # 替换所有emoji和特殊Unicode字符
    replacements = {
        '[启动]': '[启动]',
        '[精准]': '[目标]',
        '[成功]': '[成功]',
        '[警告]': '[警告]',
        '[失败]': '[失败]',
        '[测试]': '[测试]',
        '[数据]': '[数据]',
        '[优秀]': '[优秀]',
        '[列表]': '[列表]',
        '[预测]': '[预测]',
        '[循环]': '[循环]',
        '[推荐]': '[推荐]',
        '[杀号]': '[杀号]',
        '[提示]': '[提示]',
        '[工具]': '[工具]',
        '[提升]': '[提升]',
        '[随机]': '[随机]',
        '[集成]': '[系统]',
        '[特色]': '[特色]',
        '[热门]': '[热门]',
        '[精选]': '[精选]',
        '[优化]': '[优化]',
        '[模式]': '[模式]',
        '[集成]': '[集成]',
        '[精准]': '[精准]',
        '[成果]': '[成果]',
        '[完成]': '[完成]',
    }

    original_content = content
    for old, new in replacements.items():
        content = content.replace(old, new)

    # 如果内容有变化，写回文件
    if content != original_content:
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"已修复 {filepath} 中的Unicode字符")
            return True
        except Exception as e:
            print(f"写入文件失败 {filepath}: {e}")
            return False

    return False

def fix_all_python_files():
    """修复所有Python文件中的Unicode字符"""
    project_root = Path(__file__).parent
    python_files = []

    # 查找所有Python文件
    for pattern in ['**/*.py', '**/*.pyx']:
        python_files.extend(project_root.glob(pattern))

    fixed_count = 0
    total_count = len(python_files)

    print(f"开始修复 {total_count} 个Python文件...")

    for file_path in python_files:
        # 跳过一些特殊目录
        if any(skip in str(file_path) for skip in ['__pycache__', '.git', 'venv', 'env']):
            continue

        if fix_unicode_in_file(file_path):
            fixed_count += 1

    print(f"修复完成: {fixed_count}/{total_count} 个文件被修改")
    return fixed_count

if __name__ == "__main__":
    fix_all_python_files()