# 选号系统使用说明

## 系统概述

选号系统是一个先进的彩票号码选择工具，集成了深度学习和传统机器学习方法，通过对比分析为用户提供最优的选号策略。

## 核心功能

### 1. 深度学习选号
- **LSTM神经网络**: 利用长短期记忆网络捕捉时序模式
- **Transformer模型**: 使用注意力机制分析号码关联性
- **多任务神经网络**: 同时预测红球和蓝球的综合模型

### 2. 传统机器学习选号
- **随机森林**: 集成学习方法，提供稳定预测
- **梯度提升**: 逐步优化的预测算法
- **极端随机树**: 增强模型多样性
- **贝叶斯选号器**: 基于概率统计的选号方法
- **马尔可夫链**: 基于状态转移的序列预测

### 3. 集成选号
- 结合深度学习和传统ML的优势
- 智能权重分配
- 多模型投票机制

### 4. 性能对比分析
- 命中率统计
- ROI计算
- 稳定性评估
- 统计显著性检验
- 可视化报告生成

## 使用方法

### 启动系统
```bash
python launcher.py
```

### 选择选号系统
在主菜单中选择 `5. Number Selection System`

### 功能选项

#### 1. 完整对比 (Run Full Comparison)
- 同时运行深度学习和传统ML方法
- 生成详细的性能对比报告
- 推荐最优方法

#### 2. 深度学习选号 (Deep Learning Selection Only)
- 仅使用深度学习方法
- 快速获得AI预测结果
- 显示置信度和预测时间

#### 3. 传统ML选号 (Traditional ML Selection Only)
- 仅使用传统机器学习方法
- 基于统计学习的稳定预测
- 多算法集成结果

#### 4. 集成选号 (Ensemble Selection)
- 结合两种方法的优势
- 最高准确率的综合预测
- 智能融合算法

#### 5. 性能分析 (Performance Analysis)
- 详细的性能评估报告
- 统计分析和显著性检验
- 方法推荐和建议

## 系统架构

### 数据处理流程
1. **数据清洗**: 去除异常和重复数据
2. **特征工程**: 提取35个关键特征
3. **序列构建**: 为深度学习构建时序数据
4. **数据分割**: 训练集、验证集、测试集划分
5. **特征缩放**: 标准化和归一化处理

### 特征类型
- **号码特征**: 基础号码信息
- **统计特征**: 均值、方差、偏度、峰度
- **比例特征**: 奇偶比、大小比
- **区间分布**: 号码在不同区间的分布
- **连号特征**: 连续号码模式
- **历史统计**: 历史出现频率和趋势

### 模型配置
- **序列长度**: 20期历史数据
- **训练轮数**: 深度学习50轮，可配置
- **回测期数**: 默认10期，可调整
- **集成权重**: 动态调整，基于历史性能

## 性能指标

### 评估标准
- **命中率**: 预测号码与实际开奖号码的匹配度
- **ROI**: 投资回报率，基于奖金计算
- **稳定性**: 预测结果的一致性
- **预测时间**: 模型训练和预测的耗时
- **置信度**: 模型对预测结果的信心度

### 统计分析
- **t检验**: 比较两种方法的显著性差异
- **效应大小**: Cohen's d，衡量差异的实际意义
- **Wilcoxon检验**: 非参数统计检验
- **变异系数**: 稳定性量化指标

## 配置选项

### 系统参数
```python
class SelectionConfig:
    sequence_length: int = 20          # 序列长度
    test_size: float = 0.1             # 测试集比例
    validation_size: float = 0.1       # 验证集比例
    random_state: int = 42             # 随机种子
    
    # 深度学习参数
    dl_epochs: int = 50                # 训练轮数
    dl_batch_size: int = 32            # 批次大小
    dl_learning_rate: float = 0.001    # 学习率
    
    # 回测参数
    backtest_periods: int = 10         # 回测期数
    generate_report: bool = True       # 生成报告
```

### 模型参数
- **LSTM**: 128个隐藏单元，0.3的dropout
- **Transformer**: 4个注意力头，128维度
- **随机森林**: 100棵树，最大深度10
- **梯度提升**: 100个估计器，0.1学习率

## 输出结果

### 选号结果格式
```
Deep Learning Selection Results:
Red Balls: [4, 9, 13, 20, 26]
Blue Balls: [4, 8]
Confidence: 85.32%
Prediction Time: 2.45s
```

### 对比分析报告
```
COMPARISON RESULTS
Winner: 深度学习
Deep Learning Hit Rate: 17.14%
Traditional ML Hit Rate: 11.43%
Statistical Significance: No
P-value: 0.2341
Effect Size: 0.156
```

## 注意事项

1. **首次运行**: 需要训练模型，耗时较长
2. **数据依赖**: 需要足够的历史数据支持
3. **硬件要求**: 深度学习需要较好的CPU/GPU
4. **结果解读**: 彩票具有随机性，预测仅供参考
5. **理性投注**: 请根据个人情况合理投注

## 技术支持

如遇问题，请检查：
1. Python环境和依赖包是否正确安装
2. 数据文件是否完整
3. 系统内存是否充足
4. TensorFlow是否正常工作

## 更新日志

- **v1.0**: 初始版本，基础功能实现
- **v1.1**: 增加集成选号功能
- **v1.2**: 优化性能对比分析
- **v1.3**: 添加可视化报告生成

---

*选号系统 - 智能彩票预测的新选择*
