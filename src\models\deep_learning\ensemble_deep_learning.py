"""
深度学习集成器

集成多个深度学习模型的预测结果，包括：
- LSTM预测器
- Transformer预测器
- 多任务神经网络
- 传统机器学习模型

通过智能融合策略提高预测准确率。
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Tuple, Optional, Union
import logging
from dataclasses import dataclass
from enum import Enum

from ...core.interfaces import IStandardPredictor, PredictionResult, PredictionType, BallType
from ...core.base import StandardBasePredictor
from .lstm_predictor import LSTMPredictor, LSTMConfig
from .transformer_predictor import TransformerPredictor, TransformerConfig
from .multi_task_neural import MultiTaskNeuralPredictor, MultiTaskConfig


class EnsembleMethod(Enum):
    """集成方法枚举"""
    VOTING = "voting"              # 投票法
    WEIGHTED_AVERAGE = "weighted"  # 加权平均
    STACKING = "stacking"          # 堆叠法
    DYNAMIC_WEIGHT = "dynamic"     # 动态权重
    BAYESIAN_FUSION = "bayesian"   # 贝叶斯融合


@dataclass
class EnsembleConfig:
    """集成配置参数"""
    # 集成方法
    ensemble_method: EnsembleMethod = EnsembleMethod.WEIGHTED_AVERAGE
    
    # 模型权重
    model_weights: Dict[str, float] = None
    
    # 动态权重参数
    performance_window: int = 10  # 性能评估窗口
    weight_decay: float = 0.9     # 权重衰减因子
    
    # 堆叠法参数
    meta_learner: str = "linear"  # "linear", "rf", "xgb"
    
    # 贝叶斯融合参数
    prior_weight: float = 0.1
    confidence_threshold: float = 0.7
    
    # 模型配置
    lstm_config: Optional[LSTMConfig] = None
    transformer_config: Optional[TransformerConfig] = None
    multi_task_config: Optional[MultiTaskConfig] = None
    
    def __post_init__(self):
        if self.model_weights is None:
            self.model_weights = {
                "lstm": 0.3,
                "transformer": 0.4,
                "multi_task": 0.3
            }


class EnsembleDeepLearning(StandardBasePredictor):
    """深度学习集成器"""
    
    def __init__(self, config: Optional[EnsembleConfig] = None):
        super().__init__("深度学习集成器", PredictionType.DEEP_LEARNING)
        self.config = config or EnsembleConfig()
        
        # 初始化子模型
        self.models = {}
        self._initialize_models()
        
        # 集成相关
        self.meta_learner = None
        self.performance_history = {}
        self.dynamic_weights = {}
        self.is_trained_flag = False
    
    def _initialize_models(self):
        """初始化子模型"""
        try:
            # LSTM预测器
            lstm_config = self.config.lstm_config or LSTMConfig()
            self.models["lstm"] = LSTMPredictor(lstm_config)
            
            # Transformer预测器
            transformer_config = self.config.transformer_config or TransformerConfig()
            self.models["transformer"] = TransformerPredictor(transformer_config)
            
            # 多任务神经网络
            multi_task_config = self.config.multi_task_config or MultiTaskConfig()
            self.models["multi_task"] = MultiTaskNeuralPredictor(multi_task_config)
            
            self.logger.info("深度学习模型初始化完成")
            
        except Exception as e:
            self.logger.error(f"模型初始化失败: {e}")
            raise
    
    def train(self, data: pd.DataFrame, **kwargs) -> Dict[str, Any]:
        """训练所有子模型"""
        try:
            self.logger.info("开始训练深度学习集成模型...")
            
            training_results = {}
            
            # 训练每个子模型
            for model_name, model in self.models.items():
                self.logger.info(f"训练 {model_name} 模型...")
                
                try:
                    if model_name == "multi_task":
                        result = model.train(data, **kwargs)
                    else:
                        # LSTM和Transformer可能需要目标列
                        target_col = kwargs.get('target_col', None)
                        result = model.train(data, target_col)
                    
                    training_results[model_name] = result
                    
                    if "error" not in result:
                        self.logger.info(f"{model_name} 训练成功")
                    else:
                        self.logger.warning(f"{model_name} 训练失败: {result['error']}")
                        
                except Exception as e:
                    self.logger.error(f"{model_name} 训练异常: {e}")
                    training_results[model_name] = {"error": str(e)}
            
            # 训练元学习器（如果使用堆叠法）
            if self.config.ensemble_method == EnsembleMethod.STACKING:
                self._train_meta_learner(data, **kwargs)
            
            # 初始化动态权重
            if self.config.ensemble_method == EnsembleMethod.DYNAMIC_WEIGHT:
                self._initialize_dynamic_weights()
            
            self.is_trained_flag = True
            
            # 统计训练结果
            successful_models = [name for name, result in training_results.items() 
                               if "error" not in result]
            
            self.logger.info(f"集成训练完成 - 成功模型: {len(successful_models)}/{len(self.models)}")
            
            return {
                "method": "ensemble_deep_learning",
                "successful_models": successful_models,
                "training_results": training_results,
                "ensemble_method": self.config.ensemble_method.value
            }
            
        except Exception as e:
            self.logger.error(f"集成训练失败: {e}")
            return {"error": str(e)}
    
    def _train_meta_learner(self, data: pd.DataFrame, **kwargs):
        """训练元学习器（堆叠法）"""
        try:
            from sklearn.linear_model import LinearRegression
            from sklearn.ensemble import RandomForestRegressor
            
            # 收集子模型的预测结果作为元特征
            meta_features = []
            meta_targets = []
            
            # 使用交叉验证生成元特征
            n_samples = len(data)
            fold_size = max(50, n_samples // 5)  # 5折交叉验证
            
            for i in range(fold_size, n_samples - fold_size, fold_size):
                # 训练数据
                train_data = pd.concat([data.iloc[:i], data.iloc[i+fold_size:]])
                
                # 验证数据
                val_data = data.iloc[i:i+fold_size]
                
                # 训练子模型
                fold_predictions = []
                for model_name, model in self.models.items():
                    try:
                        if model_name == "multi_task":
                            model.train(train_data, **kwargs)
                        else:
                            target_col = kwargs.get('target_col', None)
                            model.train(train_data, target_col)
                        
                        # 预测验证集
                        for j in range(len(val_data)):
                            pred_result = model.predict(data, i + j)
                            if pred_result.metadata.get("success", False):
                                # 提取数值预测结果
                                pred_value = self._extract_numeric_prediction(pred_result.metadata.get("predictions", {}))
                                fold_predictions.append(pred_value)
                            else:
                                fold_predictions.append(0.0)  # 默认值
                    
                    except Exception as e:
                        self.logger.warning(f"元学习器训练中 {model_name} 失败: {e}")
                        fold_predictions.extend([0.0] * len(val_data))
                
                if fold_predictions:
                    # 重塑为特征矩阵
                    n_models = len(self.models)
                    n_val_samples = len(val_data)
                    features = np.array(fold_predictions).reshape(n_val_samples, n_models)
                    meta_features.extend(features.tolist())
                    
                    # 目标值（这里需要根据实际任务定义）
                    targets = [1.0] * n_val_samples  # 占位符
                    meta_targets.extend(targets)
            
            if meta_features and meta_targets:
                # 训练元学习器
                if self.config.meta_learner == "linear":
                    self.meta_learner = LinearRegression()
                else:
                    self.meta_learner = RandomForestRegressor(n_estimators=50, random_state=42)
                
                self.meta_learner.fit(meta_features, meta_targets)
                self.logger.info("元学习器训练完成")
            
        except Exception as e:
            self.logger.error(f"元学习器训练失败: {e}")
    
    def _initialize_dynamic_weights(self):
        """初始化动态权重"""
        for model_name in self.models.keys():
            self.dynamic_weights[model_name] = self.config.model_weights.get(model_name, 1.0)
            self.performance_history[model_name] = []
    
    def _extract_numeric_prediction(self, predictions: Dict[str, Any]) -> float:
        """从预测结果中提取数值"""
        # 尝试提取第一个数值预测
        for key, value in predictions.items():
            if isinstance(value, (int, float)):
                return float(value)
            elif isinstance(value, dict) and 'confidence' in value:
                return float(value['confidence'])
            elif isinstance(value, list) and len(value) > 0:
                if isinstance(value[0], (int, float)):
                    return float(value[0])
        
        return 0.5  # 默认值
    
    def predict(self, data: pd.DataFrame, target_index: int, **kwargs) -> PredictionResult:
        """执行集成预测"""
        if not self.is_trained_flag:
            return PredictionResult(
                prediction_type=PredictionType.DEEP_LEARNING,
                ball_type=BallType.RED,
                value=None,
                confidence=0.0,
                metadata={"success": False, "error": "集成模型未训练", "predictions": {}}
            )
        
        try:
            # 收集所有子模型的预测结果
            model_predictions = {}
            model_confidences = {}
            
            for model_name, model in self.models.items():
                try:
                    pred_result = model.predict(data, target_index, **kwargs)
                    
                    if pred_result.metadata.get("success", False):
                        model_predictions[model_name] = pred_result.metadata.get("predictions", {})

                        # 提取置信度
                        confidence = self._extract_confidence(pred_result.metadata.get("predictions", {}))
                        model_confidences[model_name] = confidence
                        
                        # 更新性能历史（用于动态权重）
                        if self.config.ensemble_method == EnsembleMethod.DYNAMIC_WEIGHT:
                            self._update_performance_history(model_name, confidence)
                    else:
                        self.logger.warning(f"{model_name} 预测失败: {pred_result.error}")
                        
                except Exception as e:
                    self.logger.error(f"{model_name} 预测异常: {e}")
            
            if not model_predictions:
                return PredictionResult(
                    prediction_type=PredictionType.DEEP_LEARNING,
                    ball_type=BallType.RED,
                    value=None,
                    confidence=0.0,
                    metadata={"success": False, "error": "所有子模型预测失败", "predictions": {}}
                )
            
            # 执行集成预测
            ensemble_result = self._ensemble_predictions(
                model_predictions, 
                model_confidences
            )
            
            return PredictionResult(
                prediction_type=PredictionType.DEEP_LEARNING,
                ball_type=BallType.RED,
                value=ensemble_result,
                confidence=0.9,
                metadata={
                    "success": True,
                    "predictions": ensemble_result,
                    "ensemble_method": self.config.ensemble_method.value,
                    "participating_models": list(model_predictions.keys()),
                    "model_count": len(model_predictions)
                }
            )
            
        except Exception as e:
            self.logger.error(f"集成预测失败: {e}")
            return PredictionResult(
                prediction_type=PredictionType.DEEP_LEARNING,
                ball_type=BallType.RED,
                value=None,
                confidence=0.0,
                metadata={"success": False, "error": str(e), "predictions": {}}
            )
    
    def _extract_confidence(self, predictions: Dict[str, Any]) -> float:
        """提取预测置信度"""
        # 查找置信度字段
        for key, value in predictions.items():
            if 'confidence' in key.lower():
                if isinstance(value, (int, float)):
                    return float(value)
                elif isinstance(value, dict) and 'confidence' in value:
                    return float(value['confidence'])
        
        # 如果没有明确的置信度，使用默认值
        return 0.7
    
    def _update_performance_history(self, model_name: str, performance: float):
        """更新模型性能历史"""
        if model_name not in self.performance_history:
            self.performance_history[model_name] = []
        
        self.performance_history[model_name].append(performance)
        
        # 保持窗口大小
        if len(self.performance_history[model_name]) > self.config.performance_window:
            self.performance_history[model_name].pop(0)
        
        # 更新动态权重
        avg_performance = np.mean(self.performance_history[model_name])
        self.dynamic_weights[model_name] = avg_performance
    
    def _ensemble_predictions(self, model_predictions: Dict[str, Dict], 
                            model_confidences: Dict[str, float]) -> Dict[str, Any]:
        """执行集成预测"""
        
        if self.config.ensemble_method == EnsembleMethod.VOTING:
            return self._voting_ensemble(model_predictions)
        
        elif self.config.ensemble_method == EnsembleMethod.WEIGHTED_AVERAGE:
            return self._weighted_average_ensemble(model_predictions, model_confidences)
        
        elif self.config.ensemble_method == EnsembleMethod.DYNAMIC_WEIGHT:
            return self._dynamic_weight_ensemble(model_predictions, model_confidences)
        
        elif self.config.ensemble_method == EnsembleMethod.STACKING:
            return self._stacking_ensemble(model_predictions)
        
        elif self.config.ensemble_method == EnsembleMethod.BAYESIAN_FUSION:
            return self._bayesian_fusion_ensemble(model_predictions, model_confidences)
        
        else:
            # 默认使用加权平均
            return self._weighted_average_ensemble(model_predictions, model_confidences)
    
    def _voting_ensemble(self, model_predictions: Dict[str, Dict]) -> Dict[str, Any]:
        """投票集成"""
        # 简单投票：选择最多模型预测的结果
        vote_counts = {}
        
        for model_name, predictions in model_predictions.items():
            for key, value in predictions.items():
                if key not in vote_counts:
                    vote_counts[key] = {}
                
                vote_key = str(value)
                if vote_key not in vote_counts[key]:
                    vote_counts[key][vote_key] = 0
                vote_counts[key][vote_key] += 1
        
        # 选择得票最多的结果
        ensemble_result = {}
        for key, votes in vote_counts.items():
            best_vote = max(votes.items(), key=lambda x: x[1])
            try:
                ensemble_result[key] = float(best_vote[0])
            except ValueError:
                ensemble_result[key] = best_vote[0]
        
        ensemble_result["ensemble_method"] = "voting"
        return ensemble_result
    
    def _weighted_average_ensemble(self, model_predictions: Dict[str, Dict], 
                                 model_confidences: Dict[str, float]) -> Dict[str, Any]:
        """加权平均集成"""
        ensemble_result = {}
        
        # 收集所有预测键
        all_keys = set()
        for predictions in model_predictions.values():
            all_keys.update(predictions.keys())
        
        for key in all_keys:
            weighted_sum = 0.0
            total_weight = 0.0
            
            for model_name, predictions in model_predictions.items():
                if key in predictions:
                    value = predictions[key]
                    
                    # 提取数值
                    numeric_value = self._extract_numeric_prediction({key: value})
                    
                    # 获取权重
                    weight = self.config.model_weights.get(model_name, 1.0)
                    confidence = model_confidences.get(model_name, 1.0)
                    
                    final_weight = weight * confidence
                    weighted_sum += numeric_value * final_weight
                    total_weight += final_weight
            
            if total_weight > 0:
                ensemble_result[key] = weighted_sum / total_weight
            else:
                ensemble_result[key] = 0.5  # 默认值
        
        ensemble_result["ensemble_method"] = "weighted_average"
        ensemble_result["total_confidence"] = np.mean(list(model_confidences.values()))
        
        return ensemble_result
    
    def _dynamic_weight_ensemble(self, model_predictions: Dict[str, Dict], 
                               model_confidences: Dict[str, float]) -> Dict[str, Any]:
        """动态权重集成"""
        # 使用动态权重替代静态权重
        dynamic_config_weights = {}
        for model_name in model_predictions.keys():
            dynamic_config_weights[model_name] = self.dynamic_weights.get(model_name, 1.0)
        
        # 临时替换配置权重
        original_weights = self.config.model_weights.copy()
        self.config.model_weights = dynamic_config_weights
        
        # 执行加权平均
        result = self._weighted_average_ensemble(model_predictions, model_confidences)
        result["ensemble_method"] = "dynamic_weight"
        
        # 恢复原始权重
        self.config.model_weights = original_weights
        
        return result
    
    def _stacking_ensemble(self, model_predictions: Dict[str, Dict]) -> Dict[str, Any]:
        """堆叠集成"""
        if self.meta_learner is None:
            # 如果没有元学习器，回退到加权平均
            return self._weighted_average_ensemble(model_predictions, {})
        
        try:
            # 构建元特征
            meta_features = []
            for model_name in self.models.keys():
                if model_name in model_predictions:
                    pred_value = self._extract_numeric_prediction(model_predictions[model_name])
                    meta_features.append(pred_value)
                else:
                    meta_features.append(0.0)
            
            # 使用元学习器预测
            if meta_features:
                meta_prediction = self.meta_learner.predict([meta_features])[0]
                
                return {
                    "stacking_prediction": float(meta_prediction),
                    "ensemble_method": "stacking",
                    "meta_features": meta_features
                }
            else:
                return {"ensemble_method": "stacking", "error": "无有效元特征"}
                
        except Exception as e:
            self.logger.error(f"堆叠集成失败: {e}")
            # 回退到加权平均
            return self._weighted_average_ensemble(model_predictions, {})
    
    def _bayesian_fusion_ensemble(self, model_predictions: Dict[str, Dict], 
                                model_confidences: Dict[str, float]) -> Dict[str, Any]:
        """贝叶斯融合集成"""
        # 简化的贝叶斯融合：基于置信度的概率加权
        ensemble_result = {}
        
        # 收集所有预测键
        all_keys = set()
        for predictions in model_predictions.values():
            all_keys.update(predictions.keys())
        
        for key in all_keys:
            # 计算贝叶斯后验
            posterior_sum = 0.0
            evidence_sum = 0.0
            
            for model_name, predictions in model_predictions.items():
                if key in predictions:
                    value = predictions[key]
                    numeric_value = self._extract_numeric_prediction({key: value})
                    confidence = model_confidences.get(model_name, 0.5)
                    
                    # 贝叶斯权重：置信度作为似然
                    likelihood = confidence
                    prior = self.config.prior_weight
                    
                    # 后验 ∝ 似然 × 先验
                    posterior = likelihood * (prior + numeric_value)
                    evidence = likelihood + prior
                    
                    posterior_sum += posterior
                    evidence_sum += evidence
            
            if evidence_sum > 0:
                ensemble_result[key] = posterior_sum / evidence_sum
            else:
                ensemble_result[key] = 0.5
        
        ensemble_result["ensemble_method"] = "bayesian_fusion"
        ensemble_result["evidence_strength"] = evidence_sum
        
        return ensemble_result
    
    def predict_batch(self, data: pd.DataFrame, target_indices: List[int], **kwargs) -> List[PredictionResult]:
        """批量预测"""
        results = []
        for idx in target_indices:
            result = self.predict(data, idx, **kwargs)
            results.append(result)
        return results
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        info = {
            "name": "深度学习集成器",
            "version": "1.0.0",
            "is_trained": self.is_trained_flag,
            "ensemble_method": self.config.ensemble_method.value,
            "model_weights": self.config.model_weights,
            "sub_models": {}
        }
        
        # 收集子模型信息
        for model_name, model in self.models.items():
            try:
                info["sub_models"][model_name] = model.get_model_info()
            except Exception as e:
                info["sub_models"][model_name] = {"error": str(e)}
        
        if self.config.ensemble_method == EnsembleMethod.DYNAMIC_WEIGHT:
            info["dynamic_weights"] = self.dynamic_weights
            info["performance_history"] = {
                name: len(history) for name, history in self.performance_history.items()
            }
        
        return info
