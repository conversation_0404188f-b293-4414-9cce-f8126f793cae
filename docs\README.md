# 大乐透预测系统文档

## 文档结构

### API文档
- [API参考](api/) - 自动生成的API文档

### 用户指南
- [快速开始](user_guide/quick_start.md) - 快速上手指南
- [配置说明](user_guide/configuration.md) - 配置参数说明
- [使用教程](user_guide/tutorial.md) - 详细使用教程
- [常见问题](user_guide/faq.md) - 常见问题解答

### 开发指南
- [开发环境搭建](developer_guide/setup.md) - 开发环境配置
- [代码贡献指南](developer_guide/contributing.md) - 如何贡献代码
- [架构设计](developer_guide/architecture.md) - 系统架构说明
- [测试指南](developer_guide/testing.md) - 测试编写和运行

### 示例代码
- [基础使用示例](examples/basic_usage.py) - 基础功能示例
- [高级功能示例](examples/advanced_usage.py) - 高级功能示例
- [自定义扩展示例](examples/custom_extension.py) - 自定义扩展示例

## 生成文档

### 使用Sphinx生成API文档
```bash
cd docs
sphinx-build -b html . _build/html
```

### 查看文档
生成的文档位于 `docs/_build/html/index.html`
