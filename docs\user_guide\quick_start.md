# 快速开始指南

## 安装

### 1. 克隆项目
```bash
git clone https://github.com/your-username/lottery-predictor.git
cd lottery-predictor
```

### 2. 创建虚拟环境
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate  # Windows
```

### 3. 安装依赖
```bash
pip install -r requirements.txt
```

### 4. 配置环境
```bash
cp .env.example .env
# 根据需要编辑 .env 文件
```

## 快速运行

### 1. 验证环境
```bash
python scripts/main.py --validate-only
```

### 2. 运行基础预测
```bash
python scripts/main.py --mode basic
```

### 3. 运行高级预测
```bash
python scripts/main.py --mode advanced
```

### 4. 自动选择最佳模式
```bash
python scripts/main.py --mode auto
```

## 命令行参数

- `--mode`: 运行模式 (basic, advanced, super, ultimate, auto)
- `--periods`: 回测期数 (默认: 50)
- `--display`: 显示期数 (默认: 10)
- `--debug`: 启用调试模式
- `--validate-only`: 仅验证环境

## 示例输出

```
开始大乐透预测系统回测...
============================================================
将回测 50 期数据

基于第25068期预测第25069期:

红球
奇偶比: 预测[3:2(0.709)] -> 实际[2:3] (未中)
大小比: 预测[2:3(0.695)] -> 实际[3:2] (未中)

蓝球
大小比: 预测[1:1(0.708)] -> 实际[0:2] (未中)

杀号：1：（05,19），2：（07,23），3：（08,22），4：（14,31），5：（11,20），6：（04,33），7：（01,09）——成功率：92%

预测号码：05,06,08,18,32——01,02
实际开奖号码：01,04,17,20,22——04,10
```

## 配置说明

主要配置文件位于 `config/settings.py`，包含：

- **数据配置**: 数据文件路径、验证设置
- **模型配置**: 马尔科夫阶数、神经网络参数
- **生成器配置**: 号码生成参数、杀号设置
- **回测配置**: 回测期数、性能阈值
- **日志配置**: 日志级别、文件设置

## 故障排除

### 常见问题

1. **数据文件不存在**
   ```
   FileNotFoundError: 数据文件不存在: data/raw/dlt_data.csv
   ```
   解决方案: 确保数据文件存在于正确位置

2. **依赖包缺失**
   ```
   ModuleNotFoundError: No module named 'pandas'
   ```
   解决方案: 运行 `pip install -r requirements.txt`

3. **权限错误**
   ```
   PermissionError: [Errno 13] Permission denied
   ```
   解决方案: 检查文件权限或使用管理员权限

### 获取帮助

- 查看详细日志: `python scripts/main.py --debug`
- 运行测试: `pytest tests/`
- 查看配置: `python -c "from config import get_settings; print(get_settings())"`

## 下一步

- 阅读 [配置说明](configuration.md) 了解详细配置
- 查看 [使用教程](tutorial.md) 学习高级功能
- 参考 [API文档](../api/) 了解编程接口
