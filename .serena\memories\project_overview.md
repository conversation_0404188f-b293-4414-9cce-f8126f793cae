# 彩票预测系统项目概述

## 项目目的
这是一个基于数据分析和机器学习的大乐透彩票预测系统。系统通过分析历史开奖数据，使用马尔科夫链、贝叶斯推理、决策树等算法进行特征分析和号码预测。

## 核心功能
- **数据分析**: 对历史开奖数据进行奇偶比、大小比等特征分析
- **预测模型**: 使用马尔科夫链、贝叶斯推理、神经网络等多种算法
- **杀号策略**: 预测最不可能出现的号码，提高预测准确性
- **号码生成**: 基于预测结果生成符合条件的号码组合
- **回测验证**: 对预测模型进行历史数据回测验证

## 技术特点
- 严格遵守数据隔离原则，避免未来数据泄漏
- 确定性预测，避免随机性影响结果一致性
- 模块化设计，支持多种算法组合
- 完整的回测框架，验证模型效果

## 数据源
- 主要数据文件: `data/raw/dlt_data.csv`
- 数据格式: 期号,红球1-5,蓝球1-2,日期
- 数据范围: 从2015年开始的历史开奖数据