#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大乐透预测系统 - 交互式启动器
Interactive Launcher for Lottery Prediction System
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def display_banner():
    """显示系统横幅"""
    print("=" * 80)
    print("大乐透预测系统 - 交互式启动器")
    print("Lottery Prediction System - Interactive Launcher")
    print("=" * 80)


def display_system_menu():
    """显示系统选择菜单"""
    print("\n请选择要使用的预测系统:")
    print("   1. 主系统 (Main System) - 集成学习版本，包含红球大小比优化器")
    print("   2. 终极系统 (Ultimate System) - 多模式预测系统")
    print("   3. 重构系统 (Refactored System) - 交互式架构演示")
    print("   4. 简化重构系统 (Simple Refactored System)")
    print("   0. 退出 (Exit)")
    print("-" * 60)


def display_action_menu():
    """显示操作选择菜单"""
    print("\n请选择要执行的操作:")
    print("   1. 预测下一期 (Predict Next Period)")
    print("   2. 回测分析 (Backtest Analysis)")
    print("   3. 性能对比 (Performance Comparison)")
    print("   4. 交互模式 (Interactive Mode)")
    print("   0. 返回上级菜单 (Back to Main Menu)")
    print("-" * 60)


def display_mode_menu():
    """显示模式选择菜单（终极系统专用）"""
    print("\n请选择预测模式:")
    print("   1. 自动模式 (Auto) - 自动选择最佳可用模式")
    print("   2. 基础模式 (Basic) - 基础预测功能")
    print("   3. 增强模式 (Enhanced) - 增强预测功能")
    print("   4. 超级模式 (Super) - 超级预测功能")
    print("   5. 终极模式 (Ultimate) - 终极预测功能")
    print("   0. 返回上级菜单 (Back)")
    print("-" * 60)


def get_user_choice(prompt, valid_choices):
    """获取用户选择"""
    while True:
        try:
            choice = input(f"{prompt} ").strip()
            if choice in valid_choices:
                return choice
            else:
                print(f"无效选择，请输入: {', '.join(valid_choices)}")
        except KeyboardInterrupt:
            print("\n\n用户取消操作，退出程序")
            sys.exit(0)
        except EOFError:
            print("\n\n输入结束，退出程序")
            sys.exit(0)


def run_main_system(action):
    """运行主系统"""
    try:
        from src.systems.main import LotteryPredictor
        
        print("\n启动主系统 (集成学习版本)...")
        predictor = LotteryPredictor()
        
        if action == "1":  # 预测
            print("执行集成学习预测...")
            predictor.run_ensemble_backtest(num_periods=1, display_periods=1)
        elif action == "2":  # 回测
            periods = get_backtest_periods()
            print(f"执行回测分析 ({periods}期)...")
            predictor.run_ensemble_backtest(num_periods=periods, display_periods=min(periods, 5))
        elif action == "3":  # 对比
            periods = get_backtest_periods()
            print(f"执行性能对比 ({periods}期)...")
            predictor.run_ensemble_backtest(num_periods=periods, display_periods=min(periods, 3))
            print("\n" + "="*60)
            print("运行传统回测进行对比...")
            predictor.run_backtest(num_periods=periods, display_periods=min(periods, 3))
            
    except Exception as e:
        print(f"主系统运行失败: {e}")
        import traceback
        traceback.print_exc()


def run_ultimate_system(action, mode="auto"):
    """运行终极系统"""
    try:
        from src.systems.main_ultimate_fixed import UltimateLotteryPredictor
        
        print(f"\n启动终极系统 ({mode.upper()}模式)...")
        predictor = UltimateLotteryPredictor(mode=mode)
        
        if action == "1":  # 预测
            print("执行预测...")
            predictor.predict_and_display_next()
        elif action == "2":  # 回测
            periods = get_backtest_periods()
            print(f"执行回测分析 ({periods}期)...")
            predictor.basic_predictor.run_backtest(num_periods=periods, display_periods=min(periods, 5))
        elif action == "3":  # 对比
            periods = get_backtest_periods()
            print(f"执行综合回测对比 ({periods}期)...")
            predictor.run_comprehensive_backtest(num_periods=periods, display_periods=min(periods, 5))
            
    except Exception as e:
        print(f"终极系统运行失败: {e}")
        import traceback
        traceback.print_exc()


def run_refactored_system():
    """运行重构系统"""
    try:
        from src.systems.refactored_main import RefactoredLotterySystem
        
        print("\n启动重构系统 (交互式架构演示)...")
        system = RefactoredLotterySystem()
        system.run_interactive_mode()
        
    except Exception as e:
        print(f"重构系统运行失败: {e}")
        import traceback
        traceback.print_exc()


def run_simple_refactored_system():
    """运行简化重构系统"""
    try:
        from src.systems.simple_refactored_main import SimpleRefactoredLotterySystem
        
        print("\n启动简化重构系统...")
        system = SimpleRefactoredLotterySystem()
        # 这里需要根据实际的简化重构系统接口调用相应方法
        print("简化重构系统启动成功")
        
    except Exception as e:
        print(f"简化重构系统运行失败: {e}")
        import traceback
        traceback.print_exc()


def get_backtest_periods():
    """获取回测期数"""
    while True:
        try:
            periods_str = input("请输入回测期数 (默认10期): ").strip()
            if not periods_str:
                return 10
            periods = int(periods_str)
            if 1 <= periods <= 100:
                return periods
            else:
                print("期数应在1-100之间")
        except ValueError:
            print("请输入有效的数字")
        except KeyboardInterrupt:
            return 10


def main():
    """主函数"""
    display_banner()
    
    while True:
        display_system_menu()
        system_choice = get_user_choice("请选择系统 (0-4):", ["0", "1", "2", "3", "4"])
        
        if system_choice == "0":
            print("\n感谢使用大乐透预测系统，再见！")
            break
            
        elif system_choice == "1":  # 主系统
            while True:
                display_action_menu()
                action_choice = get_user_choice("请选择操作 (0-4):", ["0", "1", "2", "3", "4"])
                
                if action_choice == "0":
                    break
                elif action_choice in ["1", "2", "3"]:
                    run_main_system(action_choice)
                    input("\n按回车键继续...")
                elif action_choice == "4":
                    print("主系统暂不支持交互模式，请选择其他操作")
                    
        elif system_choice == "2":  # 终极系统
            while True:
                display_mode_menu()
                mode_choice = get_user_choice("请选择模式 (0-5):", ["0", "1", "2", "3", "4", "5"])
                
                if mode_choice == "0":
                    break
                    
                mode_map = {"1": "auto", "2": "basic", "3": "enhanced", "4": "super", "5": "ultimate"}
                selected_mode = mode_map[mode_choice]
                
                while True:
                    display_action_menu()
                    action_choice = get_user_choice("请选择操作 (0-4):", ["0", "1", "2", "3", "4"])
                    
                    if action_choice == "0":
                        break
                    elif action_choice in ["1", "2", "3"]:
                        run_ultimate_system(action_choice, selected_mode)
                        input("\n按回车键继续...")
                    elif action_choice == "4":
                        print("终极系统暂不支持独立交互模式")
                        
        elif system_choice == "3":  # 重构系统
            run_refactored_system()
            input("\n按回车键继续...")
            
        elif system_choice == "4":  # 简化重构系统
            run_simple_refactored_system()
            input("\n按回车键继续...")


if __name__ == "__main__":
    main()