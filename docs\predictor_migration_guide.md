# 预测器迁移指南

## 概述

本指南帮助开发者将旧的预测器迁移到新的架构中，确保与 `IStandardPredictor` 接口的兼容性。

## 新架构的主要变化

### 1. 接口标准化
- 新增 `IStandardPredictor` 接口，提供标准化的预测器功能
- 所有新预测器都应实现此接口
- 提供 `StandardBasePredictor` 基类简化实现

### 2. 统一的结果格式
- 使用 `PredictionResult` 作为标准返回格式
- 包含预测类型、球类型、值、置信度和元数据

### 3. 输入验证
- 强制要求实现 `validate_input` 方法
- 返回 `ValidationResult` 对象

## 迁移策略

### 策略1：使用适配器（推荐）

对于现有的旧预测器，最简单的方法是使用适配器：

```python
from src.core.predictor_adapter import create_legacy_adapter

# 创建旧预测器实例
legacy_predictor = YourOldPredictor()

# 创建适配器
adapted_predictor = create_legacy_adapter(
    legacy_predictor, 
    name="AdaptedYourOldPredictor", 
    version="1.0"
)

# 现在可以像使用新接口一样使用
result = adapted_predictor.predict(data, target_index, ball_type=BallType.RED)
```

### 策略2：直接重构（长期推荐）

对于重要的预测器，建议直接重构以实现新接口：

```python
from src.core.base import StandardBasePredictor
from src.core.interfaces import PredictionResult, ValidationResult
from src.core.base import BallType, PredictionType

class YourNewPredictor(StandardBasePredictor):
    def __init__(self):
        super().__init__("YourNewPredictor", PredictionType.NUMBERS)
        # 初始化你的预测器特定配置
    
    def predict(self, data: pd.DataFrame, target_index: int, **kwargs) -> PredictionResult:
        """执行预测"""
        # 你的预测逻辑
        predicted_value = self._your_prediction_logic(data, target_index, **kwargs)
        
        return PredictionResult(
            prediction_type=self.prediction_type,
            ball_type=kwargs.get('ball_type', BallType.RED),
            value=predicted_value,
            confidence=self._calculate_confidence(),
            metadata={"method": "your_method"}
        )
    
    def validate_input(self, data: pd.DataFrame, target_index: int) -> ValidationResult:
        """验证输入数据"""
        errors = []
        warnings = []
        
        # 你的验证逻辑
        if data is None or data.empty:
            errors.append("输入数据为空")
        
        if target_index < 0 or target_index >= len(data):
            errors.append("目标索引超出范围")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )
    
    def _your_prediction_logic(self, data, target_index, **kwargs):
        """你的具体预测逻辑"""
        # 实现你的预测算法
        pass
    
    def _calculate_confidence(self):
        """计算置信度"""
        # 实现置信度计算
        return 0.5
```

## 具体迁移示例

### 示例1：RatioPredictor 迁移

**旧版本：**
```python
class RatioPredictor(IPredictor):
    def predict(self, data, target_index, **kwargs):
        # 返回字典格式
        return {"value": "1:4", "confidence": 0.8}
```

**新版本（使用适配器）：**
```python
from src.core.predictor_adapter import create_legacy_adapter

# 使用适配器
ratio_predictor = RatioPredictor()
adapted_ratio_predictor = create_legacy_adapter(ratio_predictor, "AdaptedRatioPredictor")
```

**新版本（直接重构）：**
```python
class NewRatioPredictor(StandardBasePredictor):
    def __init__(self):
        super().__init__("RatioPredictor", PredictionType.RATIO)
        # 初始化比值状态映射等
    
    def predict(self, data, target_index, **kwargs):
        # 你的比值预测逻辑
        ratio_value = self._calculate_ratio(data, target_index, **kwargs)
        
        return PredictionResult(
            prediction_type=self.prediction_type,
            ball_type=kwargs.get('ball_type', BallType.RED),
            value=ratio_value,
            confidence=0.8,
            metadata={"method": "ratio_analysis"}
        )
```

## 接口对比

### 旧接口 vs 新接口

| 功能 | 旧接口 (IPredictor) | 新接口 (IStandardPredictor) |
|------|-------------------|---------------------------|
| 预测方法 | `predict(data, target_index, **kwargs)` | `predict(data, target_index, **kwargs)` |
| 批量预测 | 无 | `predict_batch(data, target_indices, **kwargs)` |
| 输入验证 | 可选 | 必需 `validate_input(data, target_index)` |
| 模型信息 | 无 | 必需 `get_model_info()` |
| 置信度管理 | 无 | `get_confidence_threshold()`, `set_confidence_threshold()` |
| 训练支持 | 可选 | `train(data, **kwargs)` |
| 增量训练 | 无 | `supports_incremental_training()` |

## 迁移检查清单

### 对于使用适配器的迁移：
- [ ] 创建适配器实例
- [ ] 测试基本预测功能
- [ ] 测试批量预测功能
- [ ] 验证输入验证功能
- [ ] 检查模型信息输出

### 对于直接重构的迁移：
- [ ] 继承 `StandardBasePredictor`
- [ ] 实现 `predict` 方法
- [ ] 实现 `validate_input` 方法
- [ ] 设置正确的预测类型
- [ ] 返回标准的 `PredictionResult`
- [ ] 添加适当的错误处理
- [ ] 编写单元测试
- [ ] 更新文档

## 常见问题

### Q: 旧预测器的配置如何处理？
A: 适配器会自动保留旧预测器的配置，可以通过 `get_model_info()` 方法的 `legacy_config` 字段访问。

### Q: 如何处理不同的返回格式？
A: 适配器会自动转换常见的返回格式（字典、对象、简单值）到标准的 `PredictionResult` 格式。

### Q: 旧预测器的性能会受影响吗？
A: 适配器只是一个轻量级的包装器，性能影响微乎其微。

### Q: 什么时候应该选择直接重构？
A: 对于核心预测器、需要长期维护的预测器，或者需要利用新接口特性的预测器，建议直接重构。

## 测试建议

### 1. 单元测试
```python
def test_migrated_predictor():
    predictor = YourMigratedPredictor()
    
    # 测试基本功能
    assert isinstance(predictor, IStandardPredictor)
    
    # 测试预测
    result = predictor.predict(test_data, 10)
    assert result is not None
    assert hasattr(result, 'value')
    assert hasattr(result, 'confidence')
    
    # 测试验证
    validation = predictor.validate_input(test_data, 10)
    assert validation.is_valid
```

### 2. 集成测试
使用提供的集成测试套件：
```bash
python src/tests/integration_test.py
```

## 支持和帮助

如果在迁移过程中遇到问题：

1. 查看 `src/tests/integration_test.py` 中的示例
2. 参考 `src/models/unified_odd_even_predictor.py` 的实现
3. 使用适配器作为临时解决方案
4. 查看错误日志和异常信息

## 最佳实践

1. **渐进式迁移**：先使用适配器，再逐步重构重要组件
2. **保持向后兼容**：在迁移期间保留旧接口的支持
3. **充分测试**：确保迁移后功能正常
4. **文档更新**：及时更新相关文档
5. **性能监控**：监控迁移后的性能变化

---

*本指南会根据实际使用情况持续更新。*