# 统一回测框架使用指南

## 🎯 概述

统一回测框架解决了原有回测系统的核心问题：
- **期号误用作循环条件** → 现在期号只作为标志，循环基于数据索引
- **回测逻辑分散** → 统一的回测接口和流程
- **代码重复** → 标准化的预测器接口和适配器模式
- **结果格式不一致** → 标准化的数据模型和显示格式

## 🏗️ 架构设计

### 核心组件

```
src/framework/
├── __init__.py              # 框架入口
├── interfaces.py            # 接口定义
├── data_models.py          # 数据模型
├── backtest_framework.py   # 核心回测逻辑
├── predictor_adapter.py    # 预测器适配器
├── result_display.py       # 结果显示器
└── example_usage.py        # 使用示例
```

### 设计原则

1. **期号只作为标志**：
   ```python
   # ❌ 错误：基于期号循环
   for period in period_list:
       # 期号作为循环条件
   
   # ✅ 正确：基于数据索引循环
   for data_index in range(min_train, max_backtest):
       period_number = data.iloc[data_index]['期号']  # 期号只作为标志
   ```

2. **统一的预测器接口**：
   ```python
   class PredictorInterface(ABC):
       @abstractmethod
       def predict_for_period(self, data_index: int, data: pd.DataFrame) -> PredictionResult:
           pass
   ```

3. **标准化的数据流**：
   ```
   数据索引 → 预测器 → 标准预测结果 → 评估器 → 标准评估结果 → 显示器
   ```

## 🚀 快速开始

### 1. 基本使用

```python
from src.framework import BacktestFramework, BacktestConfig, create_predictor_adapter
from src.systems.main import LotteryPredictor

# 创建原始预测器
original_predictor = LotteryPredictor()

# 创建适配器
predictor_adapter = create_predictor_adapter('lottery', original_predictor)

# 创建回测框架
framework = BacktestFramework(original_predictor.data)

# 配置回测
config = BacktestConfig(
    num_periods=10,           # 回测期数
    min_train_periods=50,     # 最少训练期数
    display_periods=5         # 显示期数
)

# 运行回测
result = framework.run_backtest(predictor_adapter, config)

# 显示结果
from src.framework import ResultDisplayer
displayer = ResultDisplayer()
displayer.display_backtest_result(result)
```

### 2. 配置选项

```python
config = BacktestConfig(
    num_periods=10,                    # 回测期数
    min_train_periods=50,              # 最少训练期数
    display_periods=5,                 # 显示期数
    metrics=[                          # 评估指标
        'red_odd_even_hit', 'red_size_hit', 'blue_size_hit',
        'hit_2_plus_1', 'red_kill_success', 'blue_kill_success'
    ],
    enable_detailed_output=True,       # 详细输出
    enable_statistics=True,            # 统计信息
    reverse_display=True               # 倒序显示
)
```

## 🔧 适配现有预测器

### 创建自定义适配器

```python
from src.framework.interfaces import PredictorInterface
from src.framework.data_models import PredictionResult

class MyPredictorAdapter(PredictorInterface):
    def __init__(self, original_predictor):
        self.original_predictor = original_predictor
    
    def predict_for_period(self, data_index: int, data: pd.DataFrame) -> PredictionResult:
        # 更新预测器数据（使用到当前索引的所有数据）
        train_data = data.iloc[:data_index + 1]
        self.original_predictor.data = train_data
        
        # 期号只作为标志
        period_number = str(data.iloc[data_index]['期号'])
        
        # 调用原始预测方法
        prediction_dict = self.original_predictor.predict_next_period()
        
        # 转换为标准格式
        return PredictionResult(
            period_number=period_number,
            data_index=data_index,
            red_odd_even_predictions=prediction_dict.get('red_odd_even', []),
            red_size_predictions=prediction_dict.get('red_size', []),
            blue_size_predictions=prediction_dict.get('blue_size', []),
            generated_numbers=prediction_dict.get('generated_numbers', ([], [])),
            kill_numbers=prediction_dict.get('kill_numbers', {}),
            predictor_name=self.get_predictor_name()
        )
    
    def get_predictor_name(self) -> str:
        return "MyPredictor"
    
    def get_predictor_version(self) -> str:
        return "1.0.0"
```

## 📊 结果分析

### 标准化结果格式

```python
# 回测结果
result = BacktestResult(
    config=config,                    # 回测配置
    period_results=period_results,    # 各期结果
    statistics=statistics,            # 统计信息
    predictor_name="LotteryPredictor",
    total_duration=1.23              # 耗时（秒）
)

# 获取成功率
success_rate = result.get_success_rate()

# 获取显示结果（根据配置）
display_results = result.get_display_results()
```

### 统计指标

- **比例预测命中率**：红球奇偶比、红球大小比、蓝球大小比
- **2+1命中率**：所有三个比例预测都正确
- **杀号成功率**：杀号中没有出现在开奖号码中
- **号码命中统计**：平均红球命中、蓝球命中、总命中

## 🔍 关键特性

### 1. 期号与数据索引分离

```python
# 框架内部循环逻辑
for data_index in range(min_train_periods, min_train_periods + max_backtest):
    # 期号只作为标志
    period_number = str(data.iloc[data_index]['期号'])
    
    # 预测器接收数据索引，内部决定如何使用历史数据
    prediction = predictor.predict_for_period(data_index, data)
```

**优势**：
- 支持非连续期号数据
- 避免期号作为循环条件的逻辑错误
- 数据索引直接对应DataFrame行号，更可靠

### 2. 适配器模式

```python
# 支持不同类型的预测器
lottery_adapter = create_predictor_adapter('lottery', lottery_predictor)
advanced_adapter = create_predictor_adapter('advanced', advanced_system)

# 统一的回测接口
result1 = framework.run_backtest(lottery_adapter, config)
result2 = framework.run_backtest(advanced_adapter, config)
```

### 3. 可配置的评估

```python
# 自定义评估指标
config = BacktestConfig(
    metrics=['red_kill_success', 'blue_kill_success']  # 只关注杀号
)

# 自定义显示选项
config = BacktestConfig(
    display_periods=10,        # 显示10期
    reverse_display=False,     # 正序显示
    enable_detailed_output=False  # 只显示统计
)
```

## 🛠️ 扩展指南

### 添加新的预测器

1. 实现 `PredictorInterface` 接口
2. 或创建适配器适配现有预测器
3. 使用 `create_predictor_adapter` 工厂方法

### 添加新的评估指标

1. 继承 `EvaluatorInterface`
2. 实现 `evaluate` 方法
3. 在 `BacktestConfig.metrics` 中添加新指标

### 自定义结果显示

1. 继承 `ResultDisplayer`
2. 重写显示方法
3. 或直接使用 `BacktestResult` 数据自定义显示

## 📝 最佳实践

1. **数据准备**：确保数据包含'期号'列
2. **训练期数**：设置合理的 `min_train_periods`
3. **回测期数**：根据数据量设置 `num_periods`
4. **错误处理**：框架会自动处理单期失败，继续其他期次
5. **性能优化**：大量回测时可关闭详细输出

## 🔧 故障排除

### 常见问题

1. **数据格式错误**：确保包含'期号'列
2. **训练数据不足**：增加 `min_train_periods`
3. **适配器错误**：检查原始预测器接口
4. **内存不足**：减少 `num_periods` 或关闭详细输出

### 调试技巧

```python
# 启用详细输出
config.enable_detailed_output = True

# 减少回测期数进行快速测试
config.num_periods = 3

# 检查单期预测
prediction = predictor.predict_for_period(data_index, data)
print(prediction)
```

## 🎉 总结

统一回测框架成功解决了原有系统的架构问题：

✅ **期号只作为标志**：避免了期号作为循环条件的错误设计  
✅ **统一接口**：所有预测器通过标准接口交互  
✅ **高内聚低耦合**：回测逻辑与预测算法分离  
✅ **可扩展性**：支持添加新预测器和评估指标  
✅ **标准化**：统一的配置、结果和显示格式  

现在可以：
1. 轻松添加新的预测器
2. 标准化比较不同算法性能
3. 避免重复的回测代码
4. 确保回测逻辑的正确性
