"""
2+1命中率分析模块
深度分析为什么2+1命中率低，找出改进方案
"""

import pandas as pd
import numpy as np
from collections import Counter, defaultdict
from typing import Dict, List, Tuple, Set
from utils import load_data, parse_numbers, check_hit_2_plus_1
from main import LotteryPredictor


class HitAnalyzer:
    """2+1命中分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.data = load_data()
        self.predictor = LotteryPredictor()
    
    def analyze_prediction_vs_actual(self, num_periods: int = 20) -> None:
        """分析预测号码与实际开奖的差异"""
        print("=" * 60)
        print("预测号码 vs 实际开奖分析")
        print("=" * 60)
        
        red_hit_stats = defaultdict(int)
        blue_hit_stats = defaultdict(int)
        
        for i in range(num_periods):
            try:
                # 获取预测
                prediction = self.predictor.predict_next_period(i)
                pred_red, pred_blue = prediction['generated_numbers']
                
                # 获取实际开奖
                actual_row = self.data.iloc[i]
                actual_red, actual_blue = parse_numbers(actual_row)
                
                # 分析红球命中情况
                red_hits = len(set(pred_red) & set(actual_red))
                red_hit_stats[red_hits] += 1
                
                # 分析蓝球命中情况
                blue_hits = len(set(pred_blue) & set(actual_blue))
                blue_hit_stats[blue_hits] += 1
                
                print(f"\n期号 {actual_row['期号']}:")
                print(f"  预测红球: {sorted(pred_red)}")
                print(f"  实际红球: {sorted(actual_red)}")
                print(f"  红球命中: {red_hits}/5 个 {list(set(pred_red) & set(actual_red))}")
                print(f"  预测蓝球: {sorted(pred_blue)}")
                print(f"  实际蓝球: {sorted(actual_blue)}")
                print(f"  蓝球命中: {blue_hits}/2 个 {list(set(pred_blue) & set(actual_blue))}")
                
                # 检查2+1
                is_2_plus_1 = check_hit_2_plus_1((pred_red, pred_blue), (actual_red, actual_blue))
                print(f"  2+1命中: {'是' if is_2_plus_1 else '否'}")
                
            except Exception as e:
                print(f"分析第{i+1}期时出错: {e}")
                continue
        
        print(f"\n红球命中统计 (共{num_periods}期):")
        for hits in range(6):
            count = red_hit_stats[hits]
            rate = count / num_periods * 100
            print(f"  命中{hits}个: {count}次 ({rate:.1f}%)")
        
        print(f"\n蓝球命中统计 (共{num_periods}期):")
        for hits in range(3):
            count = blue_hit_stats[hits]
            rate = count / num_periods * 100
            print(f"  命中{hits}个: {count}次 ({rate:.1f}%)")
    
    def analyze_number_selection_bias(self) -> None:
        """分析号码选择偏差"""
        print("\n" + "=" * 60)
        print("号码选择偏差分析")
        print("=" * 60)
        
        # 统计我们经常选择的号码
        predicted_red_freq = Counter()
        predicted_blue_freq = Counter()
        
        # 统计实际开奖的号码
        actual_red_freq = Counter()
        actual_blue_freq = Counter()
        
        for i in range(50):
            try:
                # 预测号码
                prediction = self.predictor.predict_next_period(i)
                pred_red, pred_blue = prediction['generated_numbers']
                predicted_red_freq.update(pred_red)
                predicted_blue_freq.update(pred_blue)
                
                # 实际号码
                actual_row = self.data.iloc[i]
                actual_red, actual_blue = parse_numbers(actual_row)
                actual_red_freq.update(actual_red)
                actual_blue_freq.update(actual_blue)
                
            except:
                continue
        
        print("\n红球选择偏差分析:")
        print("我们最常预测的红球:")
        for num, count in predicted_red_freq.most_common(10):
            actual_count = actual_red_freq.get(num, 0)
            bias = count - actual_count
            print(f"  {num:02d}: 预测{count}次, 实际{actual_count}次, 偏差{bias:+d}")
        
        print("\n实际最常开出的红球:")
        for num, count in actual_red_freq.most_common(10):
            pred_count = predicted_red_freq.get(num, 0)
            bias = pred_count - count
            print(f"  {num:02d}: 实际{count}次, 预测{pred_count}次, 偏差{bias:+d}")
        
        print("\n蓝球选择偏差分析:")
        print("我们最常预测的蓝球:")
        for num, count in predicted_blue_freq.most_common():
            actual_count = actual_blue_freq.get(num, 0)
            bias = count - actual_count
            print(f"  {num:02d}: 预测{count}次, 实际{actual_count}次, 偏差{bias:+d}")
    
    def analyze_winning_patterns(self) -> None:
        """分析中奖模式"""
        print("\n" + "=" * 60)
        print("中奖模式分析")
        print("=" * 60)
        
        # 分析最近50期的开奖号码特征
        red_patterns = {
            'sum_ranges': defaultdict(int),
            'span_ranges': defaultdict(int),
            'consecutive_counts': defaultdict(int),
            'odd_even_patterns': defaultdict(int),
            'size_patterns': defaultdict(int)
        }
        
        blue_patterns = {
            'sum_ranges': defaultdict(int),
            'gap_ranges': defaultdict(int),
            'size_patterns': defaultdict(int)
        }
        
        for i in range(50):
            try:
                actual_row = self.data.iloc[i]
                actual_red, actual_blue = parse_numbers(actual_row)
                
                # 红球模式分析
                red_sum = sum(actual_red)
                red_span = max(actual_red) - min(actual_red)
                
                # 和值范围
                if red_sum <= 80:
                    red_patterns['sum_ranges']['low'] += 1
                elif red_sum <= 120:
                    red_patterns['sum_ranges']['medium'] += 1
                else:
                    red_patterns['sum_ranges']['high'] += 1
                
                # 跨度范围
                if red_span <= 15:
                    red_patterns['span_ranges']['small'] += 1
                elif red_span <= 25:
                    red_patterns['span_ranges']['medium'] += 1
                else:
                    red_patterns['span_ranges']['large'] += 1
                
                # 连号统计
                sorted_red = sorted(actual_red)
                consecutive = sum(1 for j in range(4) if sorted_red[j+1] - sorted_red[j] == 1)
                red_patterns['consecutive_counts'][consecutive] += 1
                
                # 奇偶比
                odd_count = sum(1 for num in actual_red if num % 2 == 1)
                red_patterns['odd_even_patterns'][f"{odd_count}:{5-odd_count}"] += 1
                
                # 大小比
                small_count = sum(1 for num in actual_red if num <= 18)
                red_patterns['size_patterns'][f"{small_count}:{5-small_count}"] += 1
                
                # 蓝球模式分析
                blue_sum = sum(actual_blue)
                blue_gap = abs(actual_blue[1] - actual_blue[0]) if len(actual_blue) == 2 else 0
                
                # 蓝球和值
                if blue_sum <= 8:
                    blue_patterns['sum_ranges']['low'] += 1
                elif blue_sum <= 15:
                    blue_patterns['sum_ranges']['medium'] += 1
                else:
                    blue_patterns['sum_ranges']['high'] += 1
                
                # 蓝球间距
                if blue_gap <= 3:
                    blue_patterns['gap_ranges']['small'] += 1
                elif blue_gap <= 6:
                    blue_patterns['gap_ranges']['medium'] += 1
                else:
                    blue_patterns['gap_ranges']['large'] += 1
                
                # 蓝球大小比
                small_count = sum(1 for num in actual_blue if num <= 6)
                blue_patterns['size_patterns'][f"{small_count}:{2-small_count}"] += 1
                
            except:
                continue
        
        print("\n红球开奖模式统计:")
        for pattern_type, patterns in red_patterns.items():
            print(f"\n{pattern_type}:")
            total = sum(patterns.values())
            for pattern, count in patterns.items():
                rate = count / total * 100 if total > 0 else 0
                print(f"  {pattern}: {count}次 ({rate:.1f}%)")
        
        print("\n蓝球开奖模式统计:")
        for pattern_type, patterns in blue_patterns.items():
            print(f"\n{pattern_type}:")
            total = sum(patterns.values())
            for pattern, count in patterns.items():
                rate = count / total * 100 if total > 0 else 0
                print(f"  {pattern}: {count}次 ({rate:.1f}%)")
    
    def find_missed_opportunities(self) -> None:
        """找出错失的机会"""
        print("\n" + "=" * 60)
        print("错失机会分析")
        print("=" * 60)
        
        # 分析哪些号码我们从不选择，但经常开出
        never_predicted_red = set()
        never_predicted_blue = set()
        
        predicted_red_all = set()
        predicted_blue_all = set()
        
        for i in range(20):
            try:
                prediction = self.predictor.predict_next_period(i)
                pred_red, pred_blue = prediction['generated_numbers']
                predicted_red_all.update(pred_red)
                predicted_blue_all.update(pred_blue)
            except:
                continue
        
        # 找出从未预测的号码
        all_red = set(range(1, 36))
        all_blue = set(range(1, 13))
        
        never_predicted_red = all_red - predicted_red_all
        never_predicted_blue = all_blue - predicted_blue_all
        
        print(f"从未预测的红球: {sorted(never_predicted_red)}")
        print(f"从未预测的蓝球: {sorted(never_predicted_blue)}")
        
        # 统计这些号码的实际开出频率
        actual_freq_red = Counter()
        actual_freq_blue = Counter()
        
        for i in range(50):
            try:
                actual_row = self.data.iloc[i]
                actual_red, actual_blue = parse_numbers(actual_row)
                actual_freq_red.update(actual_red)
                actual_freq_blue.update(actual_blue)
            except:
                continue
        
        print("\n从未预测但经常开出的红球:")
        for num in sorted(never_predicted_red):
            freq = actual_freq_red.get(num, 0)
            if freq > 0:
                print(f"  {num:02d}: 开出{freq}次")
        
        print("\n从未预测但经常开出的蓝球:")
        for num in sorted(never_predicted_blue):
            freq = actual_freq_blue.get(num, 0)
            if freq > 0:
                print(f"  {num:02d}: 开出{freq}次")
    
    def suggest_improvements(self) -> None:
        """提出改进建议"""
        print("\n" + "=" * 60)
        print("改进建议")
        print("=" * 60)
        
        print("基于分析结果，建议以下改进策略:")
        print()
        print("1. 号码选择策略改进:")
        print("   • 扩大号码选择范围，避免过度集中在某些号码")
        print("   • 增加对历史高频号码的权重")
        print("   • 减少对理论模型的依赖，增加实际开奖数据的权重")
        print()
        print("2. 生成算法优化:")
        print("   • 使用多组合生成策略，而不是单一组合")
        print("   • 增加随机性，避免过度确定性")
        print("   • 基于实际开奖模式调整生成参数")
        print()
        print("3. 特征权重调整:")
        print("   • 降低状态预测的权重")
        print("   • 提高号码频率分析的权重")
        print("   • 增加近期趋势的影响")
        print()
        print("4. 多样化策略:")
        print("   • 生成多个候选组合")
        print("   • 使用投票机制选择最终号码")
        print("   • 引入更多随机因素")


def main():
    """主函数"""
    analyzer = HitAnalyzer()
    
    print("开始2+1命中率深度分析...")
    
    # 执行各种分析
    analyzer.analyze_prediction_vs_actual(20)
    analyzer.analyze_number_selection_bias()
    analyzer.analyze_winning_patterns()
    analyzer.find_missed_opportunities()
    analyzer.suggest_improvements()
    
    print("\n分析完成！")


if __name__ == "__main__":
    main()
