"""
Legacy Predictor Adapter 演示
展示如何使用适配器将旧预测器适配到新的标准化接口
"""

import sys
from pathlib import Path
import pandas as pd
from typing import Dict, Any

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.core.predictor_adapter import create_legacy_adapter
from src.core.interfaces import BallType, PredictionType
from src.core.base import PredictionResult, FeatureType


class MockLegacyPredictor:
    """模拟旧预测器 - 不实现IPredictor接口"""
    
    def __init__(self, name: str = "MockPredictor"):
        self.name = name
        self.is_trained = True
        self.config = {"algorithm": "mock", "version": "1.0"}
    
    def predict(self, data: pd.DataFrame, target_index: int, **kwargs) -> Dict[str, Any]:
        """旧式预测方法 - 返回字典格式"""
        # 模拟预测逻辑
        return {
            "value": "2:3",  # 奇偶比预测
            "confidence": 0.75,
            "metadata": {
                "algorithm": self.name,
                "data_size": len(data),
                "target_index": target_index
            }
        }
    
    def train(self, data: pd.DataFrame, **kwargs) -> bool:
        """训练方法"""
        print(f"* {self.name} 训练完成，数据量: {len(data)}")
        return True
    
    def get_prediction_type(self) -> PredictionType:
        """获取预测类型"""
        return PredictionType.ODD_EVEN_RATIO


class AnotherLegacyPredictor:
    """另一个旧预测器 - 返回简单值"""

    def __init__(self, name: str = "AnotherPredictor"):
        self.name = name
        self.is_trained = True

    def predict(self, data: pd.DataFrame, target_index: int, **kwargs) -> str:
        """返回简单字符串的旧预测器"""
        return "1:4"  # 大小比预测

    def train(self, data: pd.DataFrame, **kwargs) -> bool:
        print(f"* {self.name} 训练完成")
        return True

    def get_prediction_type(self) -> PredictionType:
        return PredictionType.SIZE_RATIO


def create_sample_data() -> pd.DataFrame:
    """创建示例数据"""
    return pd.DataFrame({
        '期号': [25001, 25002, 25003, 25004, 25005],
        '红球1': [1, 5, 12, 8, 15],
        '红球2': [8, 12, 18, 15, 22],
        '红球3': [15, 19, 25, 22, 29],
        '红球4': [22, 26, 32, 29, 33],
        '红球5': [29, 33, 35, 35, 35],
        '蓝球1': [3, 7, 2, 5, 8],
        '蓝球2': [9, 11, 8, 12, 10]
    })


def demo_legacy_adapter():
    """演示旧预测器适配功能"""
    print("=" * 60)
    print("Legacy Predictor Adapter 演示")
    print("=" * 60)
    
    # 创建示例数据
    sample_data = create_sample_data()
    print(f"* 创建示例数据: {len(sample_data)} 条记录")
    
    # 演示1: 适配返回字典的旧预测器
    print("\n1. 适配返回字典的旧预测器")
    print("-" * 40)
    
    legacy_predictor1 = MockLegacyPredictor("奇偶比预测器")
    adapter1 = create_legacy_adapter(legacy_predictor1, "适配的奇偶比预测器", "1.0")
    
    print(f"* 原始预测器: {legacy_predictor1.name}")
    print(f"* 适配器名称: {adapter1.name}")
    print(f"* 预测类型: {adapter1.get_prediction_type()}")
    
    # 验证输入
    validation = adapter1.validate_input(sample_data, 3)
    print(f"* 输入验证: {'通过' if validation.is_valid else '失败'}")
    
    # 执行预测
    result = adapter1.predict(sample_data, 3, ball_type=BallType.RED)
    print(f"* 预测结果: {result.value}")
    print(f"* 置信度: {result.confidence:.2f}")
    print(f"* 元数据: {result.metadata}")
    
    # 演示2: 适配返回PredictionResult的旧预测器
    print("\n2. 适配返回PredictionResult的旧预测器")
    print("-" * 40)
    
    legacy_predictor2 = AnotherLegacyPredictor("杀号预测器")
    adapter2 = create_legacy_adapter(legacy_predictor2, "适配的杀号预测器", "1.0")
    
    print(f"* 原始预测器: {legacy_predictor2.name}")
    print(f"* 适配器名称: {adapter2.name}")
    print(f"* 预测类型: {adapter2.get_prediction_type()}")
    
    # 执行预测
    result2 = adapter2.predict(sample_data, 3, ball_type=BallType.RED)
    print(f"* 预测结果: {result2.value}")
    print(f"* 置信度: {result2.confidence:.2f}")
    
    # 演示3: 批量预测
    print("\n3. 批量预测演示")
    print("-" * 40)
    
    batch_results = adapter1.predict_batch(sample_data, [1, 2, 3])
    print(f"* 批量预测结果数量: {len(batch_results)}")
    for i, result in enumerate(batch_results):
        print(f"  - 预测 {i+1}: {result.value} (置信度: {result.confidence:.2f})")
    
    # 演示4: 模型信息
    print("\n4. 模型信息")
    print("-" * 40)
    
    model_info = adapter1.get_model_info()
    print("* 模型信息:")
    for key, value in model_info.items():
        print(f"  - {key}: {value}")
    
    print("\n" + "=" * 60)
    print("演示完成！")
    print("=" * 60)


def demo_real_world_usage():
    """演示真实世界使用场景"""
    print("\n" + "=" * 60)
    print("真实世界使用场景演示")
    print("=" * 60)
    
    print("\n* 场景: 将现有算法适配到新架构")
    print("* 步骤:")
    print("  1. 创建旧预测器实例")
    print("  2. 使用适配器包装")
    print("  3. 注册到预测器工厂")
    print("  4. 在新系统中使用")
    
    # 模拟真实使用
    sample_data = create_sample_data()
    
    # 创建多个旧预测器
    predictors = [
        MockLegacyPredictor("增强贝叶斯预测器"),
        MockLegacyPredictor("优化马尔科夫预测器"),
        AnotherLegacyPredictor("智能集成预测器")
    ]
    
    # 适配所有预测器
    adapted_predictors = []
    for predictor in predictors:
        adapter = create_legacy_adapter(
            predictor, 
            f"适配的{predictor.name}", 
            "2.0"
        )
        adapted_predictors.append(adapter)
        print(f"* 适配完成: {adapter.name}")
    
    # 统一使用新接口
    print("\n* 使用统一接口进行预测:")
    for adapter in adapted_predictors:
        result = adapter.predict(sample_data, 3)
        print(f"  - {adapter.name}: {result.value} (置信度: {result.confidence:.2f})")
    
    print("\n* 优势:")
    print("  - 无需修改原有算法代码")
    print("  - 保持向后兼容性")
    print("  - 统一接口便于管理")
    print("  - 支持渐进式迁移")


if __name__ == "__main__":
    try:
        demo_legacy_adapter()
        demo_real_world_usage()
    except Exception as e:
        print(f"* 演示失败: {e}")
        import traceback
        traceback.print_exc()
