"""
贝叶斯选择器模块
使用贝叶斯公式结合多个状态概率选择最可能的状态
"""

import numpy as np
from typing import Dict, List, Tuple
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.utils.utils import get_all_red_states, get_all_blue_states


class BayesSelector:
    """贝叶斯状态选择器"""
    
    def __init__(self, feature_type: str):
        """
        初始化贝叶斯选择器
        
        Args:
            feature_type: 特征类型 ('red' 或 'blue')
        """
        self.feature_type = feature_type
        if feature_type == 'red':
            self.states = get_all_red_states()
        else:
            self.states = get_all_blue_states()
        
        self.prior_probabilities = {}
        self.is_initialized = False
    
    def set_prior_probabilities(self, state_frequencies: Dict[str, float],
                               recent_frequencies: Dict[str, float] = None,
                               recent_weight: float = 0.3) -> None:
        """
        设置先验概率（支持近期数据加权）

        Args:
            state_frequencies: 历史状态频率字典
            recent_frequencies: 近期状态频率字典
            recent_weight: 近期数据的权重
        """
        self.prior_probabilities = state_frequencies.copy()

        # 如果有近期频率数据，进行加权融合
        if recent_frequencies:
            for state in self.states:
                historical_prob = state_frequencies.get(state, 1.0 / len(self.states))
                recent_prob = recent_frequencies.get(state, 1.0 / len(self.states))

                # 加权平均
                self.prior_probabilities[state] = (
                    (1 - recent_weight) * historical_prob +
                    recent_weight * recent_prob
                )

        # 确保所有状态都有先验概率
        for state in self.states:
            if state not in self.prior_probabilities:
                self.prior_probabilities[state] = 1.0 / len(self.states)

        # 归一化先验概率
        total_prob = sum(self.prior_probabilities.values())
        if total_prob > 0:
            for state in self.prior_probabilities:
                self.prior_probabilities[state] /= total_prob

        self.is_initialized = True
    
    def calculate_posterior_probabilities(self,
                                        likelihood_sources: List[Dict[str, float]],
                                        weights: List[float] = None,
                                        confidence_scores: List[float] = None) -> Dict[str, float]:
        """
        计算后验概率（增强版）

        Args:
            likelihood_sources: 似然概率源列表（如马尔科夫模型输出）
            weights: 各个源的权重，如果为None则使用均等权重
            confidence_scores: 各个源的置信度分数

        Returns:
            Dict[str, float]: 后验概率字典
        """
        if not self.is_initialized:
            raise ValueError("贝叶斯选择器尚未初始化先验概率")

        if not likelihood_sources:
            return self.prior_probabilities.copy()

        if weights is None:
            weights = [1.0] * len(likelihood_sources)

        if len(weights) != len(likelihood_sources):
            raise ValueError("权重数量与似然源数量不匹配")

        # 如果有置信度分数，调整权重
        if confidence_scores:
            if len(confidence_scores) == len(weights):
                weights = [w * c for w, c in zip(weights, confidence_scores)]

        # 归一化权重
        total_weight = sum(weights)
        if total_weight > 0:
            weights = [w / total_weight for w in weights]
        else:
            weights = [1.0 / len(weights)] * len(weights)

        # 计算加权似然概率（使用对数空间）
        log_likelihoods = {}
        for state in self.states:
            log_likelihoods[state] = 0.0

            for i, likelihood_dict in enumerate(likelihood_sources):
                likelihood = likelihood_dict.get(state, 1.0 / len(self.states))
                # 使用对数空间避免数值下溢
                if likelihood > 0:
                    log_likelihoods[state] += weights[i] * np.log(likelihood)
                else:
                    log_likelihoods[state] += weights[i] * np.log(1e-10)

        # 转换回概率空间
        max_log_likelihood = max(log_likelihoods.values())
        weighted_likelihoods = {}
        for state in self.states:
            weighted_likelihoods[state] = np.exp(log_likelihoods[state] - max_log_likelihood)

        # 计算后验概率：P(state|evidence) ∝ P(evidence|state) * P(state)
        posterior_probabilities = {}
        for state in self.states:
            prior = self.prior_probabilities.get(state, 1.0 / len(self.states))
            likelihood = weighted_likelihoods[state]
            posterior_probabilities[state] = likelihood * prior

        # 归一化后验概率
        total_posterior = sum(posterior_probabilities.values())
        if total_posterior > 0:
            for state in posterior_probabilities:
                posterior_probabilities[state] /= total_posterior
        else:
            # 如果所有概率都为0，使用均匀分布
            uniform_prob = 1.0 / len(self.states)
            posterior_probabilities = {state: uniform_prob for state in self.states}

        return posterior_probabilities
    
    def select_best_state(self,
                         likelihood_sources: List[Dict[str, float]],
                         weights: List[float] = None,
                         confidence_scores: List[float] = None) -> Tuple[str, float]:
        """
        选择最佳状态

        Args:
            likelihood_sources: 似然概率源列表
            weights: 各个源的权重
            confidence_scores: 各个源的置信度分数

        Returns:
            Tuple[str, float]: (最佳状态, 概率)
        """
        posterior_probs = self.calculate_posterior_probabilities(
            likelihood_sources, weights, confidence_scores
        )

        # 选择概率最大的状态（确定性输出）
        best_state = max(posterior_probs.keys(), key=lambda x: posterior_probs[x])
        best_probability = posterior_probs[best_state]

        return best_state, best_probability
    
    def get_top_k_states(self,
                        likelihood_sources: List[Dict[str, float]],
                        k: int = 3,
                        weights: List[float] = None,
                        confidence_scores: List[float] = None) -> List[Tuple[str, float]]:
        """
        获取前k个最可能的状态

        Args:
            likelihood_sources: 似然概率源列表
            k: 返回的状态数量
            weights: 各个源的权重
            confidence_scores: 各个源的置信度分数

        Returns:
            List[Tuple[str, float]]: 前k个状态及其概率
        """
        posterior_probs = self.calculate_posterior_probabilities(
            likelihood_sources, weights, confidence_scores
        )

        # 按概率降序排序
        sorted_states = sorted(posterior_probs.items(), key=lambda x: x[1], reverse=True)

        return sorted_states[:k]
    
    def update_prior_with_evidence(self, 
                                  new_evidence: Dict[str, float],
                                  learning_rate: float = 0.1) -> None:
        """
        使用新证据更新先验概率
        
        Args:
            new_evidence: 新的证据（状态频率）
            learning_rate: 学习率
        """
        if not self.is_initialized:
            self.set_prior_probabilities(new_evidence)
            return
        
        # 使用指数移动平均更新先验概率
        for state in self.states:
            old_prior = self.prior_probabilities.get(state, 1.0 / len(self.states))
            new_evidence_prob = new_evidence.get(state, 1.0 / len(self.states))
            
            self.prior_probabilities[state] = (
                (1 - learning_rate) * old_prior + 
                learning_rate * new_evidence_prob
            )
        
        # 重新归一化
        total_prob = sum(self.prior_probabilities.values())
        if total_prob > 0:
            for state in self.prior_probabilities:
                self.prior_probabilities[state] /= total_prob
    
    def get_prior_probabilities(self) -> Dict[str, float]:
        """
        获取先验概率
        
        Returns:
            Dict[str, float]: 先验概率字典
        """
        return self.prior_probabilities.copy()
    
    def calculate_information_gain(self, 
                                  likelihood_sources: List[Dict[str, float]]) -> float:
        """
        计算信息增益（衡量证据的价值）
        
        Args:
            likelihood_sources: 似然概率源列表
            
        Returns:
            float: 信息增益值
        """
        if not self.is_initialized:
            return 0.0
        
        # 计算先验熵
        prior_entropy = -sum(p * np.log2(p + 1e-10) for p in self.prior_probabilities.values())
        
        # 计算后验熵
        posterior_probs = self.calculate_posterior_probabilities(likelihood_sources)
        posterior_entropy = -sum(p * np.log2(p + 1e-10) for p in posterior_probs.values())
        
        # 信息增益 = 先验熵 - 后验熵
        return prior_entropy - posterior_entropy
