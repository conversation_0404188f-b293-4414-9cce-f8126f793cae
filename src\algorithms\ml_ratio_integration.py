"""
机器学习比值预测集成模块
将ML比值预测器集成到主系统中
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
import os
import warnings
warnings.filterwarnings('ignore')

from .ml_ratio_predictor import MLRatioPredictor
from .time_series_validator import TimeSeriesValidator

class MLRatioIntegration:
    """机器学习比值预测集成器 - 优化版本"""

    def __init__(self, model_type: str = 'xgboost', lookback_periods: int = 30):
        """
        初始化ML比值预测集成器

        Args:
            model_type: 模型类型 ('xgboost', 'lightgbm', 'random_forest', 'ensemble')
            lookback_periods: 回看期数
        """
        self.model_type = model_type
        self.lookback_periods = lookback_periods
        self.predictor = MLRatioPredictor(model_type, lookback_periods)
        self.validator = TimeSeriesValidator()
        self.is_trained = False
        self.performance_metrics = {}
        self.feature_importance = {}
        self.prediction_confidence_threshold = 0.6  # 置信度阈值
        
    def train_and_validate(self, data: pd.DataFrame, validation_periods: int = 10) -> Dict[str, Any]:
        """
        训练并验证模型
        
        Args:
            data: 训练数据
            validation_periods: 验证期数
            
        Returns:
            训练和验证结果
        """
        print(f"🤖 开始训练和验证ML比值预测器...")
        
        # 进行时间序列验证
        validation_results = self.validator.validate_time_series_split(
            data, 
            self.predictor, 
            validation_periods=validation_periods,
            min_train_periods=50
        )
        
        # 使用全部数据训练最终模型
        print(f"[循环] 使用全部数据训练最终模型...")
        final_performance = self.predictor.train(data, min_history=30)
        
        self.is_trained = True
        self.performance_metrics = {
            'validation_results': validation_results,
            'final_training_performance': final_performance
        }
        
        # 打印性能摘要
        self._print_performance_summary()
        
        return self.performance_metrics
    
    def predict_next_period_ratios(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        预测下一期的比值
        
        Args:
            data: 历史数据
            
        Returns:
            预测结果
        """
        if not self.is_trained:
            print("[警告] 模型尚未训练，正在使用当前数据进行训练...")
            self.predictor.train(data, min_history=30)
            self.is_trained = True
        
        # 预测下一期
        prediction_result = self.predictor.predict_ratios(data, len(data))
        
        # 格式化输出
        formatted_result = {
            'red_odd_even_ratio': prediction_result['predictions']['red_odd_even'],
            'red_size_ratio': prediction_result['predictions']['red_size'],
            'blue_size_ratio': prediction_result['predictions']['blue_size'],
            'confidences': {
                'red_odd_even': prediction_result['confidences']['red_odd_even'],
                'red_size': prediction_result['confidences']['red_size'],
                'blue_size': prediction_result['confidences']['blue_size']
            },
            'average_confidence': np.mean(list(prediction_result['confidences'].values())),
            'model_type': self.model_type,
            'features_used': prediction_result['features_used']
        }
        
        return formatted_result
    
    def get_performance_summary(self) -> Dict[str, float]:
        """获取性能摘要"""
        if not self.performance_metrics:
            return {}
        
        validation_results = self.performance_metrics.get('validation_results', {})
        
        return {
            'red_odd_even_accuracy': validation_results.get('red_odd_even_accuracy', 0),
            'red_size_accuracy': validation_results.get('red_size_accuracy', 0),
            'blue_size_accuracy': validation_results.get('blue_size_accuracy', 0),
            'average_accuracy': validation_results.get('average_accuracy', 0),
            'total_periods_tested': len(validation_results.get('period_results', []))
        }
    
    def _print_performance_summary(self):
        """打印性能摘要"""
        if not self.performance_metrics:
            return
        
        validation_results = self.performance_metrics.get('validation_results', {})
        final_performance = self.performance_metrics.get('final_training_performance', {})
        
        print("\n" + "="*60)
        print("[精准] ML比值预测器性能摘要")
        print("="*60)
        
        # 验证性能
        print(f"[数据] 验证性能 (模型: {self.model_type}):")
        print(f"  红球奇偶比准确率: {validation_results.get('red_odd_even_accuracy', 0):.1%}")
        print(f"  红球大小比准确率: {validation_results.get('red_size_accuracy', 0):.1%}")
        print(f"  蓝球大小比准确率: {validation_results.get('blue_size_accuracy', 0):.1%}")
        print(f"  平均准确率: {validation_results.get('average_accuracy', 0):.1%}")
        
        # 训练性能
        print(f"\n[循环] 最终训练性能:")
        for target_name, metrics in final_performance.items():
            cv_mean = metrics.get('cv_mean', 0)
            train_acc = metrics.get('train_accuracy', 0)
            print(f"  {target_name}: CV={cv_mean:.3f}, 训练={train_acc:.3f}")
        
        # 性能评估
        avg_accuracy = validation_results.get('average_accuracy', 0)
        if avg_accuracy >= 0.5:
            status = "🟢 优秀"
        elif avg_accuracy >= 0.4:
            status = "🟡 良好"
        elif avg_accuracy >= 0.3:
            status = "🟠 一般"
        else:
            status = "🔴 需要改进"
        
        print(f"\n[提升] 总体评估: {status} (平均准确率: {avg_accuracy:.1%})")
        print("="*60)
    
    def save_model(self, save_dir: str = "models/ml_ratio"):
        """保存训练好的模型"""
        if not self.is_trained:
            print("[失败] 模型尚未训练，无法保存")
            return
        
        self.predictor.save_models(save_dir)
        
        # 保存性能指标
        import json
        metrics_path = os.path.join(save_dir, "performance_metrics.json")
        with open(metrics_path, 'w', encoding='utf-8') as f:
            # 转换numpy类型为Python原生类型
            serializable_metrics = self._make_serializable(self.performance_metrics)
            json.dump(serializable_metrics, f, ensure_ascii=False, indent=2)
        
        print(f"[成功] 性能指标已保存到: {metrics_path}")
    
    def load_model(self, save_dir: str = "models/ml_ratio"):
        """加载训练好的模型"""
        try:
            self.predictor.load_models(save_dir)
            
            # 加载性能指标
            import json
            metrics_path = os.path.join(save_dir, "performance_metrics.json")
            if os.path.exists(metrics_path):
                with open(metrics_path, 'r', encoding='utf-8') as f:
                    self.performance_metrics = json.load(f)
            
            self.is_trained = True
            print(f"[成功] ML比值预测器已从 {save_dir} 加载")
            
        except Exception as e:
            print(f"[失败] 模型加载失败: {str(e)}")
    
    def _make_serializable(self, obj):
        """将对象转换为可序列化的格式"""
        if isinstance(obj, dict):
            return {key: self._make_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, (np.int64, np.int32)):
            return int(obj)
        elif isinstance(obj, (np.float64, np.float32)):
            return float(obj)
        elif isinstance(obj, np.bool_):
            return bool(obj)
        else:
            return obj
    
    def compare_with_baseline(self, data: pd.DataFrame, baseline_accuracy: Dict[str, float]) -> Dict[str, Any]:
        """
        与基线方法比较性能
        
        Args:
            data: 测试数据
            baseline_accuracy: 基线准确率
            
        Returns:
            比较结果
        """
        if not self.performance_metrics:
            print("[失败] 没有性能指标可比较")
            return {}
        
        ml_performance = self.get_performance_summary()
        
        comparison = {}
        for metric in ['red_odd_even_accuracy', 'red_size_accuracy', 'blue_size_accuracy', 'average_accuracy']:
            ml_acc = ml_performance.get(metric, 0)
            baseline_acc = baseline_accuracy.get(metric, 0)
            improvement = ml_acc - baseline_acc
            
            comparison[metric] = {
                'ml_accuracy': ml_acc,
                'baseline_accuracy': baseline_acc,
                'improvement': improvement,
                'improvement_percentage': (improvement / baseline_acc * 100) if baseline_acc > 0 else 0
            }
        
        # 打印比较结果
        print("\n" + "="*70)
        print("[数据] ML vs 基线方法性能比较")
        print("="*70)
        print(f"{'指标':<15} {'ML准确率':<12} {'基线准确率':<12} {'改进':<10} {'改进率':<10}")
        print("-"*70)
        
        for metric, results in comparison.items():
            metric_name = metric.replace('_accuracy', '').replace('_', ' ')
            ml_acc = results['ml_accuracy']
            baseline_acc = results['baseline_accuracy']
            improvement = results['improvement']
            improvement_pct = results['improvement_percentage']
            
            status = "🟢" if improvement > 0 else "🔴" if improvement < 0 else "🟡"
            
            print(f"{metric_name:<15} {ml_acc:<12.1%} {baseline_acc:<12.1%} {improvement:<10.1%} {status}{improvement_pct:<9.1f}%")
        
        print("="*70)
        
        return comparison

def create_ml_ratio_predictor(model_type: str = 'random_forest') -> MLRatioIntegration:
    """
    创建ML比值预测器的便捷函数
    
    Args:
        model_type: 模型类型
        
    Returns:
        ML比值预测集成器
    """
    return MLRatioIntegration(model_type=model_type)
