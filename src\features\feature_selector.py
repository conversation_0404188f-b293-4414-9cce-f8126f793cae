"""
特征选择器
使用多种方法分析特征重要性，去除噪声特征
"""

import numpy as np
import pandas as pd
from typing import List, Tuple, Dict, Optional
from collections import Counter
import json
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.utils.utils import parse_numbers, load_data


class FeatureSelector:
    """特征选择器"""
    
    def __init__(self):
        """初始化特征选择器"""
        self.feature_importance_scores = {}
        self.selected_features = {}
        self.feature_names = []
        
    def analyze_feature_importance(self, X: np.ndarray, y: np.ndarray, 
                                 feature_names: List[str], task_name: str) -> Dict[str, float]:
        """
        分析特征重要性
        
        Args:
            X: 特征矩阵
            y: 标签
            feature_names: 特征名称列表
            task_name: 任务名称
            
        Returns:
            Dict[str, float]: 特征重要性分数
        """
        print(f"分析 {task_name} 的特征重要性...")
        
        importance_scores = {}
        
        # 方法1: 相关性分析
        correlation_scores = self._correlation_analysis(X, y, feature_names)
        
        # 方法2: 方差分析
        variance_scores = self._variance_analysis(X, feature_names)
        
        # 方法3: 互信息分析
        mutual_info_scores = self._mutual_information_analysis(X, y, feature_names)
        
        # 方法4: 递归特征消除
        rfe_scores = self._recursive_feature_elimination(X, y, feature_names)
        
        # 综合评分
        for feature in feature_names:
            combined_score = (
                correlation_scores.get(feature, 0) * 0.3 +
                variance_scores.get(feature, 0) * 0.2 +
                mutual_info_scores.get(feature, 0) * 0.3 +
                rfe_scores.get(feature, 0) * 0.2
            )
            importance_scores[feature] = combined_score
        
        self.feature_importance_scores[task_name] = importance_scores
        
        print(f"  完成特征重要性分析，共 {len(feature_names)} 个特征")
        return importance_scores
    
    def _correlation_analysis(self, X: np.ndarray, y: np.ndarray, 
                            feature_names: List[str]) -> Dict[str, float]:
        """相关性分析"""
        correlation_scores = {}
        
        # 对于分类任务，计算每个特征与目标的相关性
        if len(y.shape) > 1:  # one-hot编码的标签
            y_numeric = np.argmax(y, axis=1)
        else:
            y_numeric = y
        
        for i, feature_name in enumerate(feature_names):
            if i < X.shape[1]:
                # 计算皮尔逊相关系数
                correlation = np.corrcoef(X[:, i], y_numeric)[0, 1]
                correlation_scores[feature_name] = abs(correlation) if not np.isnan(correlation) else 0
        
        return correlation_scores
    
    def _variance_analysis(self, X: np.ndarray, feature_names: List[str]) -> Dict[str, float]:
        """方差分析"""
        variance_scores = {}
        
        for i, feature_name in enumerate(feature_names):
            if i < X.shape[1]:
                # 计算特征方差（标准化后）
                feature_var = np.var(X[:, i])
                variance_scores[feature_name] = feature_var
        
        # 归一化方差分数
        max_var = max(variance_scores.values()) if variance_scores else 1
        for feature in variance_scores:
            variance_scores[feature] /= max_var
        
        return variance_scores
    
    def _mutual_information_analysis(self, X: np.ndarray, y: np.ndarray, 
                                   feature_names: List[str]) -> Dict[str, float]:
        """互信息分析（简化实现）"""
        mutual_info_scores = {}
        
        if len(y.shape) > 1:  # one-hot编码的标签
            y_numeric = np.argmax(y, axis=1)
        else:
            y_numeric = y
        
        for i, feature_name in enumerate(feature_names):
            if i < X.shape[1]:
                # 简化的互信息计算
                feature_values = X[:, i]
                
                # 离散化特征值
                feature_bins = np.histogram_bin_edges(feature_values, bins=10)
                feature_discrete = np.digitize(feature_values, feature_bins)
                
                # 计算互信息
                mi_score = self._calculate_mutual_information(feature_discrete, y_numeric)
                mutual_info_scores[feature_name] = mi_score
        
        return mutual_info_scores
    
    def _calculate_mutual_information(self, x: np.ndarray, y: np.ndarray) -> float:
        """计算互信息"""
        # 简化的互信息计算
        x_counter = Counter(x)
        y_counter = Counter(y)
        xy_counter = Counter(zip(x, y))
        
        total = len(x)
        mi = 0.0
        
        for xy, xy_count in xy_counter.items():
            x_val, y_val = xy
            px = x_counter[x_val] / total
            py = y_counter[y_val] / total
            pxy = xy_count / total
            
            if pxy > 0 and px > 0 and py > 0:
                mi += pxy * np.log2(pxy / (px * py))
        
        return max(0, mi)  # 确保非负
    
    def _recursive_feature_elimination(self, X: np.ndarray, y: np.ndarray, 
                                     feature_names: List[str]) -> Dict[str, float]:
        """递归特征消除（简化实现）"""
        rfe_scores = {}
        
        # 简化的RFE：基于线性回归系数
        if len(y.shape) > 1:  # one-hot编码
            y_numeric = np.argmax(y, axis=1)
        else:
            y_numeric = y
        
        # 添加偏置项
        X_with_bias = np.column_stack([np.ones(X.shape[0]), X])
        
        try:
            # 使用最小二乘法
            coefficients = np.linalg.lstsq(X_with_bias, y_numeric, rcond=None)[0]
            
            # 跳过偏置项，获取特征系数
            feature_coeffs = coefficients[1:]
            
            for i, feature_name in enumerate(feature_names):
                if i < len(feature_coeffs):
                    rfe_scores[feature_name] = abs(feature_coeffs[i])
        except:
            # 如果线性回归失败，使用默认分数
            for feature_name in feature_names:
                rfe_scores[feature_name] = 0.5
        
        # 归一化分数
        max_score = max(rfe_scores.values()) if rfe_scores else 1
        for feature in rfe_scores:
            rfe_scores[feature] /= max_score
        
        return rfe_scores
    
    def select_top_features(self, task_name: str, top_k: int = 50, 
                          min_score: float = 0.1) -> List[str]:
        """
        选择顶级特征
        
        Args:
            task_name: 任务名称
            top_k: 选择前k个特征
            min_score: 最小重要性分数
            
        Returns:
            List[str]: 选中的特征名称
        """
        if task_name not in self.feature_importance_scores:
            return []
        
        importance_scores = self.feature_importance_scores[task_name]
        
        # 按重要性排序
        sorted_features = sorted(importance_scores.items(), 
                               key=lambda x: x[1], reverse=True)
        
        # 选择top_k个且分数大于min_score的特征
        selected = []
        for feature_name, score in sorted_features:
            if len(selected) >= top_k:
                break
            if score >= min_score:
                selected.append(feature_name)
        
        self.selected_features[task_name] = selected
        
        print(f"为任务 {task_name} 选择了 {len(selected)} 个特征")
        print(f"  前10个特征: {selected[:10]}")
        
        return selected
    
    def get_feature_importance_report(self, task_name: str) -> Dict:
        """获取特征重要性报告"""
        if task_name not in self.feature_importance_scores:
            return {}
        
        importance_scores = self.feature_importance_scores[task_name]
        sorted_features = sorted(importance_scores.items(), 
                               key=lambda x: x[1], reverse=True)
        
        report = {
            'task_name': task_name,
            'total_features': len(importance_scores),
            'selected_features': len(self.selected_features.get(task_name, [])),
            'top_10_features': sorted_features[:10],
            'bottom_10_features': sorted_features[-10:],
            'score_distribution': {
                'max': max(importance_scores.values()),
                'min': min(importance_scores.values()),
                'mean': np.mean(list(importance_scores.values())),
                'std': np.std(list(importance_scores.values()))
            }
        }
        
        return report
    
    def filter_features(self, X: np.ndarray, feature_names: List[str], 
                       task_name: str) -> Tuple[np.ndarray, List[str]]:
        """
        根据选择的特征过滤数据
        
        Args:
            X: 原始特征矩阵
            feature_names: 原始特征名称
            task_name: 任务名称
            
        Returns:
            Tuple[np.ndarray, List[str]]: 过滤后的特征矩阵和特征名称
        """
        if task_name not in self.selected_features:
            return X, feature_names
        
        selected_features = self.selected_features[task_name]
        
        # 找到选中特征的索引
        selected_indices = []
        filtered_names = []
        
        for i, feature_name in enumerate(feature_names):
            if feature_name in selected_features and i < X.shape[1]:
                selected_indices.append(i)
                filtered_names.append(feature_name)
        
        # 过滤特征矩阵
        if selected_indices:
            X_filtered = X[:, selected_indices]
        else:
            X_filtered = X
            filtered_names = feature_names
        
        print(f"特征过滤: {X.shape[1]} -> {X_filtered.shape[1]} 个特征")
        
        return X_filtered, filtered_names
    
    def save_feature_selection_results(self, filepath: str):
        """保存特征选择结果"""
        results = {
            'feature_importance_scores': self.feature_importance_scores,
            'selected_features': self.selected_features
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"特征选择结果已保存到 {filepath}")
    
    def load_feature_selection_results(self, filepath: str) -> bool:
        """加载特征选择结果"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                results = json.load(f)
            
            self.feature_importance_scores = results.get('feature_importance_scores', {})
            self.selected_features = results.get('selected_features', {})
            
            print(f"特征选择结果已从 {filepath} 加载")
            return True
        except Exception as e:
            print(f"加载特征选择结果失败: {e}")
            return False


class AdvancedFeatureEngineering:
    """高级特征工程"""
    
    def __init__(self):
        """初始化特征工程器"""
        self.feature_transformers = {}
    
    def create_interaction_features(self, X: np.ndarray, feature_names: List[str], 
                                  max_interactions: int = 20) -> Tuple[np.ndarray, List[str]]:
        """
        创建交互特征
        
        Args:
            X: 原始特征矩阵
            feature_names: 特征名称
            max_interactions: 最大交互特征数
            
        Returns:
            Tuple[np.ndarray, List[str]]: 增强后的特征矩阵和名称
        """
        print("创建交互特征...")
        
        interaction_features = []
        interaction_names = []
        
        # 选择重要特征进行交互
        n_features = min(10, X.shape[1])  # 最多选择10个特征
        
        for i in range(n_features):
            for j in range(i + 1, n_features):
                if len(interaction_features) >= max_interactions:
                    break
                
                # 创建乘积交互特征
                interaction = X[:, i] * X[:, j]
                interaction_features.append(interaction)
                interaction_names.append(f"{feature_names[i]}_x_{feature_names[j]}")
        
        if interaction_features:
            interaction_matrix = np.column_stack(interaction_features)
            X_enhanced = np.column_stack([X, interaction_matrix])
            enhanced_names = feature_names + interaction_names
        else:
            X_enhanced = X
            enhanced_names = feature_names
        
        print(f"  创建了 {len(interaction_features)} 个交互特征")
        return X_enhanced, enhanced_names
    
    def create_polynomial_features(self, X: np.ndarray, feature_names: List[str], 
                                 degree: int = 2, max_poly_features: int = 15) -> Tuple[np.ndarray, List[str]]:
        """
        创建多项式特征
        
        Args:
            X: 原始特征矩阵
            feature_names: 特征名称
            degree: 多项式度数
            max_poly_features: 最大多项式特征数
            
        Returns:
            Tuple[np.ndarray, List[str]]: 增强后的特征矩阵和名称
        """
        print(f"创建 {degree} 次多项式特征...")
        
        poly_features = []
        poly_names = []
        
        # 选择前几个重要特征创建多项式
        n_features = min(max_poly_features, X.shape[1])
        
        for i in range(n_features):
            for d in range(2, degree + 1):
                poly_feature = np.power(X[:, i], d)
                poly_features.append(poly_feature)
                poly_names.append(f"{feature_names[i]}^{d}")
        
        if poly_features:
            poly_matrix = np.column_stack(poly_features)
            X_enhanced = np.column_stack([X, poly_matrix])
            enhanced_names = feature_names + poly_names
        else:
            X_enhanced = X
            enhanced_names = feature_names
        
        print(f"  创建了 {len(poly_features)} 个多项式特征")
        return X_enhanced, enhanced_names
    
    def create_statistical_features(self, X: np.ndarray, feature_names: List[str], 
                                  window_size: int = 5) -> Tuple[np.ndarray, List[str]]:
        """
        创建统计特征
        
        Args:
            X: 原始特征矩阵
            feature_names: 特征名称
            window_size: 滑动窗口大小
            
        Returns:
            Tuple[np.ndarray, List[str]]: 增强后的特征矩阵和名称
        """
        print("创建统计特征...")
        
        stat_features = []
        stat_names = []
        
        # 选择前几个特征创建统计特征
        n_features = min(10, X.shape[1])
        
        for i in range(n_features):
            feature_data = X[:, i]
            
            # 滑动窗口统计
            rolling_mean = self._rolling_statistic(feature_data, window_size, np.mean)
            rolling_std = self._rolling_statistic(feature_data, window_size, np.std)
            rolling_max = self._rolling_statistic(feature_data, window_size, np.max)
            rolling_min = self._rolling_statistic(feature_data, window_size, np.min)
            
            stat_features.extend([rolling_mean, rolling_std, rolling_max, rolling_min])
            stat_names.extend([
                f"{feature_names[i]}_rolling_mean",
                f"{feature_names[i]}_rolling_std", 
                f"{feature_names[i]}_rolling_max",
                f"{feature_names[i]}_rolling_min"
            ])
        
        if stat_features:
            stat_matrix = np.column_stack(stat_features)
            X_enhanced = np.column_stack([X, stat_matrix])
            enhanced_names = feature_names + stat_names
        else:
            X_enhanced = X
            enhanced_names = feature_names
        
        print(f"  创建了 {len(stat_features)} 个统计特征")
        return X_enhanced, enhanced_names
    
    def _rolling_statistic(self, data: np.ndarray, window_size: int, 
                          stat_func) -> np.ndarray:
        """计算滑动窗口统计"""
        result = np.zeros_like(data)
        
        for i in range(len(data)):
            start_idx = max(0, i - window_size + 1)
            window_data = data[start_idx:i + 1]
            result[i] = stat_func(window_data)
        
        return result


def test_feature_selector():
    """测试特征选择器"""
    # 创建模拟数据
    np.random.seed(42)
    n_samples, n_features = 100, 50
    X = np.random.randn(n_samples, n_features)
    y = np.random.randint(0, 3, n_samples)
    y_onehot = np.eye(3)[y]
    
    feature_names = [f"feature_{i}" for i in range(n_features)]
    
    # 测试特征选择器
    selector = FeatureSelector()
    
    # 分析特征重要性
    importance_scores = selector.analyze_feature_importance(
        X, y_onehot, feature_names, "test_task"
    )
    
    # 选择顶级特征
    selected_features = selector.select_top_features("test_task", top_k=20)
    
    # 过滤特征
    X_filtered, filtered_names = selector.filter_features(X, feature_names, "test_task")
    
    print(f"\n原始特征数: {X.shape[1]}")
    print(f"过滤后特征数: {X_filtered.shape[1]}")
    
    # 获取报告
    report = selector.get_feature_importance_report("test_task")
    print(f"\n特征重要性报告:")
    print(f"  总特征数: {report['total_features']}")
    print(f"  选中特征数: {report['selected_features']}")
    print(f"  分数分布: {report['score_distribution']}")


if __name__ == "__main__":
    test_feature_selector()
