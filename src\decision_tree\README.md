# 决策树预测模块

## 概述

决策树预测模块提供基于决策树算法的号码预测功能，专门用于预测双色球的奇偶比和大小比。该模块完全集成到统一回测框架中，支持标准化的预测和评估流程。

## 核心特性

- **多种决策树预测器**：提供专门针对奇偶比和大小比的预测器
- **集成学习**：支持多个决策树模型的集成预测
- **统一接口**：完全兼容统一回测框架的PredictorInterface
- **特征工程**：自动提取历史统计特征
- **灵活配置**：支持决策树参数的自定义配置

## 模块结构

```
src/decision_tree/
├── __init__.py                    # 模块初始化
├── decision_tree_predictor.py     # 基础决策树预测器
├── odd_even_tree.py              # 奇偶比决策树预测器
├── size_ratio_tree.py            # 大小比决策树预测器
├── tree_ensemble.py              # 决策树集成预测器
├── example_usage.py              # 使用示例
└── README.md                     # 本文档
```

## 预测器类型

### 1. OddEvenTreePredictor

专门用于预测红球奇偶比的决策树模型。

**特点：**
- 重点关注奇偶比预测
- 优先满足奇偶比要求生成号码
- 辅助考虑大小比分布

**使用场景：**
- 当奇偶比预测是主要关注点时
- 需要稳定的奇偶比预测时

### 2. SizeRatioTreePredictor

专门用于预测红球大小比的决策树模型。

**特点：**
- 重点关注大小比预测
- 优先满足大小比要求生成号码
- 辅助考虑奇偶比分布

**使用场景：**
- 当大小比预测是主要关注点时
- 需要精确的大小比控制时

### 3. TreeEnsemblePredictor

集成多个决策树模型的预测器。

**特点：**
- 结合多个不同配置的决策树
- 支持多种投票策略（majority, weighted, average）
- 提供更稳定和鲁棒的预测结果

**使用场景：**
- 需要高精度预测时
- 希望降低单一模型风险时
- 追求预测稳定性时

## 特征工程

### 基础特征
- 当期奇偶比
- 当期大小比
- 当期蓝球大小

### 历史统计特征
- 近5期奇偶比分布统计
- 近5期大小比分布统计
- 近5期蓝球大小分布统计

### 特征处理
- 自动处理缺失值
- 标签编码分类变量
- 特征标准化

## 使用方法

### 基本使用

```python
from src.decision_tree import OddEvenTreePredictor
from src.framework.backtest_framework import BacktestFramework
from src.framework.data_models import BacktestConfig

# 创建预测器
predictor = OddEvenTreePredictor(
    max_depth=10,
    min_samples_split=5,
    random_state=42
)

# 配置回测
config = BacktestConfig(
    num_periods=10,
    display_periods=10
)

# 运行回测
framework = BacktestFramework(config)
results = framework.run_backtest(predictor, data)
framework.display_results(results)
```

### 集成预测器使用

```python
from src.decision_tree import TreeEnsemblePredictor

# 创建集成预测器
predictor = TreeEnsemblePredictor(
    ensemble_size=5,
    voting_strategy='weighted',
    random_state=42
)

# 查看集成信息
info = predictor.get_ensemble_info()
print(f"集成配置: {info}")

# 运行回测（同基本使用）
```

### 参数配置

#### 决策树参数
- `max_depth`: 树的最大深度（默认：10）
- `min_samples_split`: 分裂所需的最小样本数（默认：5）
- `min_samples_leaf`: 叶节点的最小样本数（默认：2）
- `random_state`: 随机种子（默认：42）

#### 集成参数
- `ensemble_size`: 集成模型数量（默认：5）
- `voting_strategy`: 投票策略（'majority', 'weighted', 'average'）
- `random_state`: 随机种子

## 投票策略说明

### majority（多数投票）
- 统计预测某个结果的模型数量
- 选择得票最多的结果
- 适合模型差异较大的情况

### weighted（加权投票）
- 使用各模型的预测概率进行加权
- 考虑模型的置信度
- 推荐使用的策略

### average（平均投票）
- 简单平均所有模型的预测概率
- 计算简单，适合快速预测

## 性能优化建议

### 数据准备
1. 确保历史数据的完整性和准确性
2. 至少提供20期以上的历史数据用于训练
3. 定期更新数据以保持模型的时效性

### 参数调优
1. 根据数据量调整`max_depth`：
   - 数据量少（<50期）：max_depth=6-8
   - 数据量中等（50-200期）：max_depth=8-12
   - 数据量大（>200期）：max_depth=10-15

2. 调整`min_samples_split`避免过拟合：
   - 数据量少：min_samples_split=5-8
   - 数据量大：min_samples_split=3-5

### 集成优化
1. 集成大小建议：
   - 快速预测：ensemble_size=3-5
   - 精确预测：ensemble_size=5-10
   - 避免过大的集成（>15）以免计算开销过大

2. 投票策略选择：
   - 一般情况：使用'weighted'
   - 模型差异大：使用'majority'
   - 快速预测：使用'average'

## 注意事项

1. **数据质量**：决策树对数据质量敏感，确保输入数据的准确性
2. **特征相关性**：避免使用高度相关的特征，可能导致过拟合
3. **随机性控制**：设置固定的random_state以确保结果可重现
4. **内存使用**：大型集成可能消耗较多内存，注意监控资源使用
5. **训练时间**：集成预测器的训练时间较长，适合离线训练

## 扩展开发

### 添加新的决策树预测器

1. 继承`DecisionTreePredictor`基类
2. 实现`_generate_numbers`方法
3. 重写`get_predictor_name`和`get_predictor_version`
4. 在`__init__.py`中添加导入

```python
from .decision_tree_predictor import DecisionTreePredictor

class CustomTreePredictor(DecisionTreePredictor):
    def _generate_numbers(self, odd_even_pred, red_size_pred, blue_size_pred):
        # 自定义号码生成逻辑
        pass
    
    def get_predictor_name(self):
        return "CustomTreePredictor"
```

### 添加新的特征

在`_extract_features`方法中添加新的特征计算逻辑：

```python
def _extract_features(self, data, end_index):
    # 现有特征提取逻辑
    
    # 添加新特征
    feature_dict['新特征名'] = 计算新特征的逻辑
    
    return features_df
```

## 版本历史

- **v1.0.0**: 初始版本，包含基础决策树预测器和集成功能

## 许可证

本模块遵循项目的整体许可证协议。