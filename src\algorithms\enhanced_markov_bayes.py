#!/usr/bin/env python3
"""
增强马尔科夫-贝叶斯融合算法
优化状态空间、提升预测准确性、降低计算复杂度
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from collections import Counter, defaultdict
from src.utils.utils import parse_numbers, get_all_red_states, get_all_blue_states


class EnhancedMarkovBayesPredictor:
    """增强马尔科夫-贝叶斯融合预测器"""
    
    def __init__(self, feature_type: str = 'red'):
        """
        初始化预测器
        
        Args:
            feature_type: 特征类型 ('red' 或 'blue')
        """
        self.feature_type = feature_type
        
        # 简化状态空间
        if feature_type == 'red':
            self.all_states = get_all_red_states()
        else:
            self.all_states = get_all_blue_states()
        
        # 核心状态（高频状态）
        self.core_states = self._identify_core_states()
        
        # 1阶马尔科夫转移矩阵（简化版）
        self.transition_matrix = {}
        
        # 贝叶斯先验概率（动态更新）
        self.prior_probabilities = {}
        
        # 历史性能记录
        self.prediction_history = []
        self.accuracy_scores = []
        
        # 自适应参数
        self.learning_rate = 0.1
        self.confidence_threshold = 0.6
        
        self.is_trained = False
    
    def _identify_core_states(self) -> List[str]:
        """识别核心状态（出现频率高的状态）"""
        # 基于经验识别高频状态
        if self.feature_type == 'red':
            # 红球常见的奇偶比和大小比组合
            core_states = [
                '3:2', '2:3',  # 奇偶比
                '2:3', '3:2',  # 大小比
            ]
        else:
            # 蓝球常见的大小比
            core_states = [
                '1:1', '2:0', '0:2'  # 大小比
            ]
        
        # 过滤出实际存在的状态
        return [state for state in core_states if state in self.all_states]
    
    def train(self, data: pd.DataFrame, window_size: int = 100) -> None:
        """
        训练模型（优化版）
        
        Args:
            data: 历史数据
            window_size: 训练窗口大小
        """
        print(f"[工具] 训练增强马尔科夫-贝叶斯预测器 ({self.feature_type})")
        
        # 限制训练数据量，避免过拟合
        train_data = data.head(min(window_size, len(data)))
        
        # 提取状态序列
        state_sequence = self._extract_state_sequence(train_data)
        
        if len(state_sequence) < 10:
            print(f"[警告] 训练数据不足: {len(state_sequence)} < 10")
            return
        
        # 训练马尔科夫模型
        self._train_markov_model(state_sequence)
        
        # 训练贝叶斯先验
        self._train_bayes_prior(state_sequence)
        
        self.is_trained = True
        print(f"[成功] 训练完成: {len(state_sequence)}期数据, {len(self.transition_matrix)}个转移")
    
    def _extract_state_sequence(self, data: pd.DataFrame) -> List[str]:
        """提取状态序列"""
        states = []
        
        for _, row in data.iterrows():
            try:
                red_balls, blue_balls = parse_numbers(row)
                
                if self.feature_type == 'red':
                    # 简化红球状态：只使用奇偶比
                    odd_count = sum(1 for ball in red_balls if ball % 2 == 1)
                    even_count = len(red_balls) - odd_count
                    state = f"{odd_count}:{even_count}"
                else:
                    # 简化蓝球状态：只使用大小比
                    small_count = sum(1 for ball in blue_balls if ball <= 7)
                    big_count = len(blue_balls) - small_count
                    state = f"{small_count}:{big_count}"
                
                if state in self.all_states:
                    states.append(state)
                    
            except Exception as e:
                continue
        
        return states
    
    def _train_markov_model(self, state_sequence: List[str]) -> None:
        """训练简化的1阶马尔科夫模型"""
        # 重置转移矩阵
        self.transition_matrix = defaultdict(lambda: defaultdict(float))
        
        # 统计状态转移（从新到旧）
        for i in range(len(state_sequence) - 1):
            current_state = state_sequence[i]
            next_state = state_sequence[i + 1]
            
            # 简单计数，不使用复杂的时间衰减
            self.transition_matrix[current_state][next_state] += 1.0
        
        # 归一化概率
        for current_state in self.transition_matrix:
            total_count = sum(self.transition_matrix[current_state].values())
            if total_count > 0:
                for next_state in self.transition_matrix[current_state]:
                    self.transition_matrix[current_state][next_state] /= total_count
    
    def _train_bayes_prior(self, state_sequence: List[str]) -> None:
        """训练贝叶斯先验概率"""
        # 计算状态频率
        state_counts = Counter(state_sequence)
        total_count = len(state_sequence)
        
        # 设置先验概率（加入平滑）
        smoothing = 0.1
        for state in self.all_states:
            count = state_counts.get(state, 0)
            # 拉普拉斯平滑
            self.prior_probabilities[state] = (count + smoothing) / (total_count + smoothing * len(self.all_states))
    
    def predict_next_state(self, recent_states: List[str]) -> Tuple[str, float]:
        """
        预测下一个状态（融合算法 + 动态学习）
        
        Args:
            recent_states: 最近的状态序列
            
        Returns:
            Tuple[str, float]: (预测状态, 动态置信度)
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        if not recent_states:
            # 返回最高先验概率的状态
            best_state = max(self.prior_probabilities.keys(), 
                           key=lambda x: self.prior_probabilities[x])
            dynamic_confidence = self.get_dynamic_confidence(self.prior_probabilities)
            return best_state, dynamic_confidence
        
        current_state = recent_states[-1]
        
        # 马尔科夫预测
        markov_probs = self._get_markov_probabilities(current_state)
        
        # 贝叶斯融合
        fused_probs = self._fuse_markov_bayes(markov_probs)
        
        # 选择最佳状态
        if fused_probs:
            best_state = max(fused_probs.keys(), key=lambda x: fused_probs[x])
            
            # 使用动态置信度计算
            dynamic_confidence = self.get_dynamic_confidence(fused_probs)
            
            # 记录预测历史（简化格式）
            self.prediction_history.append(best_state)
            if len(self.prediction_history) > 20:
                self.prediction_history = self.prediction_history[-20:]
            
            return best_state, dynamic_confidence
        else:
            # 回退到先验概率
            best_state = max(self.prior_probabilities.keys(), 
                           key=lambda x: self.prior_probabilities[x])
            dynamic_confidence = self.get_dynamic_confidence(self.prior_probabilities)
            return best_state, dynamic_confidence
    
    def _get_markov_probabilities(self, current_state: str) -> Dict[str, float]:
        """获取马尔科夫转移概率"""
        if current_state in self.transition_matrix:
            return dict(self.transition_matrix[current_state])
        else:
            # 如果当前状态未见过，返回均匀分布
            uniform_prob = 1.0 / len(self.all_states)
            return {state: uniform_prob for state in self.all_states}
    
    def _fuse_markov_bayes(self, markov_probs: Dict[str, float]) -> Dict[str, float]:
        """融合马尔科夫和贝叶斯概率"""
        fused_probs = {}
        
        # 自适应权重：基于历史准确率调整
        markov_weight = self._get_adaptive_markov_weight()
        bayes_weight = 1.0 - markov_weight
        
        for state in self.all_states:
            markov_prob = markov_probs.get(state, 0.0)
            bayes_prob = self.prior_probabilities.get(state, 0.0)
            
            # 加权融合
            fused_prob = markov_weight * markov_prob + bayes_weight * bayes_prob
            
            if fused_prob > 0:
                fused_probs[state] = fused_prob
        
        # 归一化
        total_prob = sum(fused_probs.values())
        if total_prob > 0:
            for state in fused_probs:
                fused_probs[state] /= total_prob
        
        return fused_probs
    
    def _get_adaptive_markov_weight(self) -> float:
        """获取自适应马尔科夫权重"""
        if len(self.accuracy_scores) < 5:
            return 0.7  # 默认权重
        
        # 基于最近的准确率调整权重
        recent_accuracy = np.mean(self.accuracy_scores[-5:])
        
        if recent_accuracy > 0.8:
            return 0.8  # 高准确率时增加马尔科夫权重
        elif recent_accuracy > 0.6:
            return 0.7  # 中等准确率时保持默认权重
        else:
            return 0.5  # 低准确率时降低马尔科夫权重
    
    def update_accuracy(self, predicted_state: str, actual_state: str) -> None:
        """更新预测准确率并实施动态学习"""
        is_correct = (predicted_state == actual_state)
        self.accuracy_scores.append(1.0 if is_correct else 0.0)
        
        # 只保留最近50次的记录
        if len(self.accuracy_scores) > 50:
            self.accuracy_scores = self.accuracy_scores[-50:]
        
        # 动态学习：根据预测结果调整模型参数
        self._dynamic_learning_update(predicted_state, actual_state, is_correct)

    def _dynamic_learning_update(self, predicted_state: str, actual_state: str, is_correct: bool) -> None:
        """
        动态学习更新机制
        
        Args:
            predicted_state: 预测状态
            actual_state: 实际状态
            is_correct: 预测是否正确
        """
        # 获取当前学习率（基于最近的准确率）
        current_accuracy = sum(self.accuracy_scores[-10:]) / min(len(self.accuracy_scores), 10)
        adaptive_learning_rate = self.learning_rate * (1.0 - current_accuracy + 0.1)
        
        # 更新先验概率
        if actual_state in self.prior_probabilities:
            if is_correct:
                # 预测正确，增强对实际状态的先验概率
                self.prior_probabilities[actual_state] += adaptive_learning_rate * 0.1
            else:
                # 预测错误，调整先验概率
                self.prior_probabilities[predicted_state] -= adaptive_learning_rate * 0.05
                self.prior_probabilities[actual_state] += adaptive_learning_rate * 0.05
        
        # 归一化先验概率
        total_prior = sum(self.prior_probabilities.values())
        if total_prior > 0:
            for state in self.prior_probabilities:
                self.prior_probabilities[state] /= total_prior
        
        # 动态调整转移矩阵
        self._update_transition_matrix_dynamically(predicted_state, actual_state, adaptive_learning_rate)
    
    def _update_transition_matrix_dynamically(self, predicted_state: str, actual_state: str, learning_rate: float) -> None:
        """
        动态更新转移矩阵
        
        Args:
            predicted_state: 预测状态
            actual_state: 实际状态
            learning_rate: 学习率
        """
        # 获取最近的状态序列
        if len(self.prediction_history) >= 2:
            prev_state = self.prediction_history[-2]
            
            # 更新转移概率
            if prev_state in self.transition_matrix and actual_state in self.transition_matrix[prev_state]:
                # 增强实际转移的概率
                self.transition_matrix[prev_state][actual_state] += learning_rate * 0.1
                
                # 如果预测错误，降低错误预测的转移概率
                if predicted_state != actual_state and predicted_state in self.transition_matrix[prev_state]:
                    self.transition_matrix[prev_state][predicted_state] -= learning_rate * 0.05
                
                # 归一化转移概率
                total_prob = sum(self.transition_matrix[prev_state].values())
                if total_prob > 0:
                    for state in self.transition_matrix[prev_state]:
                        self.transition_matrix[prev_state][state] /= total_prob
        
        # 记录当前状态到历史
        self.prediction_history.append(actual_state)
        if len(self.prediction_history) > 20:  # 只保留最近20个状态
            self.prediction_history = self.prediction_history[-20:]
    
    def get_dynamic_confidence(self, state_probabilities: Dict[str, float]) -> float:
        """
        基于动态学习计算置信度
        
        Args:
            state_probabilities: 状态概率分布
            
        Returns:
            float: 动态置信度
        """
        # 基础置信度：最高概率值
        base_confidence = max(state_probabilities.values()) if state_probabilities else 0.0
        
        # 历史准确率调整
        recent_accuracy = sum(self.accuracy_scores[-10:]) / min(len(self.accuracy_scores), 10) if self.accuracy_scores else 0.5
        
        # 状态分布的熵（不确定性）
        entropy = 0.0
        for prob in state_probabilities.values():
            if prob > 0:
                entropy -= prob * np.log2(prob)
        
        # 归一化熵（0-1之间）
        max_entropy = np.log2(len(state_probabilities)) if state_probabilities else 1.0
        normalized_entropy = entropy / max_entropy if max_entropy > 0 else 0.0
        
        # 综合置信度计算
        dynamic_confidence = (
            base_confidence * 0.4 +           # 基础概率权重
            recent_accuracy * 0.4 +           # 历史准确率权重
            (1.0 - normalized_entropy) * 0.2  # 确定性权重
        )
        
        return min(0.95, max(0.05, dynamic_confidence))  # 限制在合理范围内
    
    def get_state_probabilities(self, recent_states: List[str]) -> Dict[str, float]:
        """获取所有状态的概率分布"""
        if not self.is_trained:
            return self.prior_probabilities.copy()
        
        if not recent_states:
            return self.prior_probabilities.copy()
        
        current_state = recent_states[-1]
        markov_probs = self._get_markov_probabilities(current_state)
        return self._fuse_markov_bayes(markov_probs)
    
    def get_model_info(self) -> Dict:
        """获取模型信息"""
        return {
            'feature_type': self.feature_type,
            'is_trained': self.is_trained,
            'total_states': len(self.all_states),
            'core_states': len(self.core_states),
            'transition_count': len(self.transition_matrix),
            'prediction_count': len(self.prediction_history),
            'recent_accuracy': np.mean(self.accuracy_scores[-10:]) if self.accuracy_scores else 0.0
        }
