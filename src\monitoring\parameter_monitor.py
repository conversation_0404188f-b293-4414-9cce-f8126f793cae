"""
参数性能监控系统
实时监控贝叶斯参数的预测性能，并提供性能分析报告
"""

import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
import pandas as pd
import numpy as np


@dataclass
class PerformanceRecord:
    """性能记录数据结构"""
    timestamp: str
    period: str
    parameters: Dict
    prediction_results: Dict
    hit_rates: Dict
    score: float
    notes: str = ""


class ParameterMonitor:
    """参数性能监控器"""
    
    def __init__(self, monitor_dir: str = ".taskmaster/monitoring"):
        """
        初始化监控器
        
        Args:
            monitor_dir: 监控数据存储目录
        """
        self.monitor_dir = Path(monitor_dir)
        self.monitor_dir.mkdir(parents=True, exist_ok=True)
        
        self.performance_file = self.monitor_dir / "parameter_performance.json"
        self.daily_summary_file = self.monitor_dir / "daily_summary.json"
        self.alert_file = self.monitor_dir / "performance_alerts.json"
        
        # 性能阈值配置
        self.thresholds = {
            'bayes_hit_rate_min': 0.15,      # 贝叶斯命中率最低阈值
            'overall_hit_rate_min': 0.12,    # 整体命中率最低阈值
            'kill_success_rate_min': 0.80,   # 杀号成功率最低阈值
            'score_decline_threshold': 0.05,  # 得分下降警告阈值
            'consecutive_poor_periods': 5     # 连续表现不佳期数警告
        }
        
        # 加载历史数据
        self.performance_history = self._load_performance_history()
        
    def _load_performance_history(self) -> List[PerformanceRecord]:
        """加载历史性能数据"""
        if not self.performance_file.exists():
            return []
            
        try:
            with open(self.performance_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return [PerformanceRecord(**record) for record in data]
        except Exception as e:
            print(f"[警告] 加载性能历史失败: {e}")
            return []
    
    def record_performance(self, period: str, parameters: Dict, 
                          prediction_results: Dict, hit_rates: Dict, 
                          score: float, notes: str = "") -> None:
        """
        记录一期的性能数据
        
        Args:
            period: 期号
            parameters: 使用的参数配置
            prediction_results: 预测结果
            hit_rates: 各项命中率
            score: 综合得分
            notes: 备注信息
        """
        record = PerformanceRecord(
            timestamp=datetime.now().isoformat(),
            period=period,
            parameters=parameters,
            prediction_results=prediction_results,
            hit_rates=hit_rates,
            score=score,
            notes=notes
        )
        
        self.performance_history.append(record)
        self._save_performance_history()
        
        # 检查性能警告
        self._check_performance_alerts(record)
        
        print(f"[数据] 已记录期号 {period} 的性能数据，得分: {score:.4f}")
    
    def _save_performance_history(self) -> None:
        """保存性能历史数据"""
        try:
            data = [asdict(record) for record in self.performance_history]
            with open(self.performance_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"[警告] 保存性能历史失败: {e}")
    
    def _check_performance_alerts(self, record: PerformanceRecord) -> None:
        """检查性能警告"""
        alerts = []
        
        # 检查命中率阈值
        if record.hit_rates.get('bayes_hit_rate', 0) < self.thresholds['bayes_hit_rate_min']:
            alerts.append(f"贝叶斯命中率过低: {record.hit_rates.get('bayes_hit_rate', 0):.3f}")
            
        if record.hit_rates.get('overall_hit_rate', 0) < self.thresholds['overall_hit_rate_min']:
            alerts.append(f"整体命中率过低: {record.hit_rates.get('overall_hit_rate', 0):.3f}")
            
        if record.hit_rates.get('kill_success_rate', 1) < self.thresholds['kill_success_rate_min']:
            alerts.append(f"杀号成功率过低: {record.hit_rates.get('kill_success_rate', 1):.3f}")
        
        # 检查得分下降
        if len(self.performance_history) >= 2:
            prev_score = self.performance_history[-2].score
            if record.score < prev_score - self.thresholds['score_decline_threshold']:
                alerts.append(f"得分显著下降: {prev_score:.3f} -> {record.score:.3f}")
        
        # 检查连续表现不佳
        if len(self.performance_history) >= self.thresholds['consecutive_poor_periods']:
            recent_scores = [r.score for r in self.performance_history[-self.thresholds['consecutive_poor_periods']:]]
            avg_recent = np.mean(recent_scores)
            if avg_recent < 0.10:  # 连续低分
                alerts.append(f"连续{self.thresholds['consecutive_poor_periods']}期表现不佳，平均得分: {avg_recent:.3f}")
        
        if alerts:
            self._save_alerts(record.period, alerts)
            print(f"[警告] 性能警告 (期号 {record.period}):")
            for alert in alerts:
                print(f"   • {alert}")
    
    def _save_alerts(self, period: str, alerts: List[str]) -> None:
        """保存性能警告"""
        alert_data = {
            'timestamp': datetime.now().isoformat(),
            'period': period,
            'alerts': alerts
        }
        
        try:
            # 加载现有警告
            existing_alerts = []
            if self.alert_file.exists():
                with open(self.alert_file, 'r', encoding='utf-8') as f:
                    existing_alerts = json.load(f)
            
            existing_alerts.append(alert_data)
            
            # 只保留最近30天的警告
            cutoff_date = datetime.now() - timedelta(days=30)
            existing_alerts = [
                alert for alert in existing_alerts
                if datetime.fromisoformat(alert['timestamp']) > cutoff_date
            ]
            
            with open(self.alert_file, 'w', encoding='utf-8') as f:
                json.dump(existing_alerts, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"[警告] 保存警告失败: {e}")
    
    def generate_performance_report(self, days: int = 7) -> Dict:
        """
        生成性能报告
        
        Args:
            days: 分析最近几天的数据
            
        Returns:
            Dict: 性能报告
        """
        if not self.performance_history:
            return {"error": "无性能数据"}
        
        # 筛选最近数据
        cutoff_date = datetime.now() - timedelta(days=days)
        recent_records = [
            record for record in self.performance_history
            if datetime.fromisoformat(record.timestamp) > cutoff_date
        ]
        
        if not recent_records:
            return {"error": f"最近{days}天无性能数据"}
        
        # 计算统计指标
        scores = [r.score for r in recent_records]
        bayes_rates = [r.hit_rates.get('bayes_hit_rate', 0) for r in recent_records]
        overall_rates = [r.hit_rates.get('overall_hit_rate', 0) for r in recent_records]
        kill_rates = [r.hit_rates.get('kill_success_rate', 1) for r in recent_records]
        
        report = {
            'analysis_period': f"最近{days}天",
            'total_periods': len(recent_records),
            'date_range': {
                'start': recent_records[0].timestamp[:10],
                'end': recent_records[-1].timestamp[:10]
            },
            'performance_metrics': {
                'average_score': np.mean(scores),
                'score_std': np.std(scores),
                'best_score': np.max(scores),
                'worst_score': np.min(scores),
                'score_trend': self._calculate_trend(scores)
            },
            'hit_rate_analysis': {
                'bayes_hit_rate': {
                    'average': np.mean(bayes_rates),
                    'std': np.std(bayes_rates),
                    'trend': self._calculate_trend(bayes_rates)
                },
                'overall_hit_rate': {
                    'average': np.mean(overall_rates),
                    'std': np.std(overall_rates),
                    'trend': self._calculate_trend(overall_rates)
                },
                'kill_success_rate': {
                    'average': np.mean(kill_rates),
                    'std': np.std(kill_rates),
                    'trend': self._calculate_trend(kill_rates)
                }
            },
            'parameter_stability': self._analyze_parameter_stability(recent_records),
            'recommendations': self._generate_recommendations(recent_records)
        }
        
        return report
    
    def _calculate_trend(self, values: List[float]) -> str:
        """计算趋势"""
        if len(values) < 2:
            return "数据不足"
            
        # 简单线性趋势
        x = np.arange(len(values))
        slope = np.polyfit(x, values, 1)[0]
        
        if slope > 0.01:
            return "上升"
        elif slope < -0.01:
            return "下降"
        else:
            return "稳定"
    
    def _analyze_parameter_stability(self, records: List[PerformanceRecord]) -> Dict:
        """分析参数稳定性"""
        if len(records) < 2:
            return {"status": "数据不足"}
        
        # 检查参数是否发生变化
        first_params = records[0].parameters
        param_changes = 0
        
        for record in records[1:]:
            if record.parameters != first_params:
                param_changes += 1
        
        stability = 1 - (param_changes / len(records))
        
        return {
            "stability_score": stability,
            "parameter_changes": param_changes,
            "total_periods": len(records),
            "status": "稳定" if stability > 0.8 else "不稳定" if stability < 0.5 else "一般"
        }
    
    def _generate_recommendations(self, records: List[PerformanceRecord]) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        if not records:
            return ["无数据，无法生成建议"]
        
        # 分析最近表现
        recent_scores = [r.score for r in records[-5:]]  # 最近5期
        avg_recent = np.mean(recent_scores)
        
        if avg_recent < 0.15:
            recommendations.append("建议重新运行参数优化，当前参数表现不佳")
        
        # 分析命中率
        recent_bayes_rates = [r.hit_rates.get('bayes_hit_rate', 0) for r in records[-5:]]
        if np.mean(recent_bayes_rates) < 0.15:
            recommendations.append("贝叶斯命中率偏低，建议调整频率权重和模式权重")
        
        # 分析杀号成功率
        recent_kill_rates = [r.hit_rates.get('kill_success_rate', 1) for r in records[-5:]]
        if np.mean(recent_kill_rates) < 0.8:
            recommendations.append("杀号成功率下降，建议检查杀号算法")
        
        # 分析趋势
        if len(recent_scores) >= 3:
            trend = self._calculate_trend(recent_scores)
            if trend == "下降":
                recommendations.append("性能呈下降趋势，建议尽快进行参数重新优化")
        
        if not recommendations:
            recommendations.append("当前参数表现良好，继续监控")
        
        return recommendations
    
    def export_performance_data(self, output_file: str = None) -> str:
        """
        导出性能数据为CSV格式
        
        Args:
            output_file: 输出文件路径
            
        Returns:
            str: 导出文件路径
        """
        if not self.performance_history:
            raise ValueError("无性能数据可导出")
        
        if output_file is None:
            output_file = self.monitor_dir / f"performance_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        # 准备数据
        data = []
        for record in self.performance_history:
            row = {
                'timestamp': record.timestamp,
                'period': record.period,
                'score': record.score,
                'bayes_hit_rate': record.hit_rates.get('bayes_hit_rate', 0),
                'overall_hit_rate': record.hit_rates.get('overall_hit_rate', 0),
                'kill_success_rate': record.hit_rates.get('kill_success_rate', 1),
                'frequency_weight': record.parameters.get('frequency_weight', 0),
                'pattern_weight': record.parameters.get('pattern_weight', 0),
                'balance_weight': record.parameters.get('balance_weight', 0),
                'trend_weight': record.parameters.get('trend_weight', 0),
                'kill_avoidance_weight': record.parameters.get('kill_avoidance_weight', 0),
                'notes': record.notes
            }
            data.append(row)
        
        # 保存为CSV
        df = pd.DataFrame(data)
        df.to_csv(output_file, index=False, encoding='utf-8')
        
        print(f"[数据] 性能数据已导出至: {output_file}")
        return str(output_file)


def main():
    """测试监控器"""
    monitor = ParameterMonitor()
    
    # 模拟记录性能数据
    test_params = {
        'frequency_weight': 0.316,
        'pattern_weight': 0.263,
        'balance_weight': 0.105,
        'trend_weight': 0.053,
        'kill_avoidance_weight': 0.263
    }
    
    test_hit_rates = {
        'bayes_hit_rate': 0.206,
        'overall_hit_rate': 0.183,
        'kill_success_rate': 0.9
    }
    
    monitor.record_performance(
        period="25069",
        parameters=test_params,
        prediction_results={},
        hit_rates=test_hit_rates,
        score=0.178,
        notes="测试数据"
    )
    
    # 生成报告
    report = monitor.generate_performance_report(days=7)
    print("\n[数据] 性能报告:")
    print(json.dumps(report, indent=2, ensure_ascii=False))


if __name__ == "__main__":
    main()
