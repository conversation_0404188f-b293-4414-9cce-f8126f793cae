"""
基础类和接口定义
定义系统中所有组件的基础接口和抽象类
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Tuple, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum
import pandas as pd

from config.logging_config import LoggerMixin
from .interfaces import (
    IStandardPredictor,
    PredictionResult as StandardPredictionResult,
    ValidationResult,
    PredictionType,
)


class BallType(Enum):
    """球类型枚举"""

    RED = "red"
    BLUE = "blue"


class FeatureType(Enum):
    """特征类型枚举"""

    ODD_EVEN = "odd_even"
    SIZE = "size"
    SUM_RANGE = "sum_range"
    SPAN = "span"
    CONSECUTIVE = "consecutive"
    AC_VALUE = "ac_value"
    ZONE_DISTRIBUTION = "zone_distribution"


@dataclass
class PredictionResult:
    """预测结果数据类"""

    feature_type: FeatureType
    ball_type: BallType
    predicted_state: str
    confidence: float
    probability_distribution: Dict[str, float]
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class GenerationResult:
    """号码生成结果数据类"""

    red_balls: List[int]
    blue_balls: List[int]
    generation_method: str
    confidence: float
    kill_numbers: Dict[str, List[List[int]]]
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

    @property
    def formatted_numbers(self) -> Tuple[str, str]:
        """格式化的号码字符串"""
        red_str = ",".join([f"{num:02d}" for num in sorted(self.red_balls)])
        blue_str = ",".join([f"{num:02d}" for num in sorted(self.blue_balls)])
        return red_str, blue_str


@dataclass
class AnalysisResult:
    """分析结果数据类"""

    feature_sequences: Dict[str, List[str]]
    state_frequencies: Dict[str, Dict[str, float]]
    trend_analysis: Dict[str, Any]
    statistical_summary: Dict[str, Any]
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class BaseAnalyzer(ABC, LoggerMixin):
    """数据分析器基类"""

    def __init__(self, data: pd.DataFrame):
        """
        初始化分析器

        Args:
            data: 历史开奖数据
        """
        self.data = data
        self.logger.info(f"初始化分析器，数据量: {len(data)} 期")

    @abstractmethod
    def extract_features(self) -> AnalysisResult:
        """
        提取特征

        Returns:
            AnalysisResult: 分析结果
        """
        pass

    @abstractmethod
    def get_feature_sequence(self, feature_type: str) -> List[str]:
        """
        获取特征序列

        Args:
            feature_type: 特征类型

        Returns:
            List[str]: 特征序列
        """
        pass

    @abstractmethod
    def calculate_state_frequencies(self, feature_type: str) -> Dict[str, float]:
        """
        计算状态频率

        Args:
            feature_type: 特征类型

        Returns:
            Dict[str, float]: 状态频率字典
        """
        pass


class BasePredictor(ABC, LoggerMixin):
    """预测器基类"""

    def __init__(self, name: str):
        """
        初始化预测器

        Args:
            name: 预测器名称
        """
        self.name = name
        self.is_trained = False
        self.logger.info(f"初始化预测器: {name}")

    @abstractmethod
    def train(self, analyzer: BaseAnalyzer) -> None:
        """
        训练预测器

        Args:
            analyzer: 数据分析器
        """
        pass

    @abstractmethod
    def predict(
        self,
        feature_type: FeatureType,
        ball_type: BallType,
        current_state: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
    ) -> PredictionResult:
        """
        进行预测

        Args:
            feature_type: 特征类型
            ball_type: 球类型
            current_state: 当前状态
            context: 上下文信息

        Returns:
            PredictionResult: 预测结果
        """
        pass

    def validate_training(self) -> bool:
        """验证训练状态"""
        if not self.is_trained:
            self.logger.warning(f"预测器 {self.name} 尚未训练")
            return False
        return True


class BaseGenerator(ABC, LoggerMixin):
    """号码生成器基类"""

    def __init__(self, name: str):
        """
        初始化生成器

        Args:
            name: 生成器名称
        """
        self.name = name
        self.logger.info(f"初始化生成器: {name}")

    @abstractmethod
    def generate(
        self,
        predictions: Dict[FeatureType, PredictionResult],
        kill_numbers: Dict[str, List[List[int]]],
        context: Optional[Dict[str, Any]] = None,
    ) -> GenerationResult:
        """
        生成号码

        Args:
            predictions: 预测结果字典
            kill_numbers: 杀号字典
            context: 上下文信息

        Returns:
            GenerationResult: 生成结果
        """
        pass

    def validate_numbers(self, red_balls: List[int], blue_balls: List[int]) -> bool:
        """
        验证号码有效性

        Args:
            red_balls: 红球号码
            blue_balls: 蓝球号码

        Returns:
            bool: 是否有效
        """
        # 基础验证 - 大乐透格式：5个红球 + 2个蓝球
        if len(red_balls) != 5 or len(blue_balls) != 2:
            return False

        if not all(1 <= num <= 35 for num in red_balls):
            return False

        if not all(1 <= num <= 12 for num in blue_balls):
            return False

        if len(set(red_balls)) != 5 or len(set(blue_balls)) != 2:
            return False

        return True


class BaseSystem(ABC, LoggerMixin):
    """预测系统基类"""

    def __init__(self, name: str, version: str = "1.0"):
        """
        初始化系统

        Args:
            name: 系统名称
            version: 版本号
        """
        self.name = name
        self.version = version
        self.is_initialized = False
        self.logger.info(f"初始化系统: {name} v{version}")

    @abstractmethod
    def initialize(self) -> None:
        """初始化系统组件"""
        pass

    @abstractmethod
    def predict_next_period(self, current_period_index: int) -> Dict[str, Any]:
        """
        预测下一期

        Args:
            current_period_index: 当前期次索引

        Returns:
            Dict[str, Any]: 预测结果
        """
        pass

    @abstractmethod
    def run_backtest(
        self, num_periods: int = 50, display_periods: int = 10
    ) -> Dict[str, Any]:
        """
        运行回测

        Args:
            num_periods: 回测期数
            display_periods: 显示期数

        Returns:
            Dict[str, Any]: 回测结果
        """
        pass


class StandardBasePredictor(IStandardPredictor, LoggerMixin):
    """标准化基础预测器类

    实现IStandardPredictor接口的基础类，提供通用功能实现。
    所有新的预测器都应该继承此类。
    """

    def __init__(
        self, name: str, prediction_type: PredictionType, version: str = "1.0"
    ):
        """初始化标准化预测器

        Args:
            name: 预测器名称
            prediction_type: 预测类型
            version: 版本号
        """
        self.name = name
        self.prediction_type = prediction_type
        self.version = version
        self.is_trained = False
        self.confidence_threshold = 0.5
        self.model_params = {}
        self.logger.info(f"初始化标准化预测器: {name} v{version}")

    def get_prediction_type(self) -> PredictionType:
        """获取预测类型"""
        return self.prediction_type

    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "name": self.name,
            "version": self.version,
            "prediction_type": self.prediction_type.value,
            "is_trained": self.is_trained,
            "confidence_threshold": self.confidence_threshold,
            "parameters": self.model_params.copy(),
        }

    def validate_input(self, data: pd.DataFrame, target_index: int) -> ValidationResult:
        """验证输入数据"""
        errors = []
        warnings = []

        # 基础验证
        if data is None or data.empty:
            errors.append("输入数据为空")

        if target_index < 0:
            errors.append("目标索引不能为负数")

        if target_index >= len(data):
            errors.append(f"目标索引 {target_index} 超出数据范围 {len(data)}")

        # 数据量检查
        if len(data) < 10:
            warnings.append("数据量较少，可能影响预测准确性")

        # 训练状态检查
        if not self.is_trained:
            warnings.append("模型尚未训练")

        return ValidationResult(
            is_valid=len(errors) == 0, errors=errors, warnings=warnings
        )

    def predict_batch(
        self, data: pd.DataFrame, target_indices: List[int], **kwargs
    ) -> List[StandardPredictionResult]:
        """批量预测的默认实现"""
        results = []
        for target_index in target_indices:
            try:
                result = self.predict(data, target_index, **kwargs)
                results.append(result)
            except Exception as e:
                self.logger.error(f"批量预测失败，索引 {target_index}: {e}")
                # 创建错误结果
                error_result = StandardPredictionResult(
                    prediction_type=self.prediction_type,
                    ball_type=kwargs.get("ball_type", BallType.RED),
                    value=None,
                    confidence=0.0,
                    metadata={"error": str(e)},
                )
                results.append(error_result)
        return results

    def train(self, data: pd.DataFrame, **kwargs) -> bool:
        """训练模型的默认实现"""
        try:
            self.logger.info(f"开始训练模型 {self.name}")
            # 子类应该重写此方法
            self.is_trained = True
            self.logger.info(f"模型 {self.name} 训练完成")
            return True
        except Exception as e:
            self.logger.error(f"模型 {self.name} 训练失败: {e}")
            return False

    def get_confidence_threshold(self) -> float:
        """获取置信度阈值"""
        return self.confidence_threshold

    def set_confidence_threshold(self, threshold: float) -> None:
        """设置置信度阈值"""
        if 0.0 <= threshold <= 1.0:
            self.confidence_threshold = threshold
            self.logger.info(f"设置置信度阈值为 {threshold}")
        else:
            self.logger.warning(f"无效的置信度阈值 {threshold}，应在 [0.0, 1.0] 范围内")

    def supports_incremental_training(self) -> bool:
        """是否支持增量训练"""
        return False

    @abstractmethod
    def predict(
        self, data: pd.DataFrame, target_index: int, **kwargs
    ) -> StandardPredictionResult:
        """执行预测 - 子类必须实现"""
        pass
