# 技术栈和依赖

## 编程语言
- **Python 3.8+**: 主要开发语言

## 核心依赖库
- **pandas**: 数据处理和分析
- **numpy**: 数值计算
- **scikit-learn**: 机器学习算法
- **scipy**: 科学计算
- **statsmodels**: 统计分析

## 可选依赖
- **tensorflow**: 深度学习框架
- **torch**: PyTorch深度学习框架
- **matplotlib/seaborn**: 数据可视化
- **plotly**: 交互式图表

## 开发工具
- **pytest**: 测试框架
- **black**: 代码格式化
- **flake8**: 代码检查
- **mypy**: 类型检查

## 项目管理
- **setuptools**: 包管理
- **requirements.txt**: 依赖管理
- **setup.py**: 安装配置

## 数据存储
- **CSV文件**: 历史数据存储
- **JSON文件**: 配置和结果存储
- **SQLite**: 可选的轻量级数据库