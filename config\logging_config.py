"""
日志配置模块
提供统一的日志配置和管理功能
"""

import logging
import logging.handlers
import os
from pathlib import Path
from typing import Optional

from .settings import get_settings


def setup_logging(
    level: Optional[str] = None,
    file_path: Optional[str] = None,
    console_enabled: bool = True,
    file_enabled: Optional[bool] = None
) -> logging.Logger:
    """
    设置日志系统
    
    Args:
        level: 日志级别
        file_path: 日志文件路径
        console_enabled: 是否启用控制台输出
        file_enabled: 是否启用文件输出
    
    Returns:
        logging.Logger: 配置好的日志器
    """
    settings = get_settings()
    log_config = settings.logging
    
    # 使用参数或配置中的值
    level = level or log_config.level
    file_path = file_path or log_config.file_path
    file_enabled = file_enabled if file_enabled is not None else log_config.file_enabled
    
    # 创建根日志器
    logger = logging.getLogger('lottery_predictor')
    logger.setLevel(getattr(logging, level.upper()))
    
    # 清除现有的处理器
    logger.handlers.clear()
    
    # 创建格式器
    formatter = logging.Formatter(log_config.format)
    
    # 控制台处理器
    if console_enabled:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(getattr(logging, level.upper()))
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    # 文件处理器
    if file_enabled and file_path:
        # 确保日志目录存在
        log_dir = Path(file_path).parent
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # 使用RotatingFileHandler支持日志轮转
        file_handler = logging.handlers.RotatingFileHandler(
            file_path,
            maxBytes=log_config.max_file_size,
            backupCount=log_config.backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(getattr(logging, level.upper()))
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    # 防止日志重复
    logger.propagate = False
    
    return logger


def get_logger(name: str) -> logging.Logger:
    """
    获取指定名称的日志器
    
    Args:
        name: 日志器名称
    
    Returns:
        logging.Logger: 日志器实例
    """
    return logging.getLogger(f'lottery_predictor.{name}')


class LoggerMixin:
    """日志器混入类，为其他类提供日志功能"""
    
    @property
    def logger(self) -> logging.Logger:
        """获取当前类的日志器"""
        class_name = self.__class__.__name__
        return get_logger(class_name)


def log_performance(func):
    """性能日志装饰器"""
    import time
    import functools
    
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        logger = get_logger('performance')
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            end_time = time.time()
            duration = end_time - start_time
            
            logger.info(
                f"{func.__name__} 执行完成 - "
                f"耗时: {duration:.3f}s"
            )
            return result
            
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            
            logger.error(
                f"{func.__name__} 执行失败 - "
                f"耗时: {duration:.3f}s - "
                f"错误: {str(e)}"
            )
            raise
    
    return wrapper


def log_method_calls(cls):
    """类方法调用日志装饰器"""
    import functools
    
    class LoggedClass(cls):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            self._logger = get_logger(cls.__name__)
        
        def __getattribute__(self, name):
            attr = super().__getattribute__(name)
            
            if callable(attr) and not name.startswith('_'):
                @functools.wraps(attr)
                def logged_method(*args, **kwargs):
                    self._logger.debug(f"调用方法: {name}")
                    try:
                        result = attr(*args, **kwargs)
                        self._logger.debug(f"方法 {name} 执行成功")
                        return result
                    except Exception as e:
                        self._logger.error(f"方法 {name} 执行失败: {str(e)}")
                        raise
                
                return logged_method
            
            return attr
    
    return LoggedClass
