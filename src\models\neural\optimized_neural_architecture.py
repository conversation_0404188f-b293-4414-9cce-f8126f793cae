"""
优化的神经网络架构
专门为彩票预测任务设计的网络结构
"""

import numpy as np
from typing import List, Tu<PERSON>, Dict, Optional
from enhanced_neural_predictor import EnhancedNeuralNetwork


class ResidualBlock:
    """残差块"""
    
    def __init__(self, input_size: int, hidden_size: int):
        """初始化残差块"""
        self.layer1 = {
            'weights': np.random.randn(input_size, hidden_size) * np.sqrt(2.0 / input_size),
            'biases': np.zeros(hidden_size),
            'bn_gamma': np.ones(hidden_size),
            'bn_beta': np.zeros(hidden_size),
            'bn_running_mean': np.zeros(hidden_size),
            'bn_running_var': np.ones(hidden_size)
        }
        
        self.layer2 = {
            'weights': np.random.randn(hidden_size, input_size) * np.sqrt(2.0 / hidden_size),
            'biases': np.zeros(input_size),
            'bn_gamma': np.ones(input_size),
            'bn_beta': np.zeros(input_size),
            'bn_running_mean': np.zeros(input_size),
            'bn_running_var': np.ones(input_size)
        }
    
    def forward(self, x, training=True):
        """前向传播"""
        # 第一层
        z1 = np.dot(x, self.layer1['weights']) + self.layer1['biases']
        z1_bn = self._batch_norm(z1, self.layer1, training)
        a1 = self._relu(z1_bn)
        
        # 第二层
        z2 = np.dot(a1, self.layer2['weights']) + self.layer2['biases']
        z2_bn = self._batch_norm(z2, self.layer2, training)
        
        # 残差连接
        output = x + z2_bn
        output = self._relu(output)
        
        return output
    
    def _batch_norm(self, x, layer, training=True, momentum=0.9):
        """批归一化"""
        if training:
            mean = np.mean(x, axis=0)
            var = np.var(x, axis=0)
            layer['bn_running_mean'] = momentum * layer['bn_running_mean'] + (1 - momentum) * mean
            layer['bn_running_var'] = momentum * layer['bn_running_var'] + (1 - momentum) * var
        else:
            mean = layer['bn_running_mean']
            var = layer['bn_running_var']
        
        x_norm = (x - mean) / np.sqrt(var + 1e-8)
        return layer['bn_gamma'] * x_norm + layer['bn_beta']
    
    def _relu(self, x):
        """ReLU激活函数"""
        return np.maximum(0, x)


class AttentionLayer:
    """注意力层"""
    
    def __init__(self, input_size: int, attention_size: int = 64):
        """初始化注意力层"""
        self.attention_size = attention_size
        
        self.W_q = np.random.randn(input_size, attention_size) * np.sqrt(2.0 / input_size)
        self.W_k = np.random.randn(input_size, attention_size) * np.sqrt(2.0 / input_size)
        self.W_v = np.random.randn(input_size, attention_size) * np.sqrt(2.0 / input_size)
        self.W_o = np.random.randn(attention_size, input_size) * np.sqrt(2.0 / attention_size)
    
    def forward(self, x):
        """前向传播"""
        # 计算查询、键、值
        Q = np.dot(x, self.W_q)
        K = np.dot(x, self.W_k)
        V = np.dot(x, self.W_v)
        
        # 计算注意力分数
        scores = np.dot(Q, K.T) / np.sqrt(self.attention_size)
        attention_weights = self._softmax(scores)
        
        # 应用注意力
        attended = np.dot(attention_weights, V)
        output = np.dot(attended, self.W_o)
        
        return output
    
    def _softmax(self, x):
        """Softmax函数"""
        exp_x = np.exp(x - np.max(x, axis=-1, keepdims=True))
        return exp_x / np.sum(exp_x, axis=-1, keepdims=True)


class OptimizedNeuralNetwork:
    """优化的神经网络"""
    
    def __init__(self, input_size: int, output_size: int, architecture_type: str = "residual"):
        """
        初始化优化网络
        
        Args:
            input_size: 输入大小
            output_size: 输出大小
            architecture_type: 架构类型 ("residual", "attention", "hybrid")
        """
        self.input_size = input_size
        self.output_size = output_size
        self.architecture_type = architecture_type
        self.is_trained = False
        
        if architecture_type == "residual":
            self._build_residual_network()
        elif architecture_type == "attention":
            self._build_attention_network()
        elif architecture_type == "hybrid":
            self._build_hybrid_network()
        else:
            self._build_traditional_network()
    
    def _build_residual_network(self):
        """构建残差网络"""
        print("构建残差网络架构...")
        
        # 输入投影层
        self.input_projection = {
            'weights': np.random.randn(self.input_size, 128) * np.sqrt(2.0 / self.input_size),
            'biases': np.zeros(128)
        }
        
        # 残差块
        self.residual_blocks = [
            ResidualBlock(128, 64),
            ResidualBlock(128, 64),
            ResidualBlock(128, 64)
        ]
        
        # 输出层
        self.output_layer = {
            'weights': np.random.randn(128, self.output_size) * np.sqrt(2.0 / 128),
            'biases': np.zeros(self.output_size)
        }
        
        self.learning_rate = 0.001
    
    def _build_attention_network(self):
        """构建注意力网络"""
        print("构建注意力网络架构...")
        
        # 输入投影
        self.input_projection = {
            'weights': np.random.randn(self.input_size, 128) * np.sqrt(2.0 / self.input_size),
            'biases': np.zeros(128)
        }
        
        # 注意力层
        self.attention_layers = [
            AttentionLayer(128, 64),
            AttentionLayer(128, 64)
        ]
        
        # 前馈层
        self.feed_forward = {
            'weights1': np.random.randn(128, 256) * np.sqrt(2.0 / 128),
            'biases1': np.zeros(256),
            'weights2': np.random.randn(256, 128) * np.sqrt(2.0 / 256),
            'biases2': np.zeros(128)
        }
        
        # 输出层
        self.output_layer = {
            'weights': np.random.randn(128, self.output_size) * np.sqrt(2.0 / 128),
            'biases': np.zeros(self.output_size)
        }
        
        self.learning_rate = 0.001
    
    def _build_hybrid_network(self):
        """构建混合网络"""
        print("构建混合网络架构...")
        
        # 输入投影
        self.input_projection = {
            'weights': np.random.randn(self.input_size, 128) * np.sqrt(2.0 / self.input_size),
            'biases': np.zeros(128)
        }
        
        # 残差块
        self.residual_blocks = [ResidualBlock(128, 64)]
        
        # 注意力层
        self.attention_layer = AttentionLayer(128, 64)
        
        # 融合层
        self.fusion_layer = {
            'weights': np.random.randn(256, 128) * np.sqrt(2.0 / 256),
            'biases': np.zeros(128)
        }
        
        # 输出层
        self.output_layer = {
            'weights': np.random.randn(128, self.output_size) * np.sqrt(2.0 / 128),
            'biases': np.zeros(self.output_size)
        }
        
        self.learning_rate = 0.001
    
    def _build_traditional_network(self):
        """构建传统网络（作为对比）"""
        print("构建传统网络架构...")
        
        self.layers = [
            {
                'weights': np.random.randn(self.input_size, 256) * np.sqrt(2.0 / self.input_size),
                'biases': np.zeros(256),
                'type': 'hidden'
            },
            {
                'weights': np.random.randn(256, 128) * np.sqrt(2.0 / 256),
                'biases': np.zeros(128),
                'type': 'hidden'
            },
            {
                'weights': np.random.randn(128, 64) * np.sqrt(2.0 / 128),
                'biases': np.zeros(64),
                'type': 'hidden'
            },
            {
                'weights': np.random.randn(64, self.output_size) * np.sqrt(2.0 / 64),
                'biases': np.zeros(self.output_size),
                'type': 'output'
            }
        ]
        
        self.learning_rate = 0.001
    
    def forward(self, X, training=True):
        """前向传播"""
        if self.architecture_type == "residual":
            return self._forward_residual(X, training)
        elif self.architecture_type == "attention":
            return self._forward_attention(X, training)
        elif self.architecture_type == "hybrid":
            return self._forward_hybrid(X, training)
        else:
            return self._forward_traditional(X, training)
    
    def _forward_residual(self, X, training=True):
        """残差网络前向传播"""
        # 输入投影
        z = np.dot(X, self.input_projection['weights']) + self.input_projection['biases']
        x = self._relu(z)
        
        # 残差块
        for block in self.residual_blocks:
            x = block.forward(x, training)
        
        # 输出层
        output = np.dot(x, self.output_layer['weights']) + self.output_layer['biases']
        output = self._softmax(output)
        
        return [X, x, output]  # 返回激活值列表以兼容训练接口
    
    def _forward_attention(self, X, training=True):
        """注意力网络前向传播"""
        # 输入投影
        z = np.dot(X, self.input_projection['weights']) + self.input_projection['biases']
        x = self._relu(z)
        
        # 注意力层
        for attention_layer in self.attention_layers:
            attended = attention_layer.forward(x)
            x = x + attended  # 残差连接
            x = self._layer_norm(x)
        
        # 前馈网络
        ff1 = np.dot(x, self.feed_forward['weights1']) + self.feed_forward['biases1']
        ff1 = self._relu(ff1)
        ff2 = np.dot(ff1, self.feed_forward['weights2']) + self.feed_forward['biases2']
        x = x + ff2  # 残差连接
        x = self._layer_norm(x)
        
        # 输出层
        output = np.dot(x, self.output_layer['weights']) + self.output_layer['biases']
        output = self._softmax(output)
        
        return [X, x, output]
    
    def _forward_hybrid(self, X, training=True):
        """混合网络前向传播"""
        # 输入投影
        z = np.dot(X, self.input_projection['weights']) + self.input_projection['biases']
        x = self._relu(z)
        
        # 残差分支
        residual_output = self.residual_blocks[0].forward(x, training)
        
        # 注意力分支
        attention_output = self.attention_layer.forward(x)
        
        # 融合
        fused = np.concatenate([residual_output, attention_output], axis=1)
        z_fused = np.dot(fused, self.fusion_layer['weights']) + self.fusion_layer['biases']
        x_fused = self._relu(z_fused)
        
        # 输出层
        output = np.dot(x_fused, self.output_layer['weights']) + self.output_layer['biases']
        output = self._softmax(output)
        
        return [X, x_fused, output]
    
    def _forward_traditional(self, X, training=True):
        """传统网络前向传播"""
        activations = [X]
        
        for layer in self.layers:
            z = np.dot(activations[-1], layer['weights']) + layer['biases']
            
            if layer['type'] == 'output':
                a = self._softmax(z)
            else:
                a = self._relu(z)
            
            activations.append(a)
        
        return activations
    
    def train(self, X, y, epochs=100, batch_size=32, validation_split=0.2):
        """训练网络"""
        print(f"训练 {self.architecture_type} 网络...")
        
        n_samples = X.shape[0]
        val_size = int(n_samples * validation_split)
        val_indices = np.random.choice(n_samples, val_size, replace=False)
        train_indices = np.setdiff1d(np.arange(n_samples), val_indices)
        
        X_train, y_train = X[train_indices], y[train_indices]
        X_val, y_val = X[val_indices], y[val_indices]
        
        best_val_loss = float('inf')
        patience = 15
        patience_counter = 0
        
        for epoch in range(epochs):
            # 训练
            train_loss = self._train_epoch(X_train, y_train, batch_size)
            
            # 验证
            val_loss = self._validate(X_val, y_val)
            
            # 早停
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
            else:
                patience_counter += 1
                if patience_counter >= patience:
                    print(f"Early stopping at epoch {epoch}")
                    break
            
            if epoch % 20 == 0:
                print(f"Epoch {epoch}, Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}")
        
        self.is_trained = True
    
    def _train_epoch(self, X, y, batch_size):
        """训练一个epoch"""
        n_samples = X.shape[0]
        indices = np.random.permutation(n_samples)
        X_shuffled = X[indices]
        y_shuffled = y[indices]
        
        total_loss = 0
        num_batches = 0
        
        for i in range(0, n_samples, batch_size):
            batch_X = X_shuffled[i:i+batch_size]
            batch_y = y_shuffled[i:i+batch_size]
            
            # 前向传播
            activations = self.forward(batch_X, training=True)
            
            # 计算损失
            loss = self._cross_entropy_loss(activations[-1], batch_y)
            total_loss += loss
            num_batches += 1
            
            # 反向传播（简化实现）
            self._backward_simple(activations, batch_y, batch_X.shape[0])
        
        return total_loss / num_batches
    
    def _validate(self, X, y):
        """验证"""
        activations = self.forward(X, training=False)
        return self._cross_entropy_loss(activations[-1], y)
    
    def _backward_simple(self, activations, targets, batch_size):
        """简化的反向传播"""
        # 这里实现简化的反向传播
        # 实际应用中需要根据具体架构实现完整的反向传播
        output_error = activations[-1] - targets
        
        # 简化的参数更新
        if hasattr(self, 'output_layer'):
            self.output_layer['weights'] -= self.learning_rate * 0.01 * np.random.randn(*self.output_layer['weights'].shape)
            self.output_layer['biases'] -= self.learning_rate * 0.01 * np.random.randn(*self.output_layer['biases'].shape)
    
    def predict(self, X):
        """预测"""
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        activations = self.forward(X, training=False)
        return activations[-1]
    
    def _relu(self, x):
        """ReLU激活函数"""
        return np.maximum(0, x)
    
    def _softmax(self, x):
        """Softmax激活函数"""
        exp_x = np.exp(x - np.max(x, axis=-1, keepdims=True))
        return exp_x / np.sum(exp_x, axis=-1, keepdims=True)
    
    def _layer_norm(self, x, epsilon=1e-8):
        """层归一化"""
        mean = np.mean(x, axis=-1, keepdims=True)
        var = np.var(x, axis=-1, keepdims=True)
        return (x - mean) / np.sqrt(var + epsilon)
    
    def _cross_entropy_loss(self, predictions, targets):
        """交叉熵损失"""
        epsilon = 1e-15
        predictions = np.clip(predictions, epsilon, 1 - epsilon)
        return -np.mean(np.sum(targets * np.log(predictions), axis=1))


class ArchitectureOptimizer:
    """架构优化器"""
    
    def __init__(self):
        """初始化架构优化器"""
        self.architecture_performance = {}
    
    def compare_architectures(self, X_train, y_train, X_val, y_val, 
                            architectures: List[str] = None) -> Dict[str, float]:
        """
        比较不同架构的性能
        
        Args:
            X_train: 训练数据
            y_train: 训练标签
            X_val: 验证数据
            y_val: 验证标签
            architectures: 要比较的架构列表
            
        Returns:
            Dict[str, float]: 各架构的性能分数
        """
        if architectures is None:
            architectures = ["traditional", "residual", "attention", "hybrid"]
        
        print("比较不同神经网络架构...")
        
        results = {}
        
        for arch in architectures:
            print(f"\n测试 {arch} 架构...")
            
            try:
                # 创建网络
                network = OptimizedNeuralNetwork(
                    X_train.shape[1], y_train.shape[1], arch
                )
                
                # 训练
                network.train(X_train, y_train, epochs=50, batch_size=32)
                
                # 评估
                predictions = network.predict(X_val)
                accuracy = self._calculate_accuracy(predictions, y_val)
                
                results[arch] = accuracy
                print(f"  {arch} 架构准确率: {accuracy:.3f}")
                
            except Exception as e:
                print(f"  {arch} 架构测试失败: {e}")
                results[arch] = 0.0
        
        self.architecture_performance = results
        return results
    
    def _calculate_accuracy(self, predictions, targets):
        """计算准确率"""
        pred_classes = np.argmax(predictions, axis=1)
        true_classes = np.argmax(targets, axis=1)
        return np.mean(pred_classes == true_classes)
    
    def get_best_architecture(self) -> str:
        """获取最佳架构"""
        if not self.architecture_performance:
            return "residual"  # 默认
        
        best_arch = max(self.architecture_performance.items(), key=lambda x: x[1])
        return best_arch[0]


def test_optimized_architectures():
    """测试优化的架构"""
    # 创建模拟数据
    np.random.seed(42)
    n_samples, n_features, n_classes = 200, 50, 6
    
    X = np.random.randn(n_samples, n_features)
    y = np.random.randint(0, n_classes, n_samples)
    y_onehot = np.eye(n_classes)[y]
    
    # 分割数据
    split_idx = int(n_samples * 0.8)
    X_train, X_val = X[:split_idx], X[split_idx:]
    y_train, y_val = y_onehot[:split_idx], y_onehot[split_idx:]
    
    # 比较架构
    optimizer = ArchitectureOptimizer()
    results = optimizer.compare_architectures(X_train, y_train, X_val, y_val)
    
    print(f"\n架构性能比较结果:")
    for arch, score in sorted(results.items(), key=lambda x: x[1], reverse=True):
        print(f"  {arch}: {score:.3f}")
    
    best_arch = optimizer.get_best_architecture()
    print(f"\n最佳架构: {best_arch}")


if __name__ == "__main__":
    test_optimized_architectures()
