"""决策树模块配置管理

统一管理决策树相关的配置参数，支持环境变量和配置文件。
"""

from typing import Dict, Any, Optional
from dataclasses import dataclass, field
from pathlib import Path
import os
import json
from ..utils.logger import get_logger


@dataclass
class DecisionTreeConfig:
    """决策树配置类"""
    
    # 决策树参数
    max_depth: int = 10
    min_samples_split: int = 5
    min_samples_leaf: int = 2
    random_state: int = 42
    
    # 模型保存路径
    model_save_dir: str = "models/decision_tree"
    
    # 特征工程参数
    feature_window_size: int = 10
    enable_feature_cache: bool = True
    cache_ttl_seconds: int = 3600
    
    # 性能监控
    enable_performance_monitoring: bool = True
    performance_log_interval: int = 100
    
    # 预测参数
    prediction_confidence_threshold: float = 0.6
    ensemble_voting_strategy: str = "weighted"  # "weighted" or "majority"
    ensemble_size: int = 5
    
    # 数据验证
    enable_input_validation: bool = True
    min_training_samples: int = 50
    
    # 日志配置
    log_level: str = "INFO"
    enable_detailed_logging: bool = False
    
    # 其他配置
    additional_params: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self) -> None:
        """初始化后的验证和设置"""
        self._validate_config()
        self._setup_directories()
    
    def _validate_config(self) -> None:
        """验证配置参数的有效性"""
        if self.max_depth <= 0:
            raise ValueError("max_depth must be positive")
        
        if self.min_samples_split < 2:
            raise ValueError("min_samples_split must be at least 2")
        
        if self.min_samples_leaf < 1:
            raise ValueError("min_samples_leaf must be at least 1")
        
        if not 0 <= self.prediction_confidence_threshold <= 1:
            raise ValueError("prediction_confidence_threshold must be between 0 and 1")
        
        if self.ensemble_voting_strategy not in ["weighted", "majority"]:
            raise ValueError("ensemble_voting_strategy must be 'weighted' or 'majority'")
        
        if self.ensemble_size < 1:
            raise ValueError("ensemble_size must be at least 1")
    
    def _setup_directories(self) -> None:
        """创建必要的目录"""
        model_dir = Path(self.model_save_dir)
        model_dir.mkdir(parents=True, exist_ok=True)
    
    @classmethod
    def from_file(cls, config_path: str) -> 'DecisionTreeConfig':
        """从配置文件加载配置
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            配置实例
        """
        logger = get_logger("DecisionTreeConfig")
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            logger.info(f"从文件加载配置: {config_path}")
            return cls(**config_data)
            
        except FileNotFoundError:
            logger.warning(f"配置文件不存在: {config_path}，使用默认配置")
            return cls()
        except json.JSONDecodeError as e:
            logger.error(f"配置文件格式错误: {e}，使用默认配置")
            return cls()
        except Exception as e:
            logger.error(f"加载配置文件时出错: {e}，使用默认配置")
            return cls()
    
    @classmethod
    def from_env(cls) -> 'DecisionTreeConfig':
        """从环境变量加载配置
        
        Returns:
            配置实例
        """
        config_data = {}
        
        # 映射环境变量到配置参数
        env_mapping = {
            'DT_MAX_DEPTH': ('max_depth', int),
            'DT_MIN_SAMPLES_SPLIT': ('min_samples_split', int),
            'DT_MIN_SAMPLES_LEAF': ('min_samples_leaf', int),
            'DT_RANDOM_STATE': ('random_state', int),
            'DT_MODEL_SAVE_DIR': ('model_save_dir', str),
            'DT_FEATURE_WINDOW_SIZE': ('feature_window_size', int),
            'DT_ENABLE_FEATURE_CACHE': ('enable_feature_cache', lambda x: x.lower() == 'true'),
            'DT_CACHE_TTL_SECONDS': ('cache_ttl_seconds', int),
            'DT_ENABLE_PERFORMANCE_MONITORING': ('enable_performance_monitoring', lambda x: x.lower() == 'true'),
            'DT_PERFORMANCE_LOG_INTERVAL': ('performance_log_interval', int),
            'DT_PREDICTION_CONFIDENCE_THRESHOLD': ('prediction_confidence_threshold', float),
            'DT_ENSEMBLE_VOTING_STRATEGY': ('ensemble_voting_strategy', str),
            'DT_ENSEMBLE_SIZE': ('ensemble_size', int),
            'DT_ENABLE_INPUT_VALIDATION': ('enable_input_validation', lambda x: x.lower() == 'true'),
            'DT_MIN_TRAINING_SAMPLES': ('min_training_samples', int),
            'DT_LOG_LEVEL': ('log_level', str),
            'DT_ENABLE_DETAILED_LOGGING': ('enable_detailed_logging', lambda x: x.lower() == 'true'),
        }
        
        for env_var, (config_key, converter) in env_mapping.items():
            env_value = os.getenv(env_var)
            if env_value is not None:
                try:
                    config_data[config_key] = converter(env_value)
                except (ValueError, TypeError) as e:
                    logger = get_logger("DecisionTreeConfig")
                    logger.warning(f"环境变量 {env_var} 值无效: {env_value}, 错误: {e}")
        
        return cls(**config_data)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式
        
        Returns:
            配置字典
        """
        result = {}
        for key, value in self.__dict__.items():
            if not key.startswith('_'):
                result[key] = value
        return result
    
    def save_to_file(self, config_path: str) -> None:
        """保存配置到文件
        
        Args:
            config_path: 配置文件路径
        """
        logger = get_logger("DecisionTreeConfig")
        
        try:
            config_dir = Path(config_path).parent
            config_dir.mkdir(parents=True, exist_ok=True)
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(self.to_dict(), f, indent=2, ensure_ascii=False)
            
            logger.info(f"配置已保存到: {config_path}")
            
        except Exception as e:
            logger.error(f"保存配置文件时出错: {e}")
            raise
    
    def update(self, **kwargs: Any) -> None:
        """更新配置参数
        
        Args:
            **kwargs: 要更新的配置参数
        """
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
            else:
                self.additional_params[key] = value
        
        # 重新验证配置
        self._validate_config()
    
    def get_sklearn_params(self) -> Dict[str, Any]:
        """获取sklearn决策树参数
        
        Returns:
            sklearn参数字典
        """
        return {
            'max_depth': self.max_depth,
            'min_samples_split': self.min_samples_split,
            'min_samples_leaf': self.min_samples_leaf,
            'random_state': self.random_state
        }


# 全局配置实例
_global_config: Optional[DecisionTreeConfig] = None


def get_config() -> DecisionTreeConfig:
    """获取全局配置实例
    
    Returns:
        配置实例
    """
    global _global_config
    
    if _global_config is None:
        # 尝试从环境变量加载，然后从文件加载
        config_file = os.getenv('DT_CONFIG_FILE', 'config/decision_tree.json')
        
        if os.path.exists(config_file):
            _global_config = DecisionTreeConfig.from_file(config_file)
        else:
            _global_config = DecisionTreeConfig.from_env()
    
    return _global_config


def set_config(config: DecisionTreeConfig) -> None:
    """设置全局配置实例
    
    Args:
        config: 配置实例
    """
    global _global_config
    _global_config = config


def reset_config() -> None:
    """重置全局配置实例"""
    global _global_config
    _global_config = None