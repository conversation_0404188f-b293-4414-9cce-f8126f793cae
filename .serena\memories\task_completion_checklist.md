# 任务完成检查清单

## 代码开发完成后必须执行的步骤

### 1. 代码质量检查
- [ ] 运行 `python -m black src/` 进行代码格式化
- [ ] 运行 `python -m flake8 src/` 检查代码规范
- [ ] 运行 `python -m mypy src/` 进行类型检查
- [ ] 确保所有检查通过，无错误和警告

### 2. 测试验证
- [ ] 运行 `python -m pytest tests/` 执行所有测试
- [ ] 运行 `python -m pytest tests/ --cov=src` 检查测试覆盖率
- [ ] 确保测试覆盖率不低于80%
- [ ] 运行主程序 `python main_entry.py` 验证功能正常

### 3. 配置一致性检查
- [ ] 运行 `python scripts/config_consistency_checker.py` 检查配置一致性
- [ ] 确保所有配置文件路径正确
- [ ] 验证配置参数的有效性

### 4. 文档更新
- [ ] 更新相关的README.md文件
- [ ] 更新API文档和注释
- [ ] 更新项目结构说明
- [ ] 记录重要的设计决策

### 5. 性能验证
- [ ] 运行回测验证预测准确性
- [ ] 检查内存使用情况
- [ ] 验证处理速度是否满足要求
- [ ] 确保杀号成功率≥90%，2+1命中率≥60%

### 6. 版本控制
- [ ] 提交所有更改到Git
- [ ] 编写清晰的提交信息
- [ ] 确保没有遗留的临时文件
- [ ] 更新版本号（如果需要）

### 7. 部署准备
- [ ] 确认依赖包列表完整
- [ ] 验证在干净环境中的安装
- [ ] 检查数据文件的完整性
- [ ] 准备部署文档