# 交互式启动器使用说明
# Interactive Launcher User Guide

## 概述 / Overview

交互式启动器 (`launcher.py`) 提供了一个用户友好的命令行界面，让您可以通过菜单选择的方式运行不同的预测系统，而无需记住复杂的命令行参数。

The Interactive Launcher (`launcher.py`) provides a user-friendly command-line interface that allows you to run different prediction systems through menu selection without needing to remember complex command-line arguments.

## 使用方法 / Usage

### 启动启动器 / Start the Launcher
```bash
python launcher.py
```

### 主菜单 / Main Menu

启动后，您将看到主菜单：
After starting, you will see the main menu:

```
Select Prediction System:
  1. Main System - Ensemble Learning with Red Ball Size Optimizer
  2. Ultimate System - Multi-mode Prediction System  
  3. Refactored System - Interactive Architecture Demo
  4. Simple Refactored System
  0. Exit
```

## 系统介绍 / System Introduction

### 1. 主系统 (Main System)
- **特点**: 集成学习版本，包含红球大小比优化器
- **功能**: 
  - 预测下一期
  - 回测分析
  - 性能对比
- **推荐**: 日常预测使用，性能最优

### 2. 终极系统 (Ultimate System)
- **特点**: 多模式预测系统
- **模式选择**:
  - Auto Mode: 自动选择最佳可用模式
  - Basic Mode: 基础预测功能
  - Enhanced Mode: 增强预测功能
  - Super Mode: 超级预测功能
  - Ultimate Mode: 终极预测功能
- **推荐**: 需要特定模式时使用

### 3. 重构系统 (Refactored System)
- **特点**: 交互式架构演示
- **功能**: 展示系统架构和设计模式
- **推荐**: 学习和演示用途

### 4. 简化重构系统 (Simple Refactored System)
- **特点**: 简化版本的重构系统
- **推荐**: 轻量级使用场景

## 操作流程 / Operation Flow

### 典型使用流程:

1. **启动启动器**
   ```bash
   python launcher.py
   ```

2. **选择系统** (例如选择 "1" - 主系统)

3. **选择操作**:
   - `1` - 预测下一期: 执行单次预测
   - `2` - 回测分析: 执行历史数据回测
   - `3` - 性能对比: 对比不同算法性能
   - `0` - 返回主菜单

4. **输入参数** (如回测期数)

5. **查看结果**

6. **继续操作或退出**

## 功能特点 / Features

### ✅ 优点 / Advantages
- **无需参数**: 不需要记住命令行参数
- **交互式**: 菜单驱动，操作简单
- **多系统支持**: 支持所有预测系统
- **错误处理**: 友好的错误提示
- **灵活导航**: 可以随时返回上级菜单

### 🎯 适用场景 / Use Cases
- 日常预测操作
- 系统功能测试
- 新用户学习使用
- 演示和展示
- 批量操作

## 示例操作 / Example Operations

### 示例1: 运行主系统预测
```
1. 运行: python launcher.py
2. 选择: 1 (Main System)
3. 选择: 1 (Predict Next Period)
4. 查看预测结果
5. 按回车继续
```

### 示例2: 执行回测分析
```
1. 运行: python launcher.py
2. 选择: 1 (Main System)
3. 选择: 2 (Backtest Analysis)
4. 输入: 20 (回测20期)
5. 查看回测结果
```

### 示例3: 使用终极系统
```
1. 运行: python launcher.py
2. 选择: 2 (Ultimate System)
3. 选择: 1 (Auto Mode)
4. 选择: 1 (Predict Next Period)
5. 查看预测结果
```

## 故障排除 / Troubleshooting

### 常见问题 / Common Issues

1. **系统导入失败**
   - 检查Python路径设置
   - 确认所需模块已安装
   - 检查文件路径是否正确

2. **编码问题**
   - 确保终端支持UTF-8编码
   - 在Windows上可能需要设置代码页

3. **权限问题**
   - 确保有读取项目文件的权限
   - 检查Python执行权限

### 解决方案 / Solutions

```bash
# 检查Python版本
python --version

# 检查模块导入
python -c "import sys; print(sys.path)"

# 测试启动器
python demo_launcher.py
```

## 技术细节 / Technical Details

### 文件结构 / File Structure
```
launcher.py              # 主启动器文件
demo_launcher.py         # 演示脚本
LAUNCHER_README.md       # 使用说明
src/systems/            # 各个预测系统
```

### 依赖关系 / Dependencies
- Python 3.7+
- 项目中的预测系统模块
- 标准库: sys, os, pathlib

## 更新日志 / Changelog

### v1.0.0
- 初始版本
- 支持4个预测系统
- 交互式菜单界面
- 基本错误处理

## 联系支持 / Support

如果您在使用过程中遇到问题，请：
1. 查看本文档的故障排除部分
2. 运行 `python demo_launcher.py` 进行诊断
3. 检查系统日志和错误信息

---

**享受使用交互式启动器！**
**Enjoy using the Interactive Launcher!**