"""
基于数据驱动的智能预测算法
结合历史数据分析结果，提供更准确的预测
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any
from collections import Counter, defaultdict
import random
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.utils.utils import (
    parse_numbers, calculate_odd_even_ratio, calculate_size_ratio_red, 
    calculate_size_ratio_blue, ratio_to_state, get_all_red_states
)
from src.analysis.historical_pattern_analyzer import HistoricalPatternAnalyzer


class HistoricalFrequencyPredictor:
    """基于历史频率的预测器"""
    
    def __init__(self):
        self.name = "historical_frequency_predictor"
        self.frequency_weights = {}
        self.transition_weights = {}
        
    def predict_ratio(self, data: pd.DataFrame, ratio_type: str) -> Dict[str, Any]:
        """基于历史频率预测"""
        if ratio_type != 'odd_even':
            return {'prediction': '3:2', 'confidence': 0.3, 'method': 'unsupported_type'}
        
        if len(data) < 20:
            return {'prediction': '3:2', 'confidence': 0.3, 'method': 'insufficient_data'}
        
        # 分析历史频率
        analyzer = HistoricalPatternAnalyzer()
        analyzer.load_data(data)
        analysis = analyzer.analyze_red_odd_even_patterns()
        
        # 基于频率分布预测
        frequency_analysis = analysis['frequency_analysis']
        
        # 计算调整后的概率（考虑最近趋势）
        adjusted_probabilities = {}
        recent_trend = analysis.get('recent_trend', {})
        
        for state in get_all_red_states():
            base_freq = frequency_analysis.get(state, {}).get('frequency', 0)
            
            # 趋势调整
            trend_multiplier = 1.0
            if state in recent_trend and isinstance(recent_trend[state], dict):
                trend_data = recent_trend[state]
                if trend_data.get('is_trending_up', False):
                    trend_multiplier = 1.3
                elif trend_data.get('is_trending_down', False):
                    trend_multiplier = 0.7
            
            adjusted_probabilities[state] = base_freq * trend_multiplier
        
        # 归一化概率
        total_prob = sum(adjusted_probabilities.values())
        if total_prob > 0:
            for state in adjusted_probabilities:
                adjusted_probabilities[state] /= total_prob
        
        # 选择概率最高的状态
        if adjusted_probabilities:
            predicted_state = max(adjusted_probabilities.items(), key=lambda x: x[1])
            confidence = predicted_state[1]
            return {
                'prediction': predicted_state[0],
                'confidence': min(0.9, confidence * 2),  # 提升置信度
                'method': 'historical_frequency_with_trend'
            }
        
        return {'prediction': '3:2', 'confidence': 0.4, 'method': 'fallback'}


class TransitionPatternPredictor:
    """基于状态转移模式的预测器"""
    
    def __init__(self):
        self.name = "transition_pattern_predictor"
        
    def predict_ratio(self, data: pd.DataFrame, ratio_type: str) -> Dict[str, Any]:
        """基于状态转移模式预测"""
        if ratio_type != 'odd_even':
            return {'prediction': '3:2', 'confidence': 0.3, 'method': 'unsupported_type'}
        
        if len(data) < 10:
            return {'prediction': '3:2', 'confidence': 0.3, 'method': 'insufficient_data'}
        
        # 获取当前状态
        current_row = data.iloc[-1]
        red_balls, _ = parse_numbers(current_row)
        red_odd, red_even = calculate_odd_even_ratio(red_balls)
        current_state = ratio_to_state((red_odd, red_even))
        
        # 分析转移模式
        analyzer = HistoricalPatternAnalyzer()
        analyzer.load_data(data)
        analysis = analyzer.analyze_red_odd_even_patterns()
        
        transition_probs = analysis.get('transition_probabilities', {})
        
        if current_state in transition_probs:
            transitions = transition_probs[current_state]
            if transitions:
                # 选择转移概率最高的状态
                best_transition = max(transitions.items(), key=lambda x: x[1])
                confidence = best_transition[1]
                
                return {
                    'prediction': best_transition[0],
                    'confidence': confidence,
                    'method': 'transition_pattern'
                }
        
        return {'prediction': '3:2', 'confidence': 0.4, 'method': 'no_transition_data'}


class MultiTimeWindowPredictor:
    """多时间窗口预测器"""
    
    def __init__(self):
        self.name = "multi_time_window_predictor"
        self.windows = [10, 20, 50, 100]  # 不同的时间窗口
        
    def predict_ratio(self, data: pd.DataFrame, ratio_type: str) -> Dict[str, Any]:
        """基于多时间窗口预测"""
        if ratio_type != 'odd_even':
            return {'prediction': '3:2', 'confidence': 0.3, 'method': 'unsupported_type'}
        
        if len(data) < 20:
            return {'prediction': '3:2', 'confidence': 0.3, 'method': 'insufficient_data'}
        
        # 分析不同时间窗口的模式
        window_predictions = {}
        
        for window in self.windows:
            if len(data) >= window:
                window_data = data.tail(window)
                prediction = self._analyze_window(window_data)
                window_predictions[window] = prediction
        
        if not window_predictions:
            return {'prediction': '3:2', 'confidence': 0.4, 'method': 'no_valid_windows'}
        
        # 加权投票（较短窗口权重更高）
        weighted_votes = defaultdict(float)
        total_weight = 0
        
        for window, prediction in window_predictions.items():
            weight = 1.0 / window  # 较短窗口权重更高
            weighted_votes[prediction['prediction']] += weight * prediction['confidence']
            total_weight += weight
        
        # 选择加权得分最高的预测
        if weighted_votes:
            best_prediction = max(weighted_votes.items(), key=lambda x: x[1])
            confidence = best_prediction[1] / total_weight if total_weight > 0 else 0.5
            
            return {
                'prediction': best_prediction[0],
                'confidence': min(0.9, confidence),
                'method': 'multi_time_window'
            }
        
        return {'prediction': '3:2', 'confidence': 0.4, 'method': 'fallback'}
    
    def _analyze_window(self, window_data: pd.DataFrame) -> Dict[str, Any]:
        """分析单个时间窗口"""
        states = []
        for _, row in window_data.iterrows():
            red_balls, _ = parse_numbers(row)
            red_odd, red_even = calculate_odd_even_ratio(red_balls)
            state = ratio_to_state((red_odd, red_even))
            states.append(state)
        
        if not states:
            return {'prediction': '3:2', 'confidence': 0.3}
        
        # 分析最近趋势
        state_counts = Counter(states)
        recent_states = states[-5:] if len(states) >= 5 else states
        recent_counts = Counter(recent_states)
        
        # 计算趋势权重
        trend_weights = {}
        for state in get_all_red_states():
            overall_freq = state_counts.get(state, 0) / len(states)
            recent_freq = recent_counts.get(state, 0) / len(recent_states)
            
            # 如果最近频率高于整体频率，给予更高权重
            if overall_freq > 0:
                trend_weights[state] = recent_freq / overall_freq
            else:
                trend_weights[state] = recent_freq
        
        # 选择趋势权重最高的状态
        if trend_weights:
            best_state = max(trend_weights.items(), key=lambda x: x[1])
            confidence = min(0.8, best_state[1] / 2)  # 调整置信度
            return {
                'prediction': best_state[0],
                'confidence': confidence
            }
        
        return {'prediction': '3:2', 'confidence': 0.4}


class CorrelationBasedPredictor:
    """基于特征关联的预测器"""
    
    def __init__(self):
        self.name = "correlation_based_predictor"
        
    def predict_ratio(self, data: pd.DataFrame, ratio_type: str) -> Dict[str, Any]:
        """基于特征关联预测"""
        if ratio_type != 'odd_even':
            return {'prediction': '3:2', 'confidence': 0.3, 'method': 'unsupported_type'}
        
        if len(data) < 30:
            return {'prediction': '3:2', 'confidence': 0.3, 'method': 'insufficient_data'}
        
        # 获取当前期的其他特征
        current_row = data.iloc[-1]
        red_balls, blue_balls = parse_numbers(current_row)
        
        current_red_size = ratio_to_state(calculate_size_ratio_red(red_balls))
        current_red_sum = sum(red_balls)
        current_red_span = max(red_balls) - min(red_balls)
        
        # 分析历史关联模式
        correlations = self._analyze_correlations(data)
        
        # 基于当前特征预测奇偶比
        predictions = []
        
        # 基于红球大小比关联
        if current_red_size in correlations['size_correlation']:
            size_based_pred = correlations['size_correlation'][current_red_size]
            predictions.append(size_based_pred)
        
        # 基于红球和值关联
        sum_based_pred = self._predict_by_sum(current_red_sum, correlations['sum_correlation'])
        if sum_based_pred:
            predictions.append(sum_based_pred)
        
        # 基于红球跨度关联
        span_based_pred = self._predict_by_span(current_red_span, correlations['span_correlation'])
        if span_based_pred:
            predictions.append(span_based_pred)
        
        if predictions:
            # 投票选择最佳预测
            prediction_votes = Counter(pred['prediction'] for pred in predictions)
            best_prediction = prediction_votes.most_common(1)[0]
            
            # 计算平均置信度
            avg_confidence = np.mean([pred['confidence'] for pred in predictions 
                                    if pred['prediction'] == best_prediction[0]])
            
            return {
                'prediction': best_prediction[0],
                'confidence': min(0.8, avg_confidence),
                'method': 'correlation_based'
            }
        
        return {'prediction': '3:2', 'confidence': 0.4, 'method': 'no_correlation_found'}
    
    def _analyze_correlations(self, data: pd.DataFrame) -> Dict[str, Any]:
        """分析特征关联"""
        size_correlation = defaultdict(lambda: defaultdict(int))
        sum_ranges = defaultdict(list)
        span_ranges = defaultdict(list)
        
        for _, row in data.iterrows():
            red_balls, _ = parse_numbers(row)
            
            red_odd_even = ratio_to_state(calculate_odd_even_ratio(red_balls))
            red_size = ratio_to_state(calculate_size_ratio_red(red_balls))
            red_sum = sum(red_balls)
            red_span = max(red_balls) - min(red_balls)
            
            size_correlation[red_size][red_odd_even] += 1
            sum_ranges[red_odd_even].append(red_sum)
            span_ranges[red_odd_even].append(red_span)
        
        # 转换为概率
        size_probs = {}
        for size_state in size_correlation:
            total = sum(size_correlation[size_state].values())
            if total > 0:
                best_odd_even = max(size_correlation[size_state].items(), key=lambda x: x[1])
                size_probs[size_state] = {
                    'prediction': best_odd_even[0],
                    'confidence': best_odd_even[1] / total
                }
        
        # 分析和值范围
        sum_analysis = {}
        for state, sums in sum_ranges.items():
            if sums:
                sum_analysis[state] = {
                    'mean': np.mean(sums),
                    'std': np.std(sums),
                    'range': (min(sums), max(sums))
                }
        
        # 分析跨度范围
        span_analysis = {}
        for state, spans in span_ranges.items():
            if spans:
                span_analysis[state] = {
                    'mean': np.mean(spans),
                    'std': np.std(spans),
                    'range': (min(spans), max(spans))
                }
        
        return {
            'size_correlation': size_probs,
            'sum_correlation': sum_analysis,
            'span_correlation': span_analysis
        }
    
    def _predict_by_sum(self, current_sum: int, sum_correlation: Dict) -> Dict[str, Any]:
        """基于和值预测"""
        best_match = None
        best_score = 0
        
        for state, data in sum_correlation.items():
            mean = data['mean']
            std = data['std']
            
            # 计算当前和值与该状态的匹配度
            if std > 0:
                z_score = abs(current_sum - mean) / std
                score = max(0, 1 - z_score / 3)  # 3个标准差内有效
                
                if score > best_score:
                    best_score = score
                    best_match = state
        
        if best_match and best_score > 0.3:
            return {
                'prediction': best_match,
                'confidence': best_score
            }
        
        return None
    
    def _predict_by_span(self, current_span: int, span_correlation: Dict) -> Dict[str, Any]:
        """基于跨度预测"""
        best_match = None
        best_score = 0
        
        for state, data in span_correlation.items():
            mean = data['mean']
            std = data['std']
            
            # 计算当前跨度与该状态的匹配度
            if std > 0:
                z_score = abs(current_span - mean) / std
                score = max(0, 1 - z_score / 3)  # 3个标准差内有效
                
                if score > best_score:
                    best_score = score
                    best_match = state
        
        if best_match and best_score > 0.3:
            return {
                'prediction': best_match,
                'confidence': best_score
            }
        
        return None
