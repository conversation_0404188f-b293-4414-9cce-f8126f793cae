#!/usr/bin/env python3
"""
大乐透预测系统 - 架构重构版本
Enhanced Lottery Prediction System - Refactored Architecture

Phase 4 完成: 架构重构
- [成功] 依赖注入容器实现
- [成功] 工厂模式统一预测器创建
- [成功] 门面模式简化系统接口
- [成功] 配置驱动的模块化架构
- [成功] 解耦模块间依赖关系

现在开始 Phase 5: 性能优化与测试
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """主入口函数"""
    print("大乐透预测系统 - 架构重构版本")
    print("=" * 60)
    print("Phase 4 完成: 架构重构")
    print("   依赖注入容器")
    print("   工厂模式")
    print("   门面模式")
    print("   配置驱动")
    print("当前状态: 准备开始 Phase 5 - 性能优化与测试")
    print("=" * 60)

    try:
        # 导入重构后的主系统
        from src.systems.refactored_main import RefactoredLotterySystem

        # 创建重构后的系统实例
        system = RefactoredLotterySystem()

        # 加载数据
        if not system.load_data():
            print("数据加载失败，退出系统")
            return 1

        # 运行交互模式
        system.run_interactive_mode()

    except Exception as e:
        print(f"系统运行错误: {e}")
        import traceback
        traceback.print_exc()
        return 1

    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)