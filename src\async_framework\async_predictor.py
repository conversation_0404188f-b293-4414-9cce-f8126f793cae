"""
异步预测框架
提供高性能的异步预测能力，支持并发处理和批量预测
"""

import asyncio
import time
from typing import List, Dict, Any, Optional, Callable
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import pandas as pd
import logging
from dataclasses import dataclass

from ..core.interfaces import (
    IAsyncPredictor, IPredictor, PredictionResult, 
    PredictionType, BallType
)


@dataclass
class AsyncPredictionTask:
    """异步预测任务"""
    task_id: str
    data: pd.DataFrame
    target_index: int
    predictor: IPredictor
    kwargs: Dict[str, Any]
    created_at: float
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = time.time()


@dataclass
class AsyncPredictionResult:
    """异步预测结果"""
    task_id: str
    result: Optional[PredictionResult]
    error: Optional[Exception]
    processing_time: float
    completed_at: float


class AsyncPredictorAdapter(IAsyncPredictor):
    """异步预测器适配器
    
    将同步预测器包装为异步预测器，提供并发处理能力
    """
    
    def __init__(self, 
                 sync_predictor: IPredictor,
                 max_workers: int = 4,
                 use_process_pool: bool = False):
        self.sync_predictor = sync_predictor
        self.max_workers = max_workers
        self.use_process_pool = use_process_pool
        self.executor_class = ProcessPoolExecutor if use_process_pool else ThreadPoolExecutor
        self.logger = logging.getLogger(__name__)
        
        # 并发控制
        self.semaphore = asyncio.Semaphore(max_workers)
        self.active_tasks: Dict[str, AsyncPredictionTask] = {}
    
    async def predict_async(self, 
                           data: pd.DataFrame, 
                           target_index: int, 
                           **kwargs) -> PredictionResult:
        """异步预测单个目标"""
        task_id = f"predict_{target_index}_{time.time()}"
        
        async with self.semaphore:
            start_time = time.time()
            
            try:
                # 在线程池中执行同步预测
                loop = asyncio.get_event_loop()
                with self.executor_class(max_workers=1) as executor:
                    result = await loop.run_in_executor(
                        executor,
                        self._sync_predict_wrapper,
                        data, target_index, kwargs
                    )
                
                processing_time = time.time() - start_time
                self.logger.debug(f"异步预测完成，任务ID: {task_id}, 耗时: {processing_time:.4f}秒")
                
                return result
                
            except Exception as e:
                processing_time = time.time() - start_time
                self.logger.error(f"异步预测失败，任务ID: {task_id}, 错误: {e}, 耗时: {processing_time:.4f}秒")
                raise
    
    async def batch_predict_async(self, 
                                 data: pd.DataFrame, 
                                 target_indices: List[int], 
                                 **kwargs) -> List[PredictionResult]:
        """批量异步预测"""
        if not target_indices:
            return []
        
        self.logger.info(f"开始批量异步预测，目标数量: {len(target_indices)}")
        start_time = time.time()
        
        # 创建异步任务
        tasks = []
        for target_index in target_indices:
            task = self.predict_async(data, target_index, **kwargs)
            tasks.append(task)
        
        # 并发执行所有任务
        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果和异常
            successful_results = []
            failed_count = 0
            
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    self.logger.error(f"批量预测中第{i}个任务失败: {result}")
                    failed_count += 1
                    # 创建一个错误结果
                    error_result = PredictionResult(
                        prediction_type=PredictionType.NUMBER_GENERATION,
                        ball_type=BallType.RED,
                        value=None,
                        confidence=0.0,
                        metadata={'error': str(result), 'target_index': target_indices[i]}
                    )
                    successful_results.append(error_result)
                else:
                    successful_results.append(result)
            
            total_time = time.time() - start_time
            success_rate = (len(target_indices) - failed_count) / len(target_indices)
            
            self.logger.info(f"批量异步预测完成，成功率: {success_rate:.2%}, 总耗时: {total_time:.4f}秒")
            
            return successful_results
            
        except Exception as e:
            total_time = time.time() - start_time
            self.logger.error(f"批量异步预测失败: {e}, 耗时: {total_time:.4f}秒")
            raise
    
    def _sync_predict_wrapper(self, 
                             data: pd.DataFrame, 
                             target_index: int, 
                             kwargs: Dict[str, Any]) -> PredictionResult:
        """同步预测包装器"""
        try:
            return self.sync_predictor.predict(data, target_index, **kwargs)
        except Exception as e:
            # 创建错误结果
            return PredictionResult(
                prediction_type=self.sync_predictor.get_prediction_type(),
                ball_type=BallType.RED,  # 默认值
                value=None,
                confidence=0.0,
                metadata={'error': str(e), 'target_index': target_index}
            )


class AsyncBacktestFramework:
    """异步回测框架
    
    确保数据从旧到新的顺序进行回测
    """
    
    def __init__(self, max_workers: int = 4):
        self.max_workers = max_workers
        self.logger = logging.getLogger(__name__)
    
    async def run_backtest_async(self, 
                                predictor: IAsyncPredictor,
                                data: pd.DataFrame,
                                periods: int,
                                progress_callback: Optional[Callable[[int, int], None]] = None) -> Dict[str, Any]:
        """运行异步回测
        
        Args:
            predictor: 异步预测器
            data: 数据（按时间从新到旧排序，即最新数据在索引0）
            periods: 回测期数
            progress_callback: 进度回调函数
        """
        if periods <= 0 or periods > len(data) - 1:
            raise ValueError(f"回测期数无效: {periods}, 数据长度: {len(data)}")
        
        # 确保数据顺序：从新到旧（最新数据在索引0）
        # 回测时从历史数据开始，即从索引较大的位置开始
        start_time = time.time()
        
        # 创建回测任务：从旧到新进行回测
        # 例如：如果要回测10期，数据长度100，则从索引99-10=89开始，到索引99结束
        # 但预测时使用的是目标期之后的数据作为历史数据
        backtest_results = []
        
        for i in range(periods):
            # 计算目标索引：从数据末尾开始往前回测
            target_index = len(data) - periods + i
            
            if target_index <= 0:
                continue
                
            # 异步预测
            try:
                prediction = await predictor.predict_async(data, target_index)
                backtest_results.append({
                    'target_index': target_index,
                    'period_number': data.iloc[target_index]['期号'],
                    'prediction': prediction,
                    'actual_data': data.iloc[target_index]
                })
                
                if progress_callback:
                    progress_callback(i + 1, periods)
                    
            except Exception as e:
                self.logger.error(f"回测第{i+1}期失败: {e}")
        
        # 计算回测结果
        results = self._calculate_backtest_results_v2(backtest_results)
        
        total_time = time.time() - start_time
        results['total_processing_time'] = total_time
        results['average_time_per_prediction'] = total_time / periods if periods > 0 else 0
        
        return results
    
    def _calculate_backtest_results_v2(self, backtest_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算回测结果（新版本）"""
        if not backtest_results:
            return {
                'total_predictions': 0,
                'successful_predictions': 0,
                'failed_predictions': 0,
                'success_rate': 0,
                'backtest_details': []
            }
        
        successful_count = 0
        failed_count = 0
        details = []
        
        for result in backtest_results:
            prediction = result['prediction']
            actual_data = result['actual_data']
            
            if prediction.value is not None:
                successful_count += 1
                success = True
            else:
                failed_count += 1
                success = False
            
            details.append({
                'period_number': result['period_number'],
                'target_index': result['target_index'],
                'success': success,
                'confidence': prediction.confidence,
                'prediction_type': prediction.prediction_type.value,
                'ball_type': prediction.ball_type.value
            })
        
        total_count = len(backtest_results)
        success_rate = successful_count / total_count if total_count > 0 else 0
        
        return {
            'total_predictions': total_count,
            'successful_predictions': successful_count,
            'failed_predictions': failed_count,
            'success_rate': success_rate,
            'backtest_details': details
        }


class AsyncPredictionPipeline:
    """异步预测管道
    
    支持多个预测器的串行或并行执行
    """
    
    def __init__(self, predictors: List[IAsyncPredictor]):
        self.predictors = predictors
        self.logger = logging.getLogger(__name__)
    
    async def execute_parallel(self, 
                              data: pd.DataFrame, 
                              target_index: int, 
                              **kwargs) -> List[PredictionResult]:
        """并行执行所有预测器"""
        if not self.predictors:
            return []
        
        self.logger.debug(f"并行执行 {len(self.predictors)} 个预测器")
        start_time = time.time()
        
        # 创建并行任务
        tasks = []
        for predictor in self.predictors:
            task = predictor.predict_async(data, target_index, **kwargs)
            tasks.append(task)
        
        # 并发执行
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        valid_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                self.logger.error(f"预测器 {i} 执行失败: {result}")
            else:
                valid_results.append(result)
        
        processing_time = time.time() - start_time
        self.logger.debug(f"并行预测完成，有效结果: {len(valid_results)}/{len(self.predictors)}, 耗时: {processing_time:.4f}秒")
        
        return valid_results
    
    async def execute_sequential(self, 
                                data: pd.DataFrame, 
                                target_index: int, 
                                **kwargs) -> List[PredictionResult]:
        """串行执行所有预测器"""
        if not self.predictors:
            return []
        
        self.logger.debug(f"串行执行 {len(self.predictors)} 个预测器")
        start_time = time.time()
        
        results = []
        for i, predictor in enumerate(self.predictors):
            try:
                result = await predictor.predict_async(data, target_index, **kwargs)
                results.append(result)
            except Exception as e:
                self.logger.error(f"预测器 {i} 执行失败: {e}")
        
        processing_time = time.time() - start_time
        self.logger.debug(f"串行预测完成，有效结果: {len(results)}/{len(self.predictors)}, 耗时: {processing_time:.4f}秒")
        
        return results