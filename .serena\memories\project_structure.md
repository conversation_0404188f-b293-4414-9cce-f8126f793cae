# 项目结构说明

## 顶层目录结构
```
lottery-prediction-system/
├── src/                    # 源代码目录
├── tests/                  # 测试代码
├── scripts/                # 独立脚本
├── data/                   # 数据文件
├── docs/                   # 文档
├── config/                 # 配置文件
├── logs/                   # 日志文件
├── models/                 # 模型文件
├── notebooks/              # Jupyter笔记本
└── main_entry.py          # 主程序入口
```

## src目录结构
- **apps/**: 主应用程序
- **core/**: 核心模块(分析器、基础类)
- **models/**: 预测模型(马尔科夫、贝叶斯、神经网络)
- **generators/**: 号码生成器
- **utils/**: 工具函数
- **systems/**: 系统模块
- **framework/**: 统一框架
- **decision_tree/**: 决策树相关
- **features/**: 特征工程
- **tools/**: 优化工具

## 关键文件
- **main_entry.py**: 系统主入口点
- **src/apps/main.py**: 实际主程序逻辑
- **data/raw/dlt_data.csv**: 历史开奖数据
- **config/**: 各种配置文件
- **requirements.txt**: 依赖包列表

## 数据目录
- **data/raw/**: 原始数据
- **data/processed/**: 处理后数据
- **data/results/**: 结果文件
- **data/backups/**: 备份文件