"""统一异常处理系统

定义项目中使用的标准异常类型，提供统一的错误处理机制。
"""

import traceback
from typing import Optional, Dict, Any, List
from enum import Enum


class ErrorCode(Enum):
    """错误代码枚举"""

    # 通用错误
    UNKNOWN_ERROR = "E0001"
    INVALID_PARAMETER = "E0002"
    CONFIGURATION_ERROR = "E0003"
    INITIALIZATION_ERROR = "E0004"

    # 数据相关错误
    DATA_NOT_FOUND = "E1001"
    DATA_INVALID_FORMAT = "E1002"
    DATA_INSUFFICIENT = "E1003"
    DATA_CORRUPTION = "E1004"
    DATA_VALIDATION_ERROR = "E1005"

    # 模型相关错误
    MODEL_NOT_TRAINED = "E2001"
    MODEL_TRAINING_ERROR = "E2002"
    MODEL_PREDICTION_ERROR = "E2003"
    MODEL_LOADING_ERROR = "E2004"
    MODEL_SAVING_ERROR = "E2005"
    MODEL_VERSION_MISMATCH = "E2006"

    # 预测相关错误
    PREDICTION_INPUT_ERROR = "E3001"
    PREDICTION_OUTPUT_ERROR = "E3002"
    PREDICTION_TIMEOUT = "E3003"
    PREDICTION_RESOURCE_ERROR = "E3004"

    # 文件系统错误
    FILE_NOT_FOUND = "E4001"
    FILE_ACCESS_ERROR = "E4002"
    FILE_FORMAT_ERROR = "E4003"
    DIRECTORY_ERROR = "E4004"

    # 网络相关错误
    NETWORK_ERROR = "E5001"
    API_ERROR = "E5002"
    TIMEOUT_ERROR = "E5003"

    # 算法相关错误
    ALGORITHM_ERROR = "E6001"
    CONVERGENCE_ERROR = "E6002"
    OPTIMIZATION_ERROR = "E6003"

    # 系统相关错误
    MEMORY_ERROR = "E7001"
    RESOURCE_ERROR = "E7002"
    PERMISSION_ERROR = "E7003"


class LotteryPredictorException(Exception):
    """项目基础异常类"""

    def __init__(
        self,
        message: str,
        error_code: ErrorCode = ErrorCode.UNKNOWN_ERROR,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None,
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        self.cause = cause
        self.traceback_info = traceback.format_exc() if cause else None

    def __str__(self) -> str:
        base_msg = f"[{self.error_code.value}] {self.message}"
        if self.details:
            details_str = ", ".join([f"{k}={v}" for k, v in self.details.items()])
            base_msg += f" (详情: {details_str})"
        if self.cause:
            base_msg += f" (原因: {str(self.cause)})"
        return base_msg

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "error_code": self.error_code.value,
            "message": self.message,
            "details": self.details,
            "cause": str(self.cause) if self.cause else None,
            "traceback": self.traceback_info,
        }


class DataException(LotteryPredictorException):
    """数据相关异常"""

    def __init__(
        self,
        message: str,
        error_code: ErrorCode = ErrorCode.DATA_INVALID_FORMAT,
        data_path: Optional[str] = None,
        expected_format: Optional[str] = None,
        actual_format: Optional[str] = None,
        **kwargs,
    ):
        details = kwargs.get("details", {})
        if data_path:
            details["data_path"] = data_path
        if expected_format:
            details["expected_format"] = expected_format
        if actual_format:
            details["actual_format"] = actual_format

        super().__init__(message, error_code, details, kwargs.get("cause"))


class ModelException(LotteryPredictorException):
    """模型相关异常"""

    def __init__(
        self,
        message: str,
        error_code: ErrorCode = ErrorCode.MODEL_TRAINING_ERROR,
        model_name: Optional[str] = None,
        model_version: Optional[str] = None,
        model_path: Optional[str] = None,
        **kwargs,
    ):
        details = kwargs.get("details", {})
        if model_name:
            details["model_name"] = model_name
        if model_version:
            details["model_version"] = model_version
        if model_path:
            details["model_path"] = model_path

        super().__init__(message, error_code, details, kwargs.get("cause"))


class PredictionException(LotteryPredictorException):
    """预测相关异常"""

    def __init__(
        self,
        message: str,
        error_code: ErrorCode = ErrorCode.PREDICTION_INPUT_ERROR,
        predictor_name: Optional[str] = None,
        input_data: Optional[Any] = None,
        prediction_type: Optional[str] = None,
        **kwargs,
    ):
        details = kwargs.get("details", {})
        if predictor_name:
            details["predictor_name"] = predictor_name
        if input_data is not None:
            details["input_data"] = str(input_data)[:200]  # 限制长度
        if prediction_type:
            details["prediction_type"] = prediction_type

        super().__init__(message, error_code, details, kwargs.get("cause"))


class ConfigurationException(LotteryPredictorException):
    """配置相关异常"""

    def __init__(
        self,
        message: str,
        error_code: ErrorCode = ErrorCode.CONFIGURATION_ERROR,
        config_key: Optional[str] = None,
        config_value: Optional[Any] = None,
        config_file: Optional[str] = None,
        **kwargs,
    ):
        details = kwargs.get("details", {})
        if config_key:
            details["config_key"] = config_key
        if config_value is not None:
            details["config_value"] = str(config_value)
        if config_file:
            details["config_file"] = config_file

        super().__init__(message, error_code, details, kwargs.get("cause"))


class ValidationException(LotteryPredictorException):
    """验证相关异常"""

    def __init__(
        self,
        message: str,
        error_code: ErrorCode = ErrorCode.DATA_VALIDATION_ERROR,
        validation_errors: Optional[List[str]] = None,
        field_name: Optional[str] = None,
        expected_value: Optional[Any] = None,
        actual_value: Optional[Any] = None,
        **kwargs,
    ):
        details = kwargs.get("details", {})
        if validation_errors:
            details["validation_errors"] = validation_errors
        if field_name:
            details["field_name"] = field_name
        if expected_value is not None:
            details["expected_value"] = str(expected_value)
        if actual_value is not None:
            details["actual_value"] = str(actual_value)

        super().__init__(message, error_code, details, kwargs.get("cause"))


class AlgorithmException(LotteryPredictorException):
    """算法相关异常"""

    def __init__(
        self,
        message: str,
        error_code: ErrorCode = ErrorCode.ALGORITHM_ERROR,
        algorithm_name: Optional[str] = None,
        parameters: Optional[Dict[str, Any]] = None,
        iteration: Optional[int] = None,
        **kwargs,
    ):
        details = kwargs.get("details", {})
        if algorithm_name:
            details["algorithm_name"] = algorithm_name
        if parameters:
            details["parameters"] = parameters
        if iteration is not None:
            details["iteration"] = iteration

        super().__init__(message, error_code, details, kwargs.get("cause"))


class ResourceException(LotteryPredictorException):
    """资源相关异常"""

    def __init__(
        self,
        message: str,
        error_code: ErrorCode = ErrorCode.RESOURCE_ERROR,
        resource_type: Optional[str] = None,
        resource_name: Optional[str] = None,
        available_resources: Optional[Dict[str, Any]] = None,
        **kwargs,
    ):
        details = kwargs.get("details", {})
        if resource_type:
            details["resource_type"] = resource_type
        if resource_name:
            details["resource_name"] = resource_name
        if available_resources:
            details["available_resources"] = available_resources

        super().__init__(message, error_code, details, kwargs.get("cause"))


def handle_exception(
    exception: Exception,
    logger=None,
    reraise: bool = True,
    default_error_code: ErrorCode = ErrorCode.UNKNOWN_ERROR,
) -> Optional[LotteryPredictorException]:
    """统一异常处理函数

    Args:
        exception: 原始异常
        logger: 日志器
        reraise: 是否重新抛出异常
        default_error_code: 默认错误代码

    Returns:
        LotteryPredictorException: 标准化异常（如果不重新抛出）
    """
    if isinstance(exception, LotteryPredictorException):
        lottery_exception = exception
    else:
        # 将普通异常转换为项目异常
        lottery_exception = LotteryPredictorException(
            message=str(exception), error_code=default_error_code, cause=exception
        )

    # 记录日志
    if logger:
        logger.error(f"异常发生: {lottery_exception}", exc_info=True)

    if reraise:
        raise lottery_exception
    else:
        return lottery_exception


def exception_handler(error_code: ErrorCode = ErrorCode.UNKNOWN_ERROR, logger=None):
    """异常处理装饰器

    Args:
        error_code: 默认错误代码
        logger: 日志器
    """

    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                handle_exception(e, logger, reraise=True, default_error_code=error_code)

        return wrapper

    return decorator


def safe_execute(
    func,
    *args,
    default_return=None,
    error_code: ErrorCode = ErrorCode.UNKNOWN_ERROR,
    logger=None,
    **kwargs,
):
    """安全执行函数，捕获异常并返回默认值

    Args:
        func: 要执行的函数
        *args: 函数参数
        default_return: 异常时的默认返回值
        error_code: 错误代码
        logger: 日志器
        **kwargs: 函数关键字参数

    Returns:
        函数执行结果或默认值
    """
    try:
        return func(*args, **kwargs)
    except Exception as e:
        handle_exception(e, logger, reraise=False, default_error_code=error_code)
        return default_return


# 导出的公共接口
__all__ = [
    "ErrorCode",
    "LotteryPredictorException",
    "DataException",
    "ModelException",
    "PredictionException",
    "ConfigurationException",
    "ValidationException",
    "AlgorithmException",
    "ResourceException",
    "handle_exception",
    "exception_handler",
    "safe_execute",
]
