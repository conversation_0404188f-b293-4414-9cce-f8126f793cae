# 大乐透预测系统开发文档（Python）

## 一、项目概述

本项目旨在通过数据分析和概率模型，对大乐透历史开奖数据进行特征归类、马尔科夫建模、贝叶斯推理、杀号处理、最终号码生成，并进行50期回测（仅输出最后10期）来验证模型效果。数据读取自 `dlt_data.csv` 文件，预测逻辑需严格遵守数据隔离，避免未来数据泄漏。

---

## 二、项目结构设计

```
lottery_predictor/
├── main.py                 # 主入口文件，执行预测与回测
├── analyzer.py             # 各种统计分析方法：奇偶比、大小比等
├── markov_model.py         # 马尔科夫链状态转移建模与预测
├── bayes_selector.py       # 贝叶斯公式选择最可能状态
├── killer.py               # 杀号模块，实现候选号码排除
├── generator.py            # 最终号码生成模块
├── utils.py                # 工具函数：格式转换、比值计算等
├── dlt_data.csv            # 开奖历史数据（新到旧排序）
```

---

## 三、规则与算法说明

### 1. 数据读取与格式

- 文件名：`dlt_data.csv`
- 格式示例：
  ```
  期号,红球1,红球2,红球3,红球4,红球5,蓝球1,蓝球2
  24050,01,03,07,22,34,04,11
  ```

---

### 2. 回测准则

- **目标**：预测第 i+1 期，仅使用 ≤ i 期数据。
- **输出**：仅展示最后 10 期回测结果。
- **限制**：不得泄露未来数据；马尔科夫链与贝叶斯预测不能使用随机机制。

---

### 3. 特征归类（比值）

#### 红球特征：

- **奇偶比分类**：
  - 状态：0:5, 1:4, 2:3, 3:2, 4:1, 5:0 共 6 种

- **大小比分类**：
  - 小号范围：01–18，大号范围：19–35
  - 状态与奇偶比相同，共 6 种

#### 蓝球特征：

- **奇偶比与大小比**（等价）：
  - 小号范围：01–06，大号范围：07–12
  - 状态：0:2, 1:1, 2:0 共 3 种

---

### 4. 状态预测方法

#### 马尔科夫链

- 对比值状态（如红球奇偶比）建立转移概率矩阵
- 输出预测状态的最大转移概率，确保稳定性

#### 贝叶斯选择

- 输入多个状态概率，结合历史先验概率
- 选择最大后验概率状态，禁用随机选择，确保稳定性

---

### 5. 杀号策略

- 每一位红球预测两个最不可能出现的号码
- 蓝球每位也预测两个最不可能的号码
- **回测胜率需 ≥ 90%**
- 杀号影响最终号码生成逻辑

---

### 6. 号码生成

- 利用统计分析与概率模型随机生成符合状态的号码组合
- 过滤掉所有落入杀号集的号码
- 生成的号码组合需满足：
  - **50期回测中命中 2+1 的概率 ≥ 60%**
  - 满足所有比值状态预测

---

## 四、控制台日志输出格式

> 注意：禁止写入文件，仅控制台输出，格式如下：

```
基于第25157期预测第25158期:

红球
奇偶比: 预测[3:2(0.709)] -> 实际[2:3] (未中)
大小比: 预测[2:3(0.695)] -> 实际[3:2] (未中)

蓝球
大小比: 预测[1:1(0.708)] -> 实际[0:2] (未中)

杀号：1：（05,19），2：（07,23），3：（08,22），4：（14,31），5：（11,20），6：（04,33），7：（01,09）——成功率：92%

预测号码：05,06,08,18,32——01,02

实际开奖号码：05,06,08,18,32——01,02
```

> 最新预测（待开奖）格式如下：

```
预测下一期:

红球
奇偶比: 预测[3:2(0.709)] -> 实际[待开奖] (待验证)
大小比: 预测[2:3(0.695)] -> 实际[待开奖] (待验证)

蓝球
大小比: 预测[1:1(0.759)] -> 实际[待开奖] (待验证)

杀号：1：（03,25），2：（06,26），3：（08,22），4：（17,30），5：（11,24），6：（04,33），7：（02,12）——成功率：91%

预测号码：06,09,10,19,28——01,10

实际开奖号码：待开奖
```

---

## 五、依赖说明

本项目依赖以下第三方库：

```bash
pip install numpy pandas scikit-learn
```

---

## 六、运行说明

- 执行主脚本：

```bash
python main.py
```

- 控制台将打印出最近10期回测情况和预测结果。
- 确保 `dlt_data.csv` 位于同级目录，并保证数据从新到旧排序。

---

## 七、模型验证标准

| 模块       | 验证标准              |
|------------|-----------------------|
| 比值预测   | 三项命中率 ≥ 80%      |
| 杀号逻辑   | 回测成功率 ≥ 90%      |
| 号码生成器 | 50期内命中2+1 ≥ 60%   |

---

## 八、注意事项

- 所有预测不能依赖随机性生成，必须保证确定性输出。
- 严格遵守数据隔离规则，杜绝未来数据参与模型训练。
- 回测结果应实时打印，格式规范，确保可读性。
