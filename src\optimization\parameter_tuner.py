#!/usr/bin/env python3
"""
算法参数优化模块
基于历史数据自动调整算法参数以提高预测准确率
"""

import json
import os
import time
from typing import Dict, List, Tuple, Any
import numpy as np
from itertools import product
import pandas as pd

class ParameterTuner:
    """参数优化器"""
    
    def __init__(self, config_file: str = "config/default_config.json"):
        """
        初始化参数优化器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.config = self._load_config()
        self.optimization_history = []
        
    def _load_config(self) -> Dict:
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"[失败] 加载配置文件失败: {e}")
            return {}
    
    def _save_config(self, config: Dict):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            print(f"[成功] 配置已保存到 {self.config_file}")
        except Exception as e:
            print(f"[失败] 保存配置文件失败: {e}")
    
    def define_parameter_space(self) -> Dict[str, List]:
        """定义参数搜索空间"""
        return {
            # 增强贝叶斯参数
            "enhanced_bayesian": {
                "window_size": [50, 100, 150, 200],
                "decay_factor": [0.90, 0.95, 0.98, 0.99],
                "min_weight": [0.001, 0.01, 0.05, 0.1],
                "learning_rate": [0.05, 0.1, 0.15, 0.2]
            },
            
            # 优化马尔科夫参数
            "optimized_markov": {
                "order": [1, 2, 3],
                "smoothing_factor": [0.05, 0.1, 0.15, 0.2],
                "min_frequency": [1, 2, 3, 5]
            },
            
            # 增强杀号系统参数
            "enhanced_kill_system": {
                "diversity_threshold": [0.5, 0.6, 0.7, 0.8],
                "max_iterations": [50, 100, 150, 200],
                "weight_decay": [0.90, 0.95, 0.98, 0.99]
            },
            
            # 智能集成参数
            "intelligent_ensemble": {
                "performance_window": [10, 20, 30, 50],
                "adaptation_rate": [0.05, 0.1, 0.15, 0.2],
                "min_confidence": [0.2, 0.3, 0.4, 0.5]
            },
            
            # 预测配置参数
            "prediction_config": {
                "red_kill_count": [10, 13, 15, 18],
                "blue_kill_count": [3, 5, 7, 9],
                "confidence_threshold": [0.5, 0.6, 0.7, 0.8]
            }
        }
    
    def evaluate_parameters(self, params: Dict, test_periods: int = 20) -> Dict:
        """
        评估参数组合的性能
        
        Args:
            params: 参数组合
            test_periods: 测试期数
            
        Returns:
            评估结果字典
        """
        try:
            # 临时更新配置
            temp_config = self.config.copy()
            for category, category_params in params.items():
                if category in temp_config.get("algorithm_parameters", {}):
                    temp_config["algorithm_parameters"][category].update(category_params)
                elif category in temp_config:
                    temp_config[category].update(category_params)
            
            # 这里应该调用实际的预测系统进行回测
            # 为了演示，我们使用模拟结果
            
            # 模拟评估结果
            ratio_accuracy = np.random.uniform(0.4, 0.8)  # 比例预测准确率
            hit_2_plus_1 = np.random.uniform(0.1, 0.4)    # 2+1命中率
            kill_success = np.random.uniform(0.6, 0.9)    # 杀号成功率
            
            # 综合评分 (可以根据实际需求调整权重)
            score = (ratio_accuracy * 0.3 + 
                    hit_2_plus_1 * 0.5 + 
                    kill_success * 0.2)
            
            return {
                "score": score,
                "ratio_accuracy": ratio_accuracy,
                "hit_2_plus_1": hit_2_plus_1,
                "kill_success": kill_success,
                "test_periods": test_periods
            }
            
        except Exception as e:
            print(f"[失败] 参数评估失败: {e}")
            return {"score": 0, "error": str(e)}
    
    def grid_search(self, 
                   categories: List[str] = None,
                   max_combinations: int = 100) -> Dict:
        """
        网格搜索最优参数
        
        Args:
            categories: 要优化的参数类别
            max_combinations: 最大搜索组合数
            
        Returns:
            最优参数组合
        """
        print("🔍 开始网格搜索参数优化...")
        
        param_space = self.define_parameter_space()
        
        if categories:
            param_space = {k: v for k, v in param_space.items() if k in categories}
        
        # 生成参数组合
        combinations = []
        for category, params in param_space.items():
            category_combinations = []
            param_names = list(params.keys())
            param_values = list(params.values())
            
            for combination in product(*param_values):
                param_dict = dict(zip(param_names, combination))
                category_combinations.append({category: param_dict})
            
            combinations.extend(category_combinations)
        
        # 限制搜索组合数
        if len(combinations) > max_combinations:
            combinations = np.random.choice(combinations, max_combinations, replace=False)
            print(f"[警告] 组合数过多，随机选择 {max_combinations} 个组合进行测试")
        
        print(f"[数据] 总共测试 {len(combinations)} 个参数组合")
        
        best_score = 0
        best_params = None
        best_result = None
        
        for i, params in enumerate(combinations, 1):
            print(f"[测试] 测试组合 {i}/{len(combinations)}: {params}")
            
            result = self.evaluate_parameters(params)
            score = result.get("score", 0)
            
            if score > best_score:
                best_score = score
                best_params = params
                best_result = result
                print(f"✨ 发现更好的参数组合，得分: {score:.4f}")
            
            # 记录优化历史
            self.optimization_history.append({
                "params": params,
                "result": result,
                "timestamp": time.time()
            })
        
        print(f"[完成] 网格搜索完成！最佳得分: {best_score:.4f}")
        
        return {
            "best_params": best_params,
            "best_score": best_score,
            "best_result": best_result,
            "total_tested": len(combinations)
        }
    
    def random_search(self, 
                     categories: List[str] = None,
                     n_iterations: int = 50) -> Dict:
        """
        随机搜索最优参数
        
        Args:
            categories: 要优化的参数类别
            n_iterations: 搜索迭代次数
            
        Returns:
            最优参数组合
        """
        print("[随机] 开始随机搜索参数优化...")
        
        param_space = self.define_parameter_space()
        
        if categories:
            param_space = {k: v for k, v in param_space.items() if k in categories}
        
        best_score = 0
        best_params = None
        best_result = None
        
        for i in range(n_iterations):
            # 随机选择参数
            params = {}
            for category, category_params in param_space.items():
                category_selection = {}
                for param_name, param_values in category_params.items():
                    category_selection[param_name] = np.random.choice(param_values)
                params[category] = category_selection
            
            print(f"[测试] 迭代 {i+1}/{n_iterations}: {params}")
            
            result = self.evaluate_parameters(params)
            score = result.get("score", 0)
            
            if score > best_score:
                best_score = score
                best_params = params
                best_result = result
                print(f"✨ 发现更好的参数组合，得分: {score:.4f}")
            
            # 记录优化历史
            self.optimization_history.append({
                "params": params,
                "result": result,
                "timestamp": time.time()
            })
        
        print(f"[完成] 随机搜索完成！最佳得分: {best_score:.4f}")
        
        return {
            "best_params": best_params,
            "best_score": best_score,
            "best_result": best_result,
            "total_tested": n_iterations
        }
    
    def apply_best_parameters(self, optimization_result: Dict):
        """应用最优参数到配置文件"""
        best_params = optimization_result.get("best_params")
        if not best_params:
            print("[失败] 没有找到最优参数")
            return
        
        print("[工具] 应用最优参数到配置文件...")
        
        # 更新配置
        updated_config = self.config.copy()
        
        for category, category_params in best_params.items():
            if category in updated_config.get("algorithm_parameters", {}):
                updated_config["algorithm_parameters"][category].update(category_params)
            elif category in updated_config:
                updated_config[category].update(category_params)
        
        # 保存配置
        self._save_config(updated_config)
        
        # 更新内部配置
        self.config = updated_config
        
        print("[成功] 最优参数已应用")
        print(f"[数据] 优化结果: {optimization_result['best_result']}")
    
    def save_optimization_history(self, filename: str = "data/results/optimization_history.json"):
        """保存优化历史"""
        try:
            os.makedirs(os.path.dirname(filename), exist_ok=True)
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.optimization_history, f, ensure_ascii=False, indent=2)
            print(f"[成功] 优化历史已保存到 {filename}")
        except Exception as e:
            print(f"[失败] 保存优化历史失败: {e}")
    
    def print_optimization_summary(self):
        """打印优化总结"""
        if not self.optimization_history:
            print("[失败] 暂无优化历史")
            return
        
        print("[数据] 参数优化总结")
        print("=" * 50)
        print(f"总测试次数: {len(self.optimization_history)}")
        
        scores = [h["result"].get("score", 0) for h in self.optimization_history]
        print(f"最高得分: {max(scores):.4f}")
        print(f"平均得分: {np.mean(scores):.4f}")
        print(f"得分标准差: {np.std(scores):.4f}")
        
        # 找出最佳结果
        best_idx = np.argmax(scores)
        best_history = self.optimization_history[best_idx]
        
        print("\n[优秀] 最佳参数组合:")
        for category, params in best_history["params"].items():
            print(f"  {category}:")
            for param, value in params.items():
                print(f"    {param}: {value}")
        
        print(f"\n[提升] 最佳结果:")
        result = best_history["result"]
        print(f"  综合得分: {result.get('score', 0):.4f}")
        print(f"  比例准确率: {result.get('ratio_accuracy', 0):.3f}")
        print(f"  2+1命中率: {result.get('hit_2_plus_1', 0):.3f}")
        print(f"  杀号成功率: {result.get('kill_success', 0):.3f}")
