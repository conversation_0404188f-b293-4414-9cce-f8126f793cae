"""
回测结果显示器
提供统一的结果显示格式
"""

from typing import List
from .data_models import BacktestResult, PeriodResult, Statistics


class ResultDisplayer:
    """回测结果显示器"""
    
    def __init__(self):
        self.show_detailed = True
        self.show_statistics = True
    
    def display_backtest_result(self, result: BacktestResult):
        """显示完整的回测结果"""
        self._display_header(result)
        
        if result.config.enable_detailed_output:
            self._display_period_results(result)
        
        if result.config.enable_statistics:
            self._display_statistics(result.statistics)
        
        self._display_summary(result)
    
    def _display_header(self, result: BacktestResult):
        """显示回测头部信息"""
        print(f"[精准] {result.predictor_name} 回测结果")
        print("=" * 60)
        print(f"[数据] 回测配置：{result.config.num_periods}期，显示{result.config.display_periods}期")
        print(f"[提升] 数据范围：{result.backtest_data_range}")
        print(f"⏱️  耗时：{result.total_duration:.2f}秒")
        print()
    
    def _display_period_results(self, result: BacktestResult):
        """显示各期回测结果"""
        display_results = result.get_display_results()
        
        for period_result in display_results:
            if period_result.success:
                self._display_single_period(period_result)
            else:
                self._display_failed_period(period_result)
            print()
    
    def _display_single_period(self, period_result: PeriodResult):
        """显示单期结果"""
        pred = period_result.prediction
        eval_result = period_result.evaluation
        
        print(f"期号 {pred.period_number} (索引 {pred.data_index}):")
        
        # 显示比例预测
        self._display_ratio_predictions(pred, eval_result)
        
        # 显示杀号信息
        self._display_kill_info(pred, eval_result)
        
        # 显示号码预测
        self._display_number_predictions(pred, eval_result)
        
        # 显示实际开奖
        self._display_actual_results(eval_result)
    
    def _display_ratio_predictions(self, pred, eval_result):
        """显示比例预测"""
        print("红球")
        
        # 红球奇偶比
        if pred.red_odd_even_predictions:
            pred_value, prob = pred.red_odd_even_predictions[0]
            actual_value = eval_result.actual_red_odd_even
            hit_status = "[成功]" if eval_result.hits.get('red_odd_even_hit', False) else "[失败]"
            print(f"奇偶比: 预测[{pred_value}({prob:.3f})] -> 实际[{actual_value}] {hit_status}")
        
        # 红球大小比
        if pred.red_size_predictions:
            pred_value, prob = pred.red_size_predictions[0]
            actual_value = eval_result.actual_red_size
            hit_status = "[成功]" if eval_result.hits.get('red_size_hit', False) else "[失败]"
            print(f"大小比: 预测[{pred_value}({prob:.3f})] -> 实际[{actual_value}] {hit_status}")
        
        print()
        print("蓝球")
        
        # 蓝球大小比
        if pred.blue_size_predictions:
            pred_value, prob = pred.blue_size_predictions[0]
            actual_value = eval_result.actual_blue_size
            hit_status = "[成功]" if eval_result.hits.get('blue_size_hit', False) else "[失败]"
            print(f"大小比: 预测[{pred_value}({prob:.3f})] -> 实际[{actual_value}] {hit_status}")
        
        print()
    
    def _display_kill_info(self, pred, eval_result):
        """显示杀号信息"""
        if not pred.kill_numbers:
            return
        
        # 红球杀号
        if 'red_universal' in pred.kill_numbers:
            red_kills = pred.kill_numbers['red_universal']
            if red_kills:
                kill_str = f"({','.join([f'{k:02d}' for k in red_kills])})"
                status = "[成功]" if eval_result.kill_success.get('red_kill_success', False) else "[失败]"
                print(f"红球杀号：{kill_str} {status}")
        
        # 蓝球杀号
        if 'blue_universal' in pred.kill_numbers:
            blue_kills = pred.kill_numbers['blue_universal']
            if blue_kills:
                kill_str = f"({','.join([f'{k:02d}' for k in blue_kills])})"
                status = "[成功]" if eval_result.kill_success.get('blue_kill_success', False) else "[失败]"
                print(f"蓝球杀号：{kill_str} {status}")
        
        print()
    
    def _display_number_predictions(self, pred, eval_result):
        """显示号码预测"""
        if pred.generated_numbers and any(pred.generated_numbers):
            red_balls, blue_balls = pred.generated_numbers
            if red_balls and blue_balls:
                red_str = ','.join([f'{n:02d}' for n in sorted(red_balls)])
                blue_str = ','.join([f'{n:02d}' for n in sorted(blue_balls)])
                hit_info = f"（{eval_result.red_hits}+{eval_result.blue_hits}）"
                print(f"预测号码：{red_str}——{blue_str} {hit_info}")
        
        # 显示贝叶斯推荐（如果有）
        if pred.bayes_selected:
            print(f"\n贝叶斯推荐组合 (前{len(pred.bayes_selected)}组):")
            for combo in pred.bayes_selected:
                red = combo['red_balls']
                blue = combo['blue_balls']
                rank = combo['rank']
                confidence = combo['confidence']
                red_str = ','.join([f'{n:02d}' for n in sorted(red)])
                blue_str = ','.join([f'{n:02d}' for n in sorted(blue)])

                # 计算命中信息
                if eval_result.actual_red and eval_result.actual_blue:
                    red_hits = len(set(red) & set(eval_result.actual_red))
                    blue_hits = len(set(blue) & set(eval_result.actual_blue))
                    hit_info = f"（{red_hits}+{blue_hits}）"
                else:
                    hit_info = "（待验证）"

                print(f"  第{rank}名：{red_str}——{blue_str} {hit_info}")
    
    def _display_actual_results(self, eval_result):
        """显示实际开奖结果"""
        if eval_result.actual_red and eval_result.actual_blue:
            red_str = ','.join([f'{n:02d}' for n in sorted(eval_result.actual_red)])
            blue_str = ','.join([f'{n:02d}' for n in sorted(eval_result.actual_blue)])
            print(f"实际开奖号码：{red_str}——{blue_str}")
    
    def _display_failed_period(self, period_result: PeriodResult):
        """显示失败的期次"""
        print(f"[失败] 期号 {period_result.prediction.period_number} 处理失败: {period_result.error_message}")
    
    def _display_statistics(self, stats: Statistics):
        """显示统计信息"""
        print("=" * 60)
        print("[数据] 统计信息")
        print("=" * 60)
        print(f"总回测期数: {stats.total_periods}")
        print(f"成功处理期数: {stats.successful_periods}")
        print()
        
        print("[提升] 各项指标表现:")
        
        # 比例预测命中率
        for metric, rate in stats.hit_rates.items():
            count = stats.hit_counts.get(metric, 0)
            if 'red_odd_even' in metric:
                print(f"  红球奇偶比命中率: {rate:.1%} ({count}/{stats.total_periods})")
            elif 'red_size' in metric:
                print(f"  红球大小比命中率: {rate:.1%} ({count}/{stats.total_periods})")
            elif 'blue_size' in metric:
                print(f"  蓝球大小比命中率: {rate:.1%} ({count}/{stats.total_periods}) {'[精准]' if rate >= 0.6 else ''}")
        
        # 2+1命中率
        print(f"  2+1命中率: {stats.hit_2_plus_1_rate:.1%} ({stats.hit_2_plus_1_count}/{stats.total_periods}) {'[精准]' if stats.hit_2_plus_1_rate >= 0.15 else ''}")
        
        # 杀号成功率
        for metric, rate in stats.kill_success_rates.items():
            count = stats.kill_success_counts.get(metric, 0)
            if 'red_kill' in metric:
                print(f"  红球杀号成功率: {rate:.1%} ({count}/{stats.total_periods}) {'[精准]' if rate >= 0.8 else ''}")
            elif 'blue_kill' in metric:
                print(f"  蓝球杀号成功率: {rate:.1%} ({count}/{stats.total_periods}) {'[精准]' if rate >= 0.8 else ''}")
        
        # 号码命中统计
        if stats.avg_total_hits > 0:
            print()
            print("[精准] 号码命中统计:")
            print(f"  平均红球命中: {stats.avg_red_hits:.1f}个")
            print(f"  平均蓝球命中: {stats.avg_blue_hits:.1f}个")
            print(f"  平均总命中: {stats.avg_total_hits:.1f}个")
    
    def _display_summary(self, result: BacktestResult):
        """显示总结"""
        print()
        print("=" * 60)
        print("[完成] 回测总结")
        print("=" * 60)
        
        success_rate = result.get_success_rate()
        print(f"[成功] 总体成功率: {success_rate:.1%}")
        
        stats = result.statistics
        
        # 关键指标评估
        print("\n🔍 关键指标评估:")
        
        # 比例预测综合评估
        ratio_rates = [rate for metric, rate in stats.hit_rates.items() 
                      if metric in ['red_odd_even_hit', 'red_size_hit', 'blue_size_hit']]
        if ratio_rates:
            avg_ratio_rate = sum(ratio_rates) / len(ratio_rates)
            status = "[成功]" if avg_ratio_rate >= 0.5 else "[警告]"
            print(f"  比例预测综合命中率: {avg_ratio_rate:.1%} {status}")
        
        # 2+1命中率评估
        status = "[成功]" if stats.hit_2_plus_1_rate >= 0.15 else "[失败]"
        print(f"  2+1命中率: {stats.hit_2_plus_1_rate:.1%} {status}")
        
        # 杀号成功率评估
        kill_rates = [rate for rate in stats.kill_success_rates.values()]
        if kill_rates:
            avg_kill_rate = sum(kill_rates) / len(kill_rates)
            status = "[成功]" if avg_kill_rate >= 0.8 else "[失败]"
            print(f"  杀号综合成功率: {avg_kill_rate:.1%} {status}")
        
        print(f"\n⏱️  回测耗时: {result.total_duration:.2f}秒")
        print(f"[数据] 预测器: {result.predictor_name}")
        if result.end_time:
            print(f"📅 完成时间: {result.end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        else:
            print(f"📅 完成时间: 未记录")


def format_numbers(numbers: List[int]) -> str:
    """格式化号码显示"""
    return ','.join([f'{n:02d}' for n in sorted(numbers)])
