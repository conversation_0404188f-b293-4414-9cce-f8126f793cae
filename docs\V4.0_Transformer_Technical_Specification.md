# V4.0 Transformer 技术规格文档

## 1. 项目概述

### 1.1 背景
基于V3.2深度学习增强器的成功经验，V4.0 Transformer架构旨在通过先进的Transformer技术进一步提升彩票预测系统的准确性和多样性。

### 1.2 目标
- **预测准确率提升**: 相比V3.2提升15-25%
- **预测多样性增强**: 避免预测模式固化
- **系统自适应能力**: 动态调整预测策略
- **计算效率优化**: 并行计算能力提升

## 2. 当前训练数据逻辑分析

### 2.1 数据加载策略
```python
# 当前系统的数据加载逻辑
def load_data(file_path: str = 'data/raw/dlt_data.csv') -> pd.DataFrame:
    df = pd.read_csv(file_path)
    # 确保数据按期号从旧到新排序
    df = df.sort_values('期号', ascending=True).reset_index(drop=True)
    return df
```

**关键特点:**
- 数据按期号从旧到新排序（时间正序）
- 严格遵循数据隔离原则，避免未来数据泄漏
- 支持多种数据格式（红球1-5，蓝球1-2）

### 2.2 训练数据准备流程
```python
# V3.2的训练数据准备逻辑
def _prepare_training_data(self, data: pd.DataFrame) -> Tuple[np.ndarray, Dict[str, np.ndarray]]:
    sequences = []
    targets = {task.value: [] for task in TaskType}
    seq_len = self.config.sequence_length  # 25期历史数据
    
    for i in range(seq_len, len(data)):
        # 输入序列：使用前25期数据
        seq_features = features.iloc[i-seq_len:i].values
        sequences.append(seq_features)
        
        # 目标值：预测第i期的结果
        current_row = data.iloc[i]
        # ... 提取各种目标值
```

**训练逻辑特点:**
- **序列长度**: 25期历史数据作为输入
- **滑动窗口**: 逐期滑动训练，确保时间连续性
- **多任务目标**: 同时预测奇偶比、大小比、号码生成等
- **特征工程**: 提取丰富的统计和模式特征

### 2.3 数据隔离原则实现
```python
def _prepare_data_for_period(self, target_period: str) -> Tuple:
    # 找到目标期号的位置
    target_index = None
    for i, row in full_data.iterrows():
        if str(row["期号"]) == str(target_period):
            target_index = i
            break
    
    # 训练数据：目标期号之前的所有数据
    train_data = full_data.iloc[:target_index].copy()
```

**隔离策略:**
- 严格按期号分割训练和预测数据
- 训练数据仅包含目标期号之前的历史数据
- 避免任何形式的未来信息泄漏

## 3. V4.0 Transformer 架构设计

### 3.1 整体架构
```
输入层 → 嵌入层 → 位置编码 → Multi-Head Attention × 6 → 多任务输出头
```

### 3.2 核心组件

#### 3.2.1 输入处理层
```python
class V4TransformerInputProcessor:
    def __init__(self, config):
        self.sequence_length = 25  # 与V3.2保持一致
        self.feature_dim = 128     # 嵌入维度
        self.embedding_layer = Dense(self.feature_dim)
        self.positional_encoding = PositionalEncoding(max_len=25, d_model=128)
```

**特性:**
- 序列长度: 25期历史数据
- 特征维度: 128维嵌入
- 位置编码: 支持时间序列位置信息

#### 3.2.2 增强Transformer块
```python
class EnhancedTransformerBlock:
    def __init__(self, d_model=128, num_heads=12, dff=512, dropout_rate=0.1):
        self.multi_head_attention = MultiHeadAttention(num_heads, d_model)
        self.cross_task_attention = CrossTaskAttention(num_heads, d_model)
        self.feed_forward = FeedForward(d_model, dff)
        self.layer_norm1 = LayerNormalization()
        self.layer_norm2 = LayerNormalization()
        self.dropout = Dropout(dropout_rate)
```

**创新点:**
- **12头注意力机制**: 比V3.2的8头更强的表示能力
- **跨任务注意力**: 不同预测任务间的信息交互
- **残差连接**: 防止梯度消失，提升训练稳定性

#### 3.2.3 多任务输出头
```python
class MultiTaskOutputHeads:
    def __init__(self, d_model=128):
        # 比例预测头
        self.red_odd_even_head = Dense(6, activation='softmax')  # 0:5 到 5:0
        self.red_size_head = Dense(6, activation='softmax')      # 0:5 到 5:0
        self.blue_size_head = Dense(2, activation='softmax')     # 大小球
        
        # 号码生成头
        self.red_number_head = Dense(35, activation='sigmoid')   # 35个红球概率
        self.blue_number_head = Dense(12, activation='sigmoid')  # 12个蓝球概率
        
        # 不确定性估计头
        self.uncertainty_head = Dense(1, activation='sigmoid')
```

### 3.3 训练策略

#### 3.3.1 多任务损失函数
```python
def compute_multi_task_loss(predictions, targets, weights):
    losses = {}
    
    # 比例预测损失（分类）
    losses['red_odd_even'] = categorical_crossentropy(targets['red_odd_even'], 
                                                     predictions['red_odd_even'])
    losses['red_size'] = categorical_crossentropy(targets['red_size'], 
                                                 predictions['red_size'])
    losses['blue_size'] = categorical_crossentropy(targets['blue_size'], 
                                                  predictions['blue_size'])
    
    # 号码生成损失（回归）
    losses['red_numbers'] = binary_crossentropy(targets['red_numbers'], 
                                               predictions['red_numbers'])
    losses['blue_numbers'] = binary_crossentropy(targets['blue_numbers'], 
                                                predictions['blue_numbers'])
    
    # 不确定性损失
    losses['uncertainty'] = mse(targets['uncertainty'], predictions['uncertainty'])
    
    # 加权总损失
    total_loss = sum(weights[task] * loss for task, loss in losses.items())
    return total_loss, losses
```

#### 3.3.2 损失权重配置
```python
V4_LOSS_WEIGHTS = {
    'red_odd_even': 1.8,      # 提升奇偶比预测权重
    'red_size': 1.8,          # 提升大小比预测权重
    'blue_size': 1.0,         # 蓝球大小比权重
    'red_numbers': 0.3,       # 红球号码生成权重
    'blue_numbers': 0.15,     # 蓝球号码生成权重
    'uncertainty': 0.25       # 不确定性估计权重
}
```

### 3.4 增强采样策略

#### 3.4.1 自适应温度采样
```python
class AdaptiveTemperatureSampling:
    def __init__(self):
        self.base_temperature = 1.0
        self.temperature_range = (0.3, 2.5)
        
    def calculate_adaptive_temperature(self, uncertainty, diversity_score):
        # 基于不确定性和多样性动态调整温度
        uncertainty_factor = uncertainty * 0.7
        diversity_factor = (1.0 - diversity_score) * 0.3
        
        adaptive_temp = self.base_temperature + uncertainty_factor + diversity_factor
        return np.clip(adaptive_temp, *self.temperature_range)
```

#### 3.4.2 Monte Carlo集成
```python
class MonteCarloEnsemble:
    def __init__(self, num_samples=20):  # 比V3.2的15次更多
        self.num_samples = num_samples
        
    def predict_with_uncertainty(self, model, inputs):
        predictions = []
        for _ in range(self.num_samples):
            # 启用Dropout进行随机采样
            pred = model(inputs, training=True)
            predictions.append(pred)
        
        # 计算均值和不确定性
        mean_pred = np.mean(predictions, axis=0)
        uncertainty = np.std(predictions, axis=0)
        
        return mean_pred, uncertainty
```

## 4. 与现有系统集成

### 4.1 接口兼容性
```python
class V4TransformerPredictor(StandardBasePredictor):
    """V4.0 Transformer预测器，兼容现有接口"""
    
    def __init__(self, config: Optional[V4TransformerConfig] = None):
        super().__init__("V4.0 Transformer预测器", PredictionType.DEEP_LEARNING)
        self.config = config or V4TransformerConfig()
        
    def predict(self, data: pd.DataFrame, target_index: int, **kwargs) -> PredictionResult:
        """实现标准预测接口"""
        # 使用与现有系统相同的数据准备逻辑
        # 返回标准的PredictionResult格式
```

### 4.2 配置管理
```python
@dataclass
class V4TransformerConfig:
    """V4.0 Transformer配置"""
    # 架构参数
    d_model: int = 128
    num_heads: int = 12
    num_layers: int = 6
    dff: int = 512
    dropout_rate: float = 0.1
    
    # 训练参数
    sequence_length: int = 25
    batch_size: int = 16
    learning_rate: float = 0.0001
    epochs: int = 50
    
    # 采样参数
    monte_carlo_samples: int = 20
    temperature_range: Tuple[float, float] = (0.3, 2.5)
    
    # 损失权重
    loss_weights: Dict[str, float] = field(default_factory=lambda: V4_LOSS_WEIGHTS)
```

## 5. 性能优化策略

### 5.1 计算效率优化
- **并行注意力计算**: 利用Transformer的并行特性
- **梯度累积**: 支持大批量训练而不增加内存消耗
- **混合精度训练**: 使用FP16加速训练过程

### 5.2 内存优化
- **动态序列长度**: 根据可用数据动态调整序列长度
- **检查点机制**: 定期保存模型状态，支持断点续训
- **模型剪枝**: 移除不重要的连接，减少模型大小

## 6. 评估指标

### 6.1 预测准确性指标
- **比例预测准确率**: 奇偶比、大小比预测的准确性
- **号码命中率**: 生成号码与实际开奖号码的重叠度
- **2+1命中率**: 传统的彩票评估指标

### 6.2 多样性指标
- **预测多样性分数**: 连续预测结果的差异度
- **模式重复率**: 预测模式的重复频率
- **不确定性校准**: 预测不确定性与实际准确性的一致性

## 7. 实施计划

### 7.1 Phase 1: 核心架构实现 (2-3周)
- [ ] 实现V4TransformerPredictor基础类
- [ ] 构建增强Transformer块
- [ ] 实现多任务输出头
- [ ] 集成现有数据处理流程

### 7.2 Phase 2: 增强功能开发 (2-3周)
- [ ] 实现自适应温度采样
- [ ] 开发Monte Carlo集成
- [ ] 添加跨任务注意力机制
- [ ] 实现不确定性估计

### 7.3 Phase 3: 系统集成与测试 (1-2周)
- [ ] 集成到现有预测系统
- [ ] 实现配置管理
- [ ] 完成回测验证
- [ ] 性能对比分析

### 7.4 Phase 4: 优化与部署 (1周)
- [ ] 性能调优
- [ ] 文档完善
- [ ] 部署准备
- [ ] 用户培训

## 8. 风险评估与缓解

### 8.1 技术风险
- **过拟合风险**: 通过Dropout、正则化和早停机制缓解
- **计算资源需求**: 提供CPU备用方案，支持渐进式训练
- **模型复杂度**: 实现模型剪枝和量化技术

### 8.2 集成风险
- **接口兼容性**: 严格遵循现有接口规范
- **数据格式变化**: 实现灵活的数据适配器
- **性能回归**: 保留V3.2作为备用方案

## 9. 成功标准

### 9.1 性能目标
- 比例预测准确率提升 > 15%
- 2+1命中率提升 > 10%
- 预测多样性分数 > 0.8
- 训练时间 < V3.2的150%

### 9.2 质量目标
- 代码覆盖率 > 90%
- 文档完整性 > 95%
- 接口兼容性 = 100%
- 系统稳定性 > 99%

## 10. 总结

V4.0 Transformer架构通过以下创新点实现系统升级：

1. **先进的Transformer架构**: 替代LSTM，提供更强的序列建模能力
2. **多任务学习优化**: 同时优化多个预测目标，提升整体性能
3. **自适应采样策略**: 动态调整预测策略，避免模式固化
4. **不确定性建模**: 提供预测置信度，支持风险评估
5. **系统兼容性**: 无缝集成现有系统，支持平滑升级

通过这些技术创新，V4.0预期将显著提升彩票预测系统的准确性、多样性和可靠性。