"""统一项目配置管理模块

提供项目级别的路径管理、配置访问和环境设置的统一接口。
解决项目中路径管理不一致和配置分散的问题。
"""

import os
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass
import logging

# 导入现有配置模块
try:
    from config import get_settings, setup_logging
except ImportError:
    # 如果无法导入，提供默认实现
    def get_settings():
        return {}

    def setup_logging():
        logging.basicConfig(level=logging.INFO)


@dataclass
class ProjectPaths:
    """项目路径配置"""

    root: Path
    src: Path
    config: Path
    data: Path
    tests: Path
    logs: Path
    models: Path

    @classmethod
    def from_root(cls, root_path: str = None) -> "ProjectPaths":
        """从根目录创建路径配置"""
        if root_path is None:
            # 自动检测项目根目录
            current = Path(__file__).parent
            while current.parent != current:
                if (current / "src").exists() and (current / "config").exists():
                    root_path = current
                    break
                current = current.parent
            else:
                # 如果找不到，使用当前文件的上两级目录
                root_path = Path(__file__).parent.parent.parent

        root = Path(root_path)
        return cls(
            root=root,
            src=root / "src",
            config=root / "config",
            data=root / "data",
            tests=root / "tests",
            logs=root / "logs",
            models=root / "models",
        )

    def ensure_directories(self):
        """确保所有目录存在"""
        for path in [self.data, self.logs, self.models]:
            path.mkdir(parents=True, exist_ok=True)


class ProjectConfig:
    """统一项目配置管理器"""

    _instance: Optional["ProjectConfig"] = None
    _initialized: bool = False

    def __new__(cls) -> "ProjectConfig":
        """单例模式"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not self._initialized:
            self._setup()
            self._initialized = True

    def _setup(self):
        """初始化配置"""
        # 设置路径
        self.paths = ProjectPaths.from_root()
        self.paths.ensure_directories()

        # 设置日志
        setup_logging()
        self.logger = logging.getLogger(__name__)

        # 加载设置
        self._settings = get_settings()

        self.logger.info(f"项目配置初始化完成，根目录: {self.paths.root}")

    @property
    def settings(self) -> Dict[str, Any]:
        """获取项目设置"""
        return self._settings

    def get_path(self, path_type: str) -> Path:
        """获取指定类型的路径"""
        return getattr(self.paths, path_type, self.paths.root)

    def get_relative_import_path(self, from_module: str, to_module: str) -> str:
        """生成相对导入路径

        Args:
            from_module: 源模块路径 (如 'src.models.neural')
            to_module: 目标模块路径 (如 'src.core.interfaces')

        Returns:
            相对导入路径 (如 '..core.interfaces')
        """
        from_parts = from_module.split(".")
        to_parts = to_module.split(".")

        # 找到共同前缀
        common_length = 0
        for i, (f, t) in enumerate(zip(from_parts, to_parts)):
            if f == t:
                common_length = i + 1
            else:
                break

        # 计算需要向上的层数
        up_levels = len(from_parts) - common_length - 1

        # 构建相对路径
        if up_levels > 0:
            relative_path = "." * (up_levels + 1)
        else:
            relative_path = "."

        # 添加目标路径
        if common_length < len(to_parts):
            target_path = ".".join(to_parts[common_length:])
            if relative_path == ".":
                return f".{target_path}"
            else:
                return f"{relative_path}{target_path}"

        return relative_path


# 全局配置实例
project_config = ProjectConfig()


# 便捷访问函数
def get_project_root() -> Path:
    """获取项目根目录"""
    return project_config.paths.root


def get_src_path() -> Path:
    """获取src目录"""
    return project_config.paths.src


def get_config_path() -> Path:
    """获取config目录"""
    return project_config.paths.config


def get_data_path() -> Path:
    """获取data目录"""
    return project_config.paths.data


def get_logs_path() -> Path:
    """获取logs目录"""
    return project_config.paths.logs


def get_models_path() -> Path:
    """获取models目录"""
    return project_config.paths.models


def get_project_settings() -> Dict[str, Any]:
    """获取项目设置"""
    return project_config.settings


def get_logger(name: str = None) -> logging.Logger:
    """获取配置好的日志记录器"""
    return logging.getLogger(name or __name__)


# 导出的公共接口
__all__ = [
    "ProjectConfig",
    "ProjectPaths",
    "project_config",
    "get_project_root",
    "get_src_path",
    "get_config_path",
    "get_data_path",
    "get_logs_path",
    "get_models_path",
    "get_project_settings",
    "get_logger",
]
