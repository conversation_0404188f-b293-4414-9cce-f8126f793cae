"""
V4.0 Transformer参数优化器
Parameter Optimizer for V4.0 Transformer
基于评估结果自动优化模型参数
"""

import sys
import os
from pathlib import Path
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Tuple, Optional
import json
import time
from datetime import datetime
import logging
from itertools import product
import pickle

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '..', '..', '..')
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# TensorFlow导入
try:
    import tensorflow as tf
    from tensorflow import keras
    from sklearn.preprocessing import StandardScaler
    from sklearn.model_selection import train_test_split
    TF_AVAILABLE = True
except ImportError:
    print("[WARN] TensorFlow或sklearn不可用，使用简化模式")
    TF_AVAILABLE = False


class V4ParameterOptimizer:
    """V4.0 Transformer参数优化器"""
    
    def __init__(self):
        self.project_root = Path(project_root)
        self.data_path = self.project_root / "data" / "raw" / "dlt_data.csv"
        self.models_dir = self.project_root / "models" / "v4_trained"
        self.optimization_dir = self.project_root / "models" / "v4_optimization"
        
        # 创建优化目录
        self.optimization_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 优化参数空间
        self.param_space = {
            'lstm_units_1': [64, 128, 144, 256],
            'lstm_units_2': [32, 64, 72, 128],
            'dropout_rate': [0.2, 0.3, 0.4, 0.5],
            'learning_rate': [0.0001, 0.0005, 0.001, 0.002],
            'batch_size': [16, 32, 64],
            'sequence_length': [20, 25, 30],
            'loss_weights': [
                {'red_odd_even': 2.0, 'red_size': 3.0, 'blue_size': 1.0, 'red_numbers': 1.5, 'blue_numbers': 1.0},
                {'red_odd_even': 1.5, 'red_size': 2.5, 'blue_size': 1.0, 'red_numbers': 2.0, 'blue_numbers': 1.0},
                {'red_odd_even': 1.0, 'red_size': 2.0, 'blue_size': 1.0, 'red_numbers': 1.0, 'blue_numbers': 1.0}
            ]
        }
        
        # 当前最佳结果
        self.best_score = 0.0
        self.best_params = None
        self.best_model = None
        self.optimization_history = []
        
    def load_and_prepare_data(self) -> Tuple[np.ndarray, Dict[str, np.ndarray]]:
        """加载并准备训练数据"""
        print("[INFO] 加载和准备优化数据...")
        
        # 读取数据
        df = pd.read_csv(self.data_path, header=None)
        df.columns = ['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '蓝球1', '蓝球2', '日期']
        
        # 数据类型转换
        numeric_cols = ['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '蓝球1', '蓝球2']
        for col in numeric_cols:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # 删除空值
        df = df.dropna().reset_index(drop=True)
        
        # 增强特征工程
        df = self._enhanced_feature_engineering(df)
        
        print(f"[OK] 数据准备完成: {len(df)} 期")
        return df
    
    def _enhanced_feature_engineering(self, df: pd.DataFrame) -> pd.DataFrame:
        """增强特征工程"""
        red_balls = ['红球1', '红球2', '红球3', '红球4', '红球5']
        blue_balls = ['蓝球1', '蓝球2']
        
        # 基础比例特征
        df['红球奇数个数'] = df[red_balls].apply(lambda row: sum(x % 2 == 1 for x in row), axis=1)
        df['红球偶数个数'] = 5 - df['红球奇数个数']
        df['红球大数个数'] = df[red_balls].apply(lambda row: sum(x > 17 for x in row), axis=1)
        df['红球小数个数'] = 5 - df['红球大数个数']
        
        df['蓝球大数个数'] = df[blue_balls].apply(lambda row: sum(x > 7 for x in row), axis=1)
        df['蓝球小数个数'] = 2 - df['蓝球大数个数']
        
        # 统计特征
        df['红球和值'] = df[red_balls].sum(axis=1)
        df['红球均值'] = df[red_balls].mean(axis=1)
        df['红球方差'] = df[red_balls].var(axis=1)
        df['红球跨度'] = df[red_balls].max(axis=1) - df[red_balls].min(axis=1)
        
        df['蓝球和值'] = df[blue_balls].sum(axis=1)
        df['蓝球跨度'] = df[blue_balls].max(axis=1) - df[blue_balls].min(axis=1)
        
        # 区间分布特征
        for i, (start, end) in enumerate([(1, 7), (8, 14), (15, 21), (22, 28), (29, 35)]):
            df[f'红球区间{i+1}个数'] = df[red_balls].apply(
                lambda row: sum(start <= x <= end for x in row), axis=1
            )
        
        # 连号特征
        df['红球连号个数'] = df[red_balls].apply(self._count_consecutive, axis=1)
        df['蓝球连号个数'] = df[blue_balls].apply(self._count_consecutive, axis=1)
        
        # 历史趋势特征（滑动窗口）
        window_size = 5
        for col in ['红球奇数个数', '红球大数个数', '蓝球大数个数']:
            df[f'{col}_趋势'] = df[col].rolling(window=window_size, min_periods=1).mean()
            df[f'{col}_波动'] = df[col].rolling(window=window_size, min_periods=1).std().fillna(0)
        
        # 新增特征：频率分析
        for i in range(1, 36):  # 红球1-35
            df[f'红球{i}_历史频率'] = df[red_balls].apply(lambda row: sum(x == i for x in row), axis=1)
        
        for i in range(1, 13):  # 蓝球1-12
            df[f'蓝球{i}_历史频率'] = df[blue_balls].apply(lambda row: sum(x == i for x in row), axis=1)
        
        return df
    
    def _count_consecutive(self, row) -> int:
        """计算连号个数"""
        sorted_nums = sorted(row)
        consecutive_count = 0
        for i in range(len(sorted_nums) - 1):
            if sorted_nums[i+1] - sorted_nums[i] == 1:
                consecutive_count += 1
        return consecutive_count
    
    def create_optimized_model(self, params: Dict[str, Any], input_shape: Tuple[int, int]) -> keras.Model:
        """创建优化的模型"""
        if not TF_AVAILABLE:
            raise RuntimeError("TensorFlow不可用")
        
        # 输入层
        inputs = keras.Input(shape=input_shape, name='sequence_input')
        
        # LSTM层
        x = keras.layers.LSTM(
            params['lstm_units_1'], 
            return_sequences=True, 
            dropout=params['dropout_rate'],
            name='lstm_1'
        )(inputs)
        
        x = keras.layers.LSTM(
            params['lstm_units_2'], 
            return_sequences=False, 
            dropout=params['dropout_rate'],
            name='lstm_2'
        )(x)
        
        # 共享特征层
        shared_features = keras.layers.Dense(128, activation='relu', name='shared_features')(x)
        shared_features = keras.layers.Dropout(params['dropout_rate'])(shared_features)
        
        # 多任务输出头
        # 红球奇偶比 (0-5个奇数)
        red_odd_even = keras.layers.Dense(64, activation='relu')(shared_features)
        red_odd_even = keras.layers.Dense(6, activation='softmax', name='red_odd_even')(red_odd_even)
        
        # 红球大小比 (0-5个大数)
        red_size = keras.layers.Dense(64, activation='relu')(shared_features)
        red_size = keras.layers.Dense(6, activation='softmax', name='red_size')(red_size)
        
        # 蓝球大小比 (0-2个大数)
        blue_size = keras.layers.Dense(32, activation='relu')(shared_features)
        blue_size = keras.layers.Dense(3, activation='softmax', name='blue_size')(blue_size)
        
        # 红球号码预测 (35个号码)
        red_numbers = keras.layers.Dense(128, activation='relu')(shared_features)
        red_numbers = keras.layers.Dense(35, activation='sigmoid', name='red_numbers')(red_numbers)
        
        # 蓝球号码预测 (12个号码)
        blue_numbers = keras.layers.Dense(64, activation='relu')(shared_features)
        blue_numbers = keras.layers.Dense(12, activation='sigmoid', name='blue_numbers')(blue_numbers)
        
        # 创建模型
        model = keras.Model(
            inputs=inputs,
            outputs={
                'red_odd_even': red_odd_even,
                'red_size': red_size,
                'blue_size': blue_size,
                'red_numbers': red_numbers,
                'blue_numbers': blue_numbers
            },
            name='v4_optimized_transformer'
        )
        
        # 编译模型
        model.compile(
            optimizer=keras.optimizers.Adam(learning_rate=params['learning_rate']),
            loss={
                'red_odd_even': 'sparse_categorical_crossentropy',
                'red_size': 'sparse_categorical_crossentropy',
                'blue_size': 'sparse_categorical_crossentropy',
                'red_numbers': 'binary_crossentropy',
                'blue_numbers': 'binary_crossentropy'
            },
            loss_weights=params['loss_weights'],
            metrics={
                'red_odd_even': 'accuracy',
                'red_size': 'accuracy',
                'blue_size': 'accuracy',
                'red_numbers': 'binary_accuracy',
                'blue_numbers': 'binary_accuracy'
            }
        )
        
        return model
    
    def prepare_training_data(self, df: pd.DataFrame, params: Dict[str, Any]) -> Tuple[np.ndarray, Dict[str, np.ndarray], StandardScaler]:
        """准备训练数据"""
        sequences = []
        targets = {
            'red_odd_even': [],
            'red_size': [],
            'blue_size': [],
            'red_numbers': [],
            'blue_numbers': []
        }
        
        seq_len = params['sequence_length']
        
        # 选择特征列（基础特征，避免过多特征导致过拟合）
        feature_cols = [
            '红球奇数个数', '红球大数个数', '蓝球大数个数',
            '红球和值', '红球均值', '红球方差', '红球跨度',
            '蓝球和值', '蓝球跨度',
            '红球区间1个数', '红球区间2个数', '红球区间3个数', '红球区间4个数', '红球区间5个数',
            '红球连号个数', '蓝球连号个数',
            '红球奇数个数_趋势', '红球大数个数_趋势', '蓝球大数个数_趋势',
            '红球奇数个数_波动', '红球大数个数_波动', '蓝球大数个数_波动'
        ]
        
        # 确保所有特征列都存在
        available_features = [col for col in feature_cols if col in df.columns]
        
        # 生成序列数据
        for i in range(seq_len, len(df)):
            # 输入序列特征
            sequence_features = []
            for j in range(i - seq_len, i):
                row_features = [df.iloc[j][col] for col in available_features]
                sequence_features.append(row_features)
            
            sequences.append(sequence_features)
            
            # 目标值
            target_row = df.iloc[i]
            
            targets['red_odd_even'].append(target_row['红球奇数个数'])
            targets['red_size'].append(target_row['红球大数个数'])
            targets['blue_size'].append(target_row['蓝球大数个数'])
            
            # 号码目标
            red_balls = [target_row[f'红球{k}'] for k in range(1, 6)]
            blue_balls = [target_row[f'蓝球{k}'] for k in range(1, 3)]
            
            red_target = np.zeros(35)
            blue_target = np.zeros(12)
            
            for ball in red_balls:
                if 1 <= ball <= 35:
                    red_target[int(ball) - 1] = 1
            for ball in blue_balls:
                if 1 <= ball <= 12:
                    blue_target[int(ball) - 1] = 1
            
            targets['red_numbers'].append(red_target)
            targets['blue_numbers'].append(blue_target)
        
        X = np.array(sequences, dtype=np.float32)
        
        # 特征标准化
        scaler = StandardScaler()
        X_reshaped = X.reshape(-1, X.shape[-1])
        X_scaled = scaler.fit_transform(X_reshaped)
        X = X_scaled.reshape(X.shape)
        
        # 转换目标
        y = {}
        for key in targets:
            y[key] = np.array(targets[key])
        
        return X, y, scaler

    def optimize_single_configuration(self, params: Dict[str, Any], X: np.ndarray,
                                    y: Dict[str, np.ndarray], scaler: StandardScaler) -> Dict[str, Any]:
        """优化单个参数配置"""
        print(f"[INFO] 测试参数配置: {params}")

        try:
            # 数据分割 - 修复数据分割问题
            indices = np.arange(len(X))
            train_indices, val_indices = train_test_split(indices, test_size=0.2, random_state=42)

            X_train = X[train_indices]
            X_val = X[val_indices]

            # 分割目标数据
            y_train = {}
            y_val = {}
            for key in y.keys():
                y_train[key] = y[key][train_indices]
                y_val[key] = y[key][val_indices]

            # 创建模型
            model = self.create_optimized_model(params, (X.shape[1], X.shape[2]))

            # 训练回调
            callbacks = [
                keras.callbacks.EarlyStopping(
                    monitor='val_loss', patience=5, restore_best_weights=True
                ),
                keras.callbacks.ReduceLROnPlateau(
                    monitor='val_loss', factor=0.5, patience=3, min_lr=1e-6
                )
            ]

            # 训练模型
            start_time = time.time()
            history = model.fit(
                X_train, y_train,
                validation_data=(X_val, y_val),
                epochs=30,
                batch_size=params['batch_size'],
                callbacks=callbacks,
                verbose=0
            )
            training_time = time.time() - start_time

            # 评估性能
            val_loss = min(history.history['val_loss'])

            # 计算各任务准确率
            val_predictions = model.predict(X_val, verbose=0)

            # 红球奇偶比准确率
            red_odd_pred = np.argmax(val_predictions['red_odd_even'], axis=1)
            red_odd_acc = np.mean(red_odd_pred == y_val['red_odd_even'])

            # 红球大小比准确率
            red_size_pred = np.argmax(val_predictions['red_size'], axis=1)
            red_size_acc = np.mean(red_size_pred == y_val['red_size'])

            # 蓝球大小比准确率
            blue_size_pred = np.argmax(val_predictions['blue_size'], axis=1)
            blue_size_acc = np.mean(blue_size_pred == y_val['blue_size'])

            # 综合评分（重点关注红球大小比）
            composite_score = (red_odd_acc * 0.2 + red_size_acc * 0.5 + blue_size_acc * 0.3)

            result = {
                'params': params,
                'val_loss': val_loss,
                'red_odd_acc': red_odd_acc,
                'red_size_acc': red_size_acc,
                'blue_size_acc': blue_size_acc,
                'composite_score': composite_score,
                'training_time': training_time,
                'model': model,
                'scaler': scaler,
                'success': True
            }

            print(f"[RESULT] 综合评分: {composite_score:.3f} (红球奇偶: {red_odd_acc:.3f}, 红球大小: {red_size_acc:.3f}, 蓝球大小: {blue_size_acc:.3f})")

            return result

        except Exception as e:
            print(f"[ERROR] 参数配置失败: {e}")
            return {
                'params': params,
                'success': False,
                'error': str(e)
            }

    def run_optimization(self, max_trials: int = 20) -> Dict[str, Any]:
        """运行参数优化"""
        print("V4.0 Transformer 参数优化系统")
        print("=" * 60)

        if not TF_AVAILABLE:
            print("[ERROR] TensorFlow不可用，无法进行优化")
            return {"success": False, "error": "TensorFlow不可用"}

        try:
            # 1. 数据准备
            print("\n[STEP 1] 数据加载和准备")
            df = self.load_and_prepare_data()

            # 2. 生成参数组合
            print(f"\n[STEP 2] 生成参数组合 (最多{max_trials}个)")
            param_combinations = []

            # 生成所有可能的参数组合
            keys = list(self.param_space.keys())
            values = list(self.param_space.values())

            all_combinations = list(product(*values))

            # 随机选择部分组合进行测试
            import random
            random.seed(42)
            selected_combinations = random.sample(all_combinations, min(max_trials, len(all_combinations)))

            for combo in selected_combinations:
                param_dict = dict(zip(keys, combo))
                param_combinations.append(param_dict)

            print(f"[INFO] 将测试 {len(param_combinations)} 个参数组合")

            # 3. 准备基础训练数据（使用默认参数）
            print("\n[STEP 3] 准备基础训练数据")
            default_params = {
                'sequence_length': 25,
                'batch_size': 32
            }
            X, y, base_scaler = self.prepare_training_data(df, default_params)

            # 4. 参数优化循环
            print(f"\n[STEP 4] 开始参数优化 ({len(param_combinations)} 个配置)")

            for i, params in enumerate(param_combinations):
                print(f"\n--- 配置 {i+1}/{len(param_combinations)} ---")

                # 如果序列长度不同，需要重新准备数据
                if params['sequence_length'] != default_params['sequence_length']:
                    X_current, y_current, scaler_current = self.prepare_training_data(df, params)
                else:
                    X_current, y_current, scaler_current = X, y, base_scaler

                # 优化单个配置
                result = self.optimize_single_configuration(params, X_current, y_current, scaler_current)

                if result['success']:
                    self.optimization_history.append(result)

                    # 更新最佳结果
                    if result['composite_score'] > self.best_score:
                        self.best_score = result['composite_score']
                        self.best_params = result['params']
                        self.best_model = result['model']
                        print(f"[NEW BEST] 新的最佳配置! 评分: {self.best_score:.3f}")

                        # 保存最佳模型
                        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                        model_path = self.optimization_dir / f"best_v4_optimized_{timestamp}.h5"
                        scaler_path = self.optimization_dir / f"best_scaler_{timestamp}.pkl"

                        result['model'].save(str(model_path))
                        with open(scaler_path, 'wb') as f:
                            pickle.dump(result['scaler'], f)

                        print(f"[SAVED] 最佳模型已保存: {model_path}")

            # 5. 生成优化报告
            print("\n[STEP 5] 生成优化报告")
            optimization_report = self.generate_optimization_report()

            # 保存优化历史
            history_path = self.optimization_dir / f"optimization_history_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(history_path, 'w', encoding='utf-8') as f:
                # 转换为可序列化的格式
                serializable_history = []
                for result in self.optimization_history:
                    serializable_result = {k: v for k, v in result.items() if k not in ['model', 'scaler']}
                    serializable_history.append(serializable_result)
                json.dump(serializable_history, f, indent=2, ensure_ascii=False)

            print(f"[OK] 优化历史已保存: {history_path}")

            final_result = {
                'success': True,
                'best_score': self.best_score,
                'best_params': self.best_params,
                'total_trials': len(self.optimization_history),
                'optimization_report': optimization_report,
                'history_path': str(history_path)
            }

            print(f"\n[SUCCESS] 参数优化完成!")
            print(f"最佳综合评分: {self.best_score:.3f}")
            print(f"总测试配置: {len(self.optimization_history)}")

            return final_result

        except Exception as e:
            print(f"\n[ERROR] 优化流程失败: {e}")
            import traceback
            traceback.print_exc()
            return {"success": False, "error": str(e)}

    def generate_optimization_report(self) -> str:
        """生成优化报告"""
        if not self.optimization_history:
            return "无优化历史数据"

        # 排序结果
        sorted_results = sorted(self.optimization_history, key=lambda x: x['composite_score'], reverse=True)

        report_lines = [
            "# V4.0 Transformer 参数优化报告",
            f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "## 优化总结",
            f"- 测试配置数量: {len(self.optimization_history)}",
            f"- 最佳综合评分: {self.best_score:.3f}",
            f"- 最佳配置参数: {self.best_params}",
            "",
            "## 性能提升分析",
        ]

        if len(sorted_results) > 1:
            best = sorted_results[0]
            worst = sorted_results[-1]
            improvement = (best['composite_score'] - worst['composite_score']) / worst['composite_score'] * 100
            report_lines.append(f"- 最佳vs最差配置提升: {improvement:.1f}%")

        report_lines.extend([
            "",
            "## Top 5 配置结果",
            "| 排名 | 综合评分 | 红球奇偶 | 红球大小 | 蓝球大小 | 训练时间 |",
            "|------|----------|----------|----------|----------|----------|"
        ])

        for i, result in enumerate(sorted_results[:5]):
            report_lines.append(
                f"| {i+1} | {result['composite_score']:.3f} | {result['red_odd_acc']:.3f} | "
                f"{result['red_size_acc']:.3f} | {result['blue_size_acc']:.3f} | {result['training_time']:.1f}s |"
            )

        report_lines.extend([
            "",
            "## 最佳参数配置详情",
            "```json",
            json.dumps(self.best_params, indent=2, ensure_ascii=False),
            "```"
        ])

        return "\n".join(report_lines)


def main():
    """主函数"""
    optimizer = V4ParameterOptimizer()
    results = optimizer.run_optimization(max_trials=15)  # 测试15个配置

    if results.get("success"):
        print("\n" + "="*60)
        print("优化报告预览:")
        print("="*60)
        print(results.get("optimization_report", "")[:1000] + "...")

    return results.get("success", False)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
