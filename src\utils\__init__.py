"""
统一工具函数模块
提供数据处理、杀号算法、验证等工具函数
"""

# 核心工具函数
from .utils import (
    load_data, parse_numbers, calculate_odd_even_ratio,
    calculate_size_ratio_red, calculate_size_ratio_blue,
    ratio_to_state, state_to_ratio, format_numbers,
    check_hit_2_plus_1, calculate_success_rate,
    validate_data_format, get_data_summary, safe_load_data,
    calculate_hit_rate_detailed
)

# 杀号算法
from .kill_algorithms import UnifiedKillAlgorithms

# 日志工具
from .logger import setup_logger

# 验证工具
try:
    from .validation import *
except ImportError:
    pass

# 向后兼容的别名
NumberKiller = UnifiedKillAlgorithms
UniversalKiller = UnifiedKillAlgorithms
BlueKiller = UnifiedKillAlgorithms

__all__ = [
    # 核心数据处理函数
    'load_data', 'parse_numbers', 'calculate_odd_even_ratio',
    'calculate_size_ratio_red', 'calculate_size_ratio_blue',
    'ratio_to_state', 'state_to_ratio', 'format_numbers',
    'check_hit_2_plus_1', 'calculate_success_rate',
    'validate_data_format', 'get_data_summary', 'safe_load_data',
    'calculate_hit_rate_detailed',

    # 杀号算法
    'UnifiedKillAlgorithms', 'NumberKiller', 'UniversalKiller', 'BlueKiller',

    # 工具
    'setup_logger'
]
