#!/usr/bin/env python3
"""
高级特征工程器
专门为红球奇偶比预测提供深度特征分析
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Tuple
from collections import Counter
import logging
from src.utils.utils import parse_numbers, calculate_odd_even_ratio, ratio_to_state


class AdvancedFeatureEngineer:
    """高级特征工程器 - 多维度特征提取"""
    
    def __init__(self):
        """初始化特征工程器"""
        self.logger = logging.getLogger(__name__)
        self.feature_cache = {}
        
    def extract_all_features(self, data: pd.DataFrame, target_period: int = None) -> Dict[str, Any]:
        """
        提取所有特征
        
        Args:
            data: 历史数据
            target_period: 目标期号（用于缓存）
            
        Returns:
            特征字典
        """
        if target_period and target_period in self.feature_cache:
            return self.feature_cache[target_period]
        
        features = {}
        
        # 1. 时间序列特征
        features.update(self._extract_time_series_features(data))
        
        # 2. 统计特征
        features.update(self._extract_statistical_features(data))
        
        # 3. 关联特征
        features.update(self._extract_correlation_features(data))
        
        # 4. 模式特征
        features.update(self._extract_pattern_features(data))
        
        # 5. 高级特征
        features.update(self._extract_advanced_features(data))
        
        if target_period:
            self.feature_cache[target_period] = features
            
        return features
    
    def _extract_time_series_features(self, data: pd.DataFrame) -> Dict[str, Any]:
        """提取时间序列特征"""
        features = {}
        
        # 准备奇偶比状态序列
        states = []
        for _, row in data.iterrows():
            red_balls, _ = parse_numbers(row)
            red_odd, red_even = calculate_odd_even_ratio(red_balls)
            state = ratio_to_state((red_odd, red_even))
            states.append(state)
        
        # 连续性分析
        features['consecutive_same_count'] = self._count_consecutive_same(states)
        features['max_consecutive_length'] = self._max_consecutive_length(states)
        features['recent_consecutive_length'] = self._recent_consecutive_length(states)
        
        # 周期性分析
        features['weekly_pattern'] = self._analyze_weekly_pattern(data, states)
        features['monthly_pattern'] = self._analyze_monthly_pattern(data, states)
        
        # 趋势分析
        features['trend_direction'] = self._analyze_trend(states[-20:] if len(states) >= 20 else states)
        features['trend_strength'] = self._calculate_trend_strength(states[-20:] if len(states) >= 20 else states)
        
        return features
    
    def _extract_statistical_features(self, data: pd.DataFrame) -> Dict[str, Any]:
        """提取统计特征"""
        features = {}
        
        # 准备奇偶比状态序列
        states = []
        for _, row in data.iterrows():
            red_balls, _ = parse_numbers(row)
            red_odd, red_even = calculate_odd_even_ratio(red_balls)
            state = ratio_to_state((red_odd, red_even))
            states.append(state)
        
        # 滑动窗口统计
        for window in [5, 10, 20, 50]:
            if len(states) >= window:
                recent_states = states[-window:]
                features[f'freq_dist_{window}'] = dict(Counter(recent_states))
                features[f'entropy_{window}'] = self._calculate_entropy(recent_states)
                features[f'stability_{window}'] = self._calculate_stability(recent_states)
        
        # 整体统计
        features['total_entropy'] = self._calculate_entropy(states)
        features['state_diversity'] = len(set(states))
        features['most_frequent_state'] = Counter(states).most_common(1)[0][0]
        features['least_frequent_state'] = Counter(states).most_common()[-1][0]
        
        return features
    
    def _extract_correlation_features(self, data: pd.DataFrame) -> Dict[str, Any]:
        """提取关联特征"""
        features = {}
        
        # 准备各种比率数据
        odd_even_states = []
        size_states = []
        sums = []
        spans = []
        
        for _, row in data.iterrows():
            red_balls, blue_balls = parse_numbers(row)
            
            # 奇偶比
            red_odd, red_even = calculate_odd_even_ratio(red_balls)
            odd_even_states.append(ratio_to_state((red_odd, red_even)))
            
            # 大小比
            red_small = sum(1 for x in red_balls if x <= 17)
            red_large = sum(1 for x in red_balls if x >= 18)
            size_states.append(ratio_to_state((red_small, red_large)))
            
            # 和值和跨度
            sums.append(sum(red_balls))
            spans.append(max(red_balls) - min(red_balls))
        
        # 计算关联度
        features['odd_even_size_correlation'] = self._calculate_state_correlation(odd_even_states, size_states)
        features['odd_even_sum_correlation'] = self._calculate_numeric_correlation(odd_even_states, sums)
        features['odd_even_span_correlation'] = self._calculate_numeric_correlation(odd_even_states, spans)
        
        # 条件概率
        features['conditional_probs'] = self._calculate_conditional_probabilities(odd_even_states, size_states)
        
        return features
    
    def _extract_pattern_features(self, data: pd.DataFrame) -> Dict[str, Any]:
        """提取模式特征"""
        features = {}
        
        # 准备奇偶比状态序列
        states = []
        for _, row in data.iterrows():
            red_balls, _ = parse_numbers(row)
            red_odd, red_even = calculate_odd_even_ratio(red_balls)
            state = ratio_to_state((red_odd, red_even))
            states.append(state)
        
        # 反转模式
        features['reversal_patterns'] = self._analyze_reversal_patterns(states)
        
        # 震荡模式
        features['oscillation_patterns'] = self._analyze_oscillation_patterns(states)
        
        # 异常检测
        features['anomaly_score'] = self._calculate_anomaly_score(states)
        
        # 周期性强度
        features['periodicity_strength'] = self._calculate_periodicity_strength(states)
        
        return features
    
    def _extract_advanced_features(self, data: pd.DataFrame) -> Dict[str, Any]:
        """提取高级特征"""
        features = {}
        
        # 准备奇偶比状态序列
        states = []
        for _, row in data.iterrows():
            red_balls, _ = parse_numbers(row)
            red_odd, red_even = calculate_odd_even_ratio(red_balls)
            state = ratio_to_state((red_odd, red_even))
            states.append(state)
        
        # 马尔科夫链特征
        features['markov_1st_order'] = self._build_markov_chain(states, order=1)
        features['markov_2nd_order'] = self._build_markov_chain(states, order=2)
        
        # 信息论特征
        features['mutual_information'] = self._calculate_mutual_information(states)
        features['conditional_entropy'] = self._calculate_conditional_entropy(states)
        
        # 复杂度特征
        features['lempel_ziv_complexity'] = self._calculate_lz_complexity(states)
        features['approximate_entropy'] = self._calculate_approximate_entropy(states)
        
        return features
    
    def _count_consecutive_same(self, states: List[str]) -> Dict[str, int]:
        """统计连续相同状态的次数"""
        consecutive_counts = {}
        current_state = None
        current_count = 0
        
        for state in states:
            if state == current_state:
                current_count += 1
            else:
                if current_state and current_count > 1:
                    key = f"{current_state}_{current_count}"
                    consecutive_counts[key] = consecutive_counts.get(key, 0) + 1
                current_state = state
                current_count = 1
        
        return consecutive_counts
    
    def _max_consecutive_length(self, states: List[str]) -> int:
        """计算最大连续长度"""
        if not states:
            return 0
        
        max_length = 1
        current_length = 1
        
        for i in range(1, len(states)):
            if states[i] == states[i-1]:
                current_length += 1
                max_length = max(max_length, current_length)
            else:
                current_length = 1
        
        return max_length
    
    def _recent_consecutive_length(self, states: List[str]) -> int:
        """计算最近的连续长度"""
        if not states:
            return 0
        
        length = 1
        for i in range(len(states) - 1, 0, -1):
            if states[i] == states[i-1]:
                length += 1
            else:
                break
        
        return length
    
    def _analyze_weekly_pattern(self, data: pd.DataFrame, states: List[str]) -> Dict[str, float]:
        """分析周模式"""
        if len(data) < 7:
            return {}
        
        # 简化的周模式分析（假设每期间隔固定）
        weekly_patterns = {}
        for i, state in enumerate(states):
            week_pos = i % 7
            if week_pos not in weekly_patterns:
                weekly_patterns[week_pos] = []
            weekly_patterns[week_pos].append(state)
        
        # 计算每个位置的状态分布
        result = {}
        for pos, pos_states in weekly_patterns.items():
            state_dist = Counter(pos_states)
            total = len(pos_states)
            result[f"week_pos_{pos}"] = {state: count/total for state, count in state_dist.items()}
        
        return result
    
    def _analyze_monthly_pattern(self, data: pd.DataFrame, states: List[str]) -> Dict[str, float]:
        """分析月模式"""
        if len(data) < 30:
            return {}
        
        # 简化的月模式分析
        monthly_patterns = {}
        for i, state in enumerate(states):
            month_pos = i % 30
            if month_pos not in monthly_patterns:
                monthly_patterns[month_pos] = []
            monthly_patterns[month_pos].append(state)
        
        # 计算每个位置的状态分布
        result = {}
        for pos, pos_states in monthly_patterns.items():
            if len(pos_states) > 0:
                state_dist = Counter(pos_states)
                total = len(pos_states)
                result[f"month_pos_{pos}"] = {state: count/total for state, count in state_dist.items()}
        
        return result
    
    def _analyze_trend(self, states: List[str]) -> str:
        """分析趋势方向"""
        if len(states) < 5:
            return "insufficient_data"
        
        # 将状态转换为数值进行趋势分析
        state_values = {"0:5": 0, "1:4": 1, "2:3": 2, "3:2": 3, "4:1": 4, "5:0": 5}
        values = [state_values.get(state, 2.5) for state in states]
        
        # 计算趋势
        x = np.arange(len(values))
        slope = np.polyfit(x, values, 1)[0]
        
        if slope > 0.1:
            return "increasing"
        elif slope < -0.1:
            return "decreasing"
        else:
            return "stable"
    
    def _calculate_trend_strength(self, states: List[str]) -> float:
        """计算趋势强度"""
        if len(states) < 5:
            return 0.0
        
        state_values = {"0:5": 0, "1:4": 1, "2:3": 2, "3:2": 3, "4:1": 4, "5:0": 5}
        values = [state_values.get(state, 2.5) for state in states]
        
        # 计算相关系数作为趋势强度
        x = np.arange(len(values))
        correlation = np.corrcoef(x, values)[0, 1]
        
        return abs(correlation) if not np.isnan(correlation) else 0.0
    
    def _calculate_entropy(self, states: List[str]) -> float:
        """计算熵"""
        if not states:
            return 0.0
        
        state_counts = Counter(states)
        total = len(states)
        entropy = 0.0
        
        for count in state_counts.values():
            prob = count / total
            if prob > 0:
                entropy -= prob * np.log2(prob)
        
        return entropy
    
    def _calculate_stability(self, states: List[str]) -> float:
        """计算稳定性（1 - 归一化方差）"""
        if len(states) < 2:
            return 1.0
        
        state_values = {"0:5": 0, "1:4": 1, "2:3": 2, "3:2": 3, "4:1": 4, "5:0": 5}
        values = [state_values.get(state, 2.5) for state in states]
        
        variance = np.var(values)
        max_variance = 2.5 ** 2  # 最大可能方差
        
        return 1.0 - (variance / max_variance)
    
    def _calculate_state_correlation(self, states1: List[str], states2: List[str]) -> float:
        """计算两个状态序列的关联度"""
        if len(states1) != len(states2) or len(states1) < 2:
            return 0.0
        
        # 转换为数值
        state_values = {"0:5": 0, "1:4": 1, "2:3": 2, "3:2": 3, "4:1": 4, "5:0": 5}
        values1 = [state_values.get(state, 2.5) for state in states1]
        values2 = [state_values.get(state, 2.5) for state in states2]
        
        correlation = np.corrcoef(values1, values2)[0, 1]
        return correlation if not np.isnan(correlation) else 0.0
    
    def _calculate_numeric_correlation(self, states: List[str], numbers: List[float]) -> float:
        """计算状态序列与数值序列的关联度"""
        if len(states) != len(numbers) or len(states) < 2:
            return 0.0
        
        state_values = {"0:5": 0, "1:4": 1, "2:3": 2, "3:2": 3, "4:1": 4, "5:0": 5}
        state_nums = [state_values.get(state, 2.5) for state in states]
        
        correlation = np.corrcoef(state_nums, numbers)[0, 1]
        return correlation if not np.isnan(correlation) else 0.0
    
    def _calculate_conditional_probabilities(self, states1: List[str], states2: List[str]) -> Dict[str, Dict[str, float]]:
        """计算条件概率 P(state1|state2)"""
        if len(states1) != len(states2):
            return {}
        
        # 统计联合分布
        joint_counts = {}
        state2_counts = Counter(states2)
        
        for s1, s2 in zip(states1, states2):
            if s2 not in joint_counts:
                joint_counts[s2] = {}
            joint_counts[s2][s1] = joint_counts[s2].get(s1, 0) + 1
        
        # 计算条件概率
        conditional_probs = {}
        for s2, s1_counts in joint_counts.items():
            conditional_probs[s2] = {}
            total = state2_counts[s2]
            for s1, count in s1_counts.items():
                conditional_probs[s2][s1] = count / total
        
        return conditional_probs

    def _analyze_reversal_patterns(self, states: List[str]) -> Dict[str, float]:
        """分析反转模式"""
        if len(states) < 3:
            return {}

        reversals = {}
        for i in range(2, len(states)):
            prev_state = states[i-2]
            curr_state = states[i-1]
            next_state = states[i]

            # 检测反转：A -> B -> A
            if prev_state == next_state and prev_state != curr_state:
                pattern = f"{prev_state}->{curr_state}->{next_state}"
                reversals[pattern] = reversals.get(pattern, 0) + 1

        # 归一化
        total = len(states) - 2
        return {pattern: count/total for pattern, count in reversals.items()}

    def _analyze_oscillation_patterns(self, states: List[str]) -> Dict[str, float]:
        """分析震荡模式"""
        if len(states) < 4:
            return {}

        oscillations = {}
        for i in range(3, len(states)):
            pattern = states[i-3:i+1]

            # 检测震荡：A-B-A-B 或 A-B-C-A
            if (pattern[0] == pattern[2] and pattern[1] == pattern[3] and
                pattern[0] != pattern[1]):
                osc_type = "ABAB"
                oscillations[osc_type] = oscillations.get(osc_type, 0) + 1
            elif pattern[0] == pattern[3] and len(set(pattern)) >= 3:
                osc_type = "ABCA"
                oscillations[osc_type] = oscillations.get(osc_type, 0) + 1

        total = len(states) - 3
        return {pattern: count/total for pattern, count in oscillations.items()}

    def _calculate_anomaly_score(self, states: List[str]) -> float:
        """计算异常分数"""
        if len(states) < 10:
            return 0.0

        # 基于频率的异常检测
        state_counts = Counter(states)
        total = len(states)

        # 计算每个状态的期望频率（基于历史数据）
        expected_freqs = {"0:5": 0.017, "1:4": 0.123, "2:3": 0.315,
                         "3:2": 0.369, "4:1": 0.151, "5:0": 0.025}

        anomaly_score = 0.0
        for state, expected_freq in expected_freqs.items():
            actual_freq = state_counts.get(state, 0) / total
            anomaly_score += abs(actual_freq - expected_freq)

        return anomaly_score

    def _calculate_periodicity_strength(self, states: List[str]) -> float:
        """计算周期性强度"""
        if len(states) < 10:
            return 0.0

        max_correlation = 0.0

        # 检测不同周期长度的自相关
        for period in range(2, min(len(states)//2, 20)):
            if len(states) >= 2 * period:
                # 计算周期自相关
                correlation = self._calculate_autocorrelation(states, period)
                max_correlation = max(max_correlation, abs(correlation))

        return max_correlation

    def _calculate_autocorrelation(self, states: List[str], lag: int) -> float:
        """计算自相关"""
        if len(states) < lag + 1:
            return 0.0

        state_values = {"0:5": 0, "1:4": 1, "2:3": 2, "3:2": 3, "4:1": 4, "5:0": 5}
        values = [state_values.get(state, 2.5) for state in states]

        if len(values) < lag + 1:
            return 0.0

        x1 = values[:-lag]
        x2 = values[lag:]

        if len(x1) < 2:
            return 0.0

        correlation = np.corrcoef(x1, x2)[0, 1]
        return correlation if not np.isnan(correlation) else 0.0

    def _build_markov_chain(self, states: List[str], order: int = 1) -> Dict[str, Dict[str, float]]:
        """构建马尔科夫链"""
        if len(states) < order + 1:
            return {}

        transitions = {}

        for i in range(len(states) - order):
            if order == 1:
                current_state = states[i]
                next_state = states[i + 1]
            else:
                current_state = tuple(states[i:i+order])
                next_state = states[i + order]

            if current_state not in transitions:
                transitions[current_state] = {}

            transitions[current_state][next_state] = transitions[current_state].get(next_state, 0) + 1

        # 归一化为概率
        for current_state, next_states in transitions.items():
            total = sum(next_states.values())
            for next_state in next_states:
                transitions[current_state][next_state] /= total

        return transitions

    def _calculate_mutual_information(self, states: List[str]) -> float:
        """计算互信息"""
        if len(states) < 2:
            return 0.0

        # 计算相邻状态的互信息
        current_states = states[:-1]
        next_states = states[1:]

        # 计算联合分布和边际分布
        joint_counts = Counter(zip(current_states, next_states))
        current_counts = Counter(current_states)
        next_counts = Counter(next_states)

        total = len(current_states)
        mutual_info = 0.0

        for (curr, next_state), joint_count in joint_counts.items():
            p_joint = joint_count / total
            p_curr = current_counts[curr] / total
            p_next = next_counts[next_state] / total

            if p_joint > 0 and p_curr > 0 and p_next > 0:
                mutual_info += p_joint * np.log2(p_joint / (p_curr * p_next))

        return mutual_info

    def _calculate_conditional_entropy(self, states: List[str]) -> float:
        """计算条件熵"""
        if len(states) < 2:
            return 0.0

        current_states = states[:-1]
        next_states = states[1:]

        # 计算 H(X_{t+1}|X_t)
        current_counts = Counter(current_states)
        conditional_counts = {}

        for curr, next_state in zip(current_states, next_states):
            if curr not in conditional_counts:
                conditional_counts[curr] = {}
            conditional_counts[curr][next_state] = conditional_counts[curr].get(next_state, 0) + 1

        total = len(current_states)
        conditional_entropy = 0.0

        for curr, next_counts in conditional_counts.items():
            p_curr = current_counts[curr] / total
            curr_total = sum(next_counts.values())

            curr_entropy = 0.0
            for next_state, count in next_counts.items():
                p_next_given_curr = count / curr_total
                if p_next_given_curr > 0:
                    curr_entropy -= p_next_given_curr * np.log2(p_next_given_curr)

            conditional_entropy += p_curr * curr_entropy

        return conditional_entropy

    def _calculate_lz_complexity(self, states: List[str]) -> float:
        """计算Lempel-Ziv复杂度"""
        if not states:
            return 0.0

        # 简化的LZ复杂度计算
        sequence = ''.join([str(hash(state) % 10) for state in states])

        complexity = 0
        i = 0

        while i < len(sequence):
            j = i + 1
            while j <= len(sequence):
                substring = sequence[i:j]
                if substring not in sequence[:i]:
                    complexity += 1
                    i = j
                    break
                j += 1
            else:
                break

        return complexity / len(states) if len(states) > 0 else 0.0

    def _calculate_approximate_entropy(self, states: List[str], m: int = 2, r: float = 0.2) -> float:
        """计算近似熵"""
        if len(states) < m + 1:
            return 0.0

        state_values = {"0:5": 0, "1:4": 1, "2:3": 2, "3:2": 3, "4:1": 4, "5:0": 5}
        values = np.array([state_values.get(state, 2.5) for state in states])

        N = len(values)

        def _maxdist(xi, xj, m):
            return max([abs(ua - va) for ua, va in zip(xi, xj)])

        def _phi(m):
            patterns = np.array([values[i:i+m] for i in range(N - m + 1)])
            C = np.zeros(N - m + 1)

            for i in range(N - m + 1):
                template = patterns[i]
                for j in range(N - m + 1):
                    if _maxdist(template, patterns[j], m) <= r:
                        C[i] += 1

            phi = np.mean(np.log(C / (N - m + 1)))
            return phi

        try:
            return _phi(m) - _phi(m + 1)
        except:
            return 0.0
